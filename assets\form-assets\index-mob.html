<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link href="font-awesome.min.css" type="text/css" rel="stylesheet">


    <!-- <link href="responsive-grid.min.css" type="text/css" rel="stylesheet"> -->


    <!-- <link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css'>
    <link rel='stylesheet' href='https://cdn.form.io/formiojs/formio.full.min.css'>
    <script src='https://cdn.form.io/formiojs/formio.full.min.js'></script> -->

    <!-- <link href="formio.full.min.css" type="text/css" rel="stylesheet">
    <link href="semantic.min.css" type="text/css" rel="stylesheet">
    <script src='semantic.js'></script>

    <link href="jquery-confirm.min.css" type="text/css" rel="stylesheet"> -->
    <script src='formio.full.min.js'></script>

    <script src="unvired-forms-sdk.js"></script>
    <script>
        // Verify SDK is loaded
        console.log('SDK loaded check:', typeof window.loadUnviredForms);
        if (typeof window.loadUnviredForms !== 'function') {
            // console.error('unvired-forms-sdk.js did not load properly!');
        }
    </script>
    <link href="custom.css" type="text/css" rel="stylesheet">
    <script src='formio.js'></script>
    <script src='eam.js'></script>
    <!-- <script src='signature_pad.min.js'></script>
    <script src="Signature.js"></script> -->



    <!-- <script src='formio.full.min.js'></script> -->
    <!-- <script src='formio.js'></script>
    <script src='eam.js'></script>
    <script src='barcode.js'></script>
    <script src='select-barcode.js'></script>
    <script src='jquery.min.js'></script>
    <script src="https://cdn.jsdelivr.net/npm/resize-observer-polyfill@1.5.1/dist/ResizeObserver.global.js"></script>
    <script src='signature_pad.min.js'></script>
    <script src='jquery-confirm.min.js'></script>

    <script src='smart-button.js'></script> 
    <script src='smart-id.js'></script>
    <script src='SmartData.js'></script>
    <script src='Choices.js'></script>
    <script src='smart-select.js'></script> 

    <script type="text/javascript" src="eventemitter3.min.js"></script>
    <script src="custom-panel.js"></script>
    <script src="Signature.js"></script>
    <script type="module" src="nested-component.js"></script>
    <script src="cropo.js"></script>
    <script src="markerjs2.js"></script>
    <script type="text/javascript" src="browser-image-compression.js"></script>
    <script src="SmartStorageAdaptor.js"></script>
    <script type="text/javascript" src="uuid.min.js"></script>
    <script src='smart-file.js'></script>  -->
    <!-- <script src='i18n/CLDRPluralRuleParser.js'></script>
    <script src='i18n/jquery.i18n.min.js'></script>
    <script src='i18n/jquery.i18n.messagestore.min.js'></script>
    <script src='i18n/jquery.i18n.fallbacks.min.js'></script>
    <script src='i18n/jquery.i18n.language.min.js'></script>
    <script src='i18n/jquery.i18n.parser.min.js'></script>
    <script src='i18n/jquery.i18n.emitter.min.js'></script> -->

    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.i18n/1.0.7/jquery.i18n.emitter.bidi.min.js"
            integrity="sha512-dWOh8gVtmoq3jnJMksc/K9sgWuSkRc61EbqofuiLsUeYjkZeelYM7mRHQ9+B9ggZhXtz6u9IFR98v6QpZfxuRg=="
            crossorigin="anonymous"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/dojo/1.16.3/global.js"></script> -->

</head>

<style>
    .formio-wrapper {
        --form-wrapper-padding: 5px !important;
    }
</style>

<body style="background-color: #f4f5f8">
    <!-- Custom header toolbar -->
    <div class="toolbarHeader">
        <div class="toolbarWrapper">
            <div class="backButton" onclick="checkDataAndGoBack()">
                <i class="fa fa-chevron-left" id="backBtnIcon"></i>
            </div>
            <div class="formName">
                <p id="formLabel">Loading...</p>
            </div>

        </div>
    </div>

    <!-- <div class="contentWrapper">
    <div class="containerBody">
        <div id='formIOContainer'></div>
    </div>
</div> -->
    <div class="formio-wrapper">
        <div id='formIOContainer'></div>
    </div>

</body>

</html>