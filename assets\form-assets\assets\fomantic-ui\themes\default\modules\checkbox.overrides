/*******************************
         Theme Overrides
*******************************/

@font-face {
    font-family: Checkbox;
    src: url("data:application/font-woff;base64,d09GRgABAAAAAAR8AA0AAAAABtwAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAEYAAAABoAAAAcgbCWJ0dERUYAAARIAAAAFwAAABgAJQAAT1MvMgAAAZgAAABAAAAAYA8SBD9jbWFwAAAB9AAAAFMAAAFc71zo6Wdhc3AAAARAAAAACAAAAAgAAAAQZ2x5ZgAAAlwAAADJAAABVHshwGBoZWFkAAABMAAAAC4AAAA2Bj3tWWhoZWEAAAFgAAAAHQAAACQHfQPIaG10eAAAAdgAAAAZAAAAHg56AEVsb2NhAAACSAAAABIAAAASAQoAjG1heHAAAAGAAAAAGAAAACAACgAvbmFtZQAAAygAAADnAAABp5xOC4Fwb3N0AAAEEAAAADAAAABaHRtWW3icY2BkYGBgYmDgqbAVjee3+crAzcIAAhc3XilHppl3M+cBKQ6QWgYGAAHnCVsAAHicY2BkYGA+8P8AAwMLAwgw72ZgZEAF7ABaJgNOAAAAeJxjYGRgYOBg0GUA0QwMTAxoAAAFtQA6eJxjYGYWYpzAwMrAwDST6QwDA0M/hGZ8zWDMyMmAChgF0AQYHBgYXjAxH/h/gMGB+QCIx8CIJKvAwAgAIOcKW3icY2GAAMZQCM0ExCwMDK7MqiAeAAqXAM4AAAB4nN2NwQ2AMAwDL00pHYJH52AQ9n/w6BbFCS9WwJFlO3IUwHl5YAQuJctcOaVdU6TGmGWt7IxJuHXnvmcrnLPR2GWb6NnF4rrwhT5U/o0Hf5EJGAAAAAAAAAgACAAQABgASABsAKoAAHichY09CsJAEIVndiG2gyQ7IASJIbHQyvyIiFhZpBE8iNsIeiVvYOFx7FPbKE6yUcHGgZ23jzd8DxDeowAWCv77Dez0Vd1hAIB+D714NMa0yNdYZrMhum0CfaWnT4bpeSPaEjOd8djIltTBNBlhSGzES3Jqb5oW6UOrE1WBL7/A73lxNBqnK8znZRbNWMgJET8qJhlWF1HV3zvXSceBSifawrTjTLrd0uI3c4lFu4UcYvbh1/KsIaodUayx7Kxr+wb8Y+EFi+YsngAAAHicfc5BSsNAGMXxf9q0agURFy50M3shlOoNikIXddGFa5N0SEvLDKQp2KUH8AxuXXkkz+JL+NYJJPzmffNmAlzxS0L7JJxzYx5wxoN5qPzZnMrv5hGXfJrHyr/NE+74UytJL5Rcdye0Huiue/NQ+aM5lV/NI27ZmsfKv8wTnvhhzgZPyY6CyAfMN77cFVFaaVBxZE9OraWvjvtc6Kn0jN40qDnoVyIBx4yMqWJfH7YxuFk27a2/dLVGxUpbQndYrrVnrazgpO9C1chSb7s3UymGxlU++Dpv/NoVJ7co4zLGkPEPj0VARQB4nGNgYsAPOBgYGJkYmBiZGZgZWRhZGdnYS/MyDYAAShuCaFcLCB9Iw/hGAOPdCuMAAQAB//8AD3icY2BkYGDgAWIBIGYCYhYIDQACOwAmAHicY2BgYGQAgns+by1A9MWNV8phNABQ5wfrAAA=") format("woff");
}

/* Checkmark */
.ui.checkbox label::after {
    font-family: Checkbox;
}

/* Checked */
.ui.checkbox input:checked ~ label::after {
    content: "\e800";
}

/* Indeterminate */
.ui.checkbox input:indeterminate ~ label::after {
    font-size: 12px;
    content: "\e801";
}

/* UTF Reference
.check::before { content: '\e800'; }
.dash::before { content: '\e801'; }
.plus::before { content: '\e802'; }
*/
