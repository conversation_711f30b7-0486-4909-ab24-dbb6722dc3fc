/*!
 * # Fomantic-UI - Modal
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "modal";

@import (multiple) "../../theme.config";

/*******************************
             Modal
*******************************/

.ui.modal {
    position: absolute;
    display: none;
    z-index: @zIndex;
    text-align: left;
    background: @background;
    border: @border;
    box-shadow: @boxShadow;
    transform-origin: @transformOrigin;
    flex: 0 0 auto;
    border-radius: @borderRadius;
    user-select: text;
    will-change: top, left, margin, transform, opacity;
    outline: none;
}

.ui.modal > :first-child:not(.close):not(.dimmer),
.ui.modal > i.icon:first-child + *,
.ui.modal > .dimmer:first-child + *:not(.close),
.ui.modal > .dimmer:first-child + i.icon + * {
    border-top-left-radius: @borderRadius;
    border-top-right-radius: @borderRadius;
}

.ui.modal > :last-child {
    border-bottom-left-radius: @borderRadius;
    border-bottom-right-radius: @borderRadius;
}

.ui.modal > .ui.dimmer {
    border-radius: inherit;
}

/*******************************
            Content
*******************************/

/* --------------
     Close
--------------- */

.ui.modal > .close {
    cursor: pointer;
    position: absolute;
    top: @closeTop;
    right: @closeRight;
    z-index: 1;
    opacity: @closeOpacity;
    font-size: @closeSize;
    color: @closeColor;
    width: @closeHitbox;
    height: @closeHitbox;
    padding: @closePadding;
}
.ui.modal > .close:focus,
.ui.modal > .close:hover {
    opacity: 1;
    outline: none;
}

/* --------------
     Header
--------------- */

.ui.modal > .header {
    display: block;
    font-family: @headerFontFamily;
    background: @headerBackground;
    margin: @headerMargin;
    padding: @headerPadding;
    box-shadow: @headerBoxShadow;
    color: @headerColor;
    border-bottom: @headerBorder;
}
.ui.modal > .header:not(.ui) {
    font-size: @headerFontSize;
    line-height: @headerLineHeight;
    font-weight: @headerFontWeight;
}

/* --------------
     Content
--------------- */

.ui.modal > .content {
    display: block;
    width: 100%;
    font-size: @contentFontSize;
    line-height: @contentLineHeight;
    padding: @contentPadding;
    background: @contentBackground;
}
.ui.modal > .image.content {
    display: flex;
    flex-direction: row;
}

/* Image */
.ui.modal > .content > .image {
    display: block;
    flex: 0 1 auto;
    width: @imageWidth;
    align-self: @imageVerticalAlign;
    max-width: 100%;
}
.ui.modal > [class*="top aligned"] {
    align-self: start;
}
.ui.modal > [class*="middle aligned"] {
    align-self: center;
}
.ui.modal > [class*="stretched"] {
    align-self: stretch;
}

/* Description */
.ui.modal > .content > .description {
    display: block;
    flex: 1 0 auto;
    min-width: 0;
    align-self: @descriptionVerticalAlign;
}

.ui.modal > .content > i.icon + .description,
.ui.modal > .content > .image + .description {
    flex: 0 1 auto;
    min-width: @descriptionMinWidth;
    width: @descriptionWidth;
    padding-left: @descriptionDistance;
}

/* rtl:ignore */
.ui.modal > .content > .image > i.icon {
    margin: 0;
    opacity: 1;
    width: auto;
    line-height: 1;
    font-size: @imageIconSize;
}

& when (@variationModalActions) {
    /* --------------
         Actions
    --------------- */

    .ui.modal > .actions {
        background: @actionBackground;
        padding: @actionPadding;
        border-top: @actionBorder;
        text-align: @actionAlign;
    }
    .ui.modal .actions > .button:not(.fluid) {
        margin-left: @buttonDistance;
    }
    .ui.ui.modal > .basic.actions,
    .ui.basic.modal > .actions {
        border-top: none;
    }
    & when (@variationModalLeftActions) {
        .ui.modal > .left.actions {
            text-align: left;
            & > .button:not(.fluid) {
                margin-left: @buttonLeftDistance;
                margin-right: @buttonLeftDistance;
            }
        }
    }
}

& when (@variationModalCentered) {
    .ui.modal > .centered,
    .ui.modal > .center.aligned {
        text-align: center;
        &.actions > .button:not(.fluid) when (@variationModalActions) {
            margin-left: @buttonCenteredDistance;
            margin-right: @buttonCenteredDistance;
        }
    }
}

/* -------------------
       Responsive
-------------------- */

/* Modal Width */
@media only screen and (max-width: @largestMobileScreen) {
    .ui.modal:not(.fullscreen) {
        width: @mobileWidth;
        margin: @mobileMargin;
    }
}
@media only screen and (min-width: @tabletBreakpoint) {
    .ui.modal:not(.fullscreen) {
        width: @tabletWidth;
        margin: @tabletMargin;
    }
}
@media only screen and (min-width: @computerBreakpoint) {
    .ui.modal:not(.fullscreen) {
        width: @computerWidth;
        margin: @computerMargin;
        & > .active.dimmer + .close:not(.inside) {
            pointer-events: none;
            opacity: @closeOpacityDimmed;
        }
    }
    .ui.dimmer > .ui.modal:not(.fullscreen) > .close:not(.inside) {
        text-shadow: @closeShadow;
    }
    .ui.inverted.dimmer > .ui.modal:not(.fullscreen) > .close:not(.inside) {
        text-shadow: @invertedCloseShadow;
    }
}
@media only screen and (min-width: @largeMonitorBreakpoint) {
    .ui.modal:not(.fullscreen) {
        width: @largeMonitorWidth;
        margin: @largeMonitorMargin;
    }
}
@media only screen and (min-width: @widescreenMonitorBreakpoint) {
    .ui.modal:not(.fullscreen) {
        width: @widescreenMonitorWidth;
        margin: @widescreenMonitorMargin;
    }
}

/* Tablet and Mobile */
@media only screen and (max-width: @largestTabletScreen) {
    .ui.modal > .close + .header {
        padding-right: @closeHitbox;
    }
    .ui.modal > .close {
        top: @innerCloseTop;
        right: @innerCloseRight;
        color: @innerCloseColor;
    }
}

/* Mobile */
@media only screen and (max-width: @largestMobileScreen) {
    .ui.modal > .header {
        padding: @mobileHeaderPadding;
    }
    .ui.modal > .close + .header {
        padding-right: @closeHitbox;
    }
    .ui.overlay.fullscreen.modal > .content.content.content {
        min-height: @overlayFullscreenScrollingContentMaxHeightMobile;
    }
    .ui.overlay.fullscreen.modal > .scrolling.content.content.content {
        max-height: @overlayFullscreenScrollingContentMaxHeightMobile;
    }
    .ui.modal > .content {
        display: block;
        padding: @mobileContentPadding !important;
    }
    .ui.modal > .close {
        top: @mobileCloseTop !important;
        right: @mobileCloseRight !important;
    }

    /* rtl:ignore */
    .ui.modal .image.content {
        flex-direction: column;
    }
    .ui.modal > .content > .image {
        display: block;
        max-width: 100%;
        margin: 0 auto !important;
        text-align: center;
        padding: @mobileImagePadding !important;
    }
    .ui.modal > .content > .image > i.icon {
        font-size: @mobileImageIconSize;
        text-align: center;
    }

    /* rtl:ignore */
    .ui.modal > .content > .description {
        display: block;
        width: 100% !important;
        margin: 0 !important;
        padding: @mobileDescriptionPadding !important;
        box-shadow: none;
    }
    & when (@variationModalActions) {
        /* Let Buttons Stack */
        .ui.modal > .actions {
            padding: @mobileActionPadding !important;
        }
        .ui.modal .actions > .buttons,
        .ui.modal .actions > .button {
            margin-bottom: @mobileButtonDistance;
        }
    }
}

/* --------------
    Coupling
--------------- */

.ui.inverted.dimmer > .ui.modal {
    box-shadow: @invertedBoxShadow;
}

/*******************************
             Types
*******************************/
& when (@variationModalBasic) {
    .ui.basic.modal {
        background-color: transparent;
        border: none;
        border-radius: 0;
        box-shadow: none !important;
        color: @basicModalColor;
    }
    .ui.ui.modal > .basic.header,
    .ui.ui.modal > .basic.actions,
    .ui.basic.modal > .header,
    .ui.basic.modal > .content,
    .ui.basic.modal > .actions {
        background-color: transparent;
    }
    .ui.modal > .basic.header {
        border-bottom: none;
    }
    .ui.basic.modal > .header {
        color: @basicModalHeaderColor;
        border-bottom: none;
    }
    .ui.basic.modal > .close {
        top: @basicModalCloseTop;
        right: @basicModalCloseRight;
        color: @basicInnerCloseColor;
    }
    .ui.inverted.dimmer > .basic.modal {
        color: @basicInvertedModalColor;
    }
    .ui.inverted.dimmer > .ui.basic.modal > .header {
        color: @basicInvertedModalHeaderColor;
    }
}

& when (@variationModalLegacy) {
    /* Resort to margin positioning if legacy */
    .ui.legacy.legacy.modal,
    .ui.legacy.legacy.page.dimmer > .ui.modal {
        left: 50% !important;
    }
    .ui.legacy.legacy.modal:not(.aligned),
    .ui.legacy.legacy.page.dimmer > .ui.modal:not(.aligned) {
        top: 50%;
    }
    .ui.legacy.legacy.page.dimmer > .ui.scrolling.modal:not(.aligned),
    .ui.page.dimmer > .ui.scrolling.legacy.legacy.modal:not(.aligned),
    .ui.top.aligned.legacy.legacy.page.dimmer > .ui.modal:not(.aligned),
    .ui.top.aligned.dimmer > .ui.legacy.legacy.modal:not(.aligned) {
        top: auto;
    }
    & when (@variationModalOverlay) {
        .ui.legacy.overlay.fullscreen.modal {
            margin-top: -@scrollingMargin !important;
        }
    }
}

/*******************************
             States
*******************************/

.ui.loading.modal {
    display: block;
    visibility: hidden;
    z-index: @loadingZIndex;
}

.ui.active.modal {
    display: block;
}

/*******************************
           Variations
*******************************/

& when (@variationModalAligned) {
    /* --------------
       Aligned
    --------------- */

    .modals.dimmer .ui.top.aligned.modal {
        top: @topAlignedMargin;
    }
    .modals.dimmer .ui.bottom.aligned.modal {
        bottom: @bottomAlignedMargin;
    }

    @media only screen and (max-width: @largestMobileScreen) {
        .modals.dimmer .ui.top.aligned.modal {
            top: @mobileTopAlignedMargin;
        }
        .modals.dimmer .ui.bottom.aligned.modal {
            bottom: @mobileBottomAlignedMargin;
        }
    }
}

& when (@variationModalScrolling) {
    /* --------------
        Scrolling
    --------------- */

    /* Scrolling Dimmer */
    .scrolling.dimmable.dimmed {
        overflow: hidden;
    }
    .scrolling.dimmable > .dimmer {
        justify-content: center;
        position: fixed;
    }
    .scrolling.dimmable:not(body) > .dimmer {
        position: absolute;
    }
    .scrolling.dimmable.dimmed > .dimmer {
        overflow: auto;
        overscroll-behavior: @overscrollBehavior;
    }
    .modals.dimmer .ui.scrolling.modal.fullscreen {
        top: 0;
    }
    .modals.dimmer .ui.scrolling.modal:not(.fullscreen) {
        margin: @scrollingMargin auto;
        top: @scrollingTop;
    }

    /* Fix for Firefox, Edge, IE11 */
    .modals.dimmer .ui.scrolling.modal:not([class*="overlay fullscreen"])::after {
        content: "\00A0";
        position: absolute;
        height: @scrollingMargin;
    }

    /* Undetached Scrolling */
    .scrolling.undetached.dimmable.dimmed {
        overflow: auto;
        overscroll-behavior: @overscrollBehavior;
    }
    .scrolling.undetached.dimmable.dimmed > .dimmer {
        overflow: hidden;
    }
    .scrolling.undetached.dimmable .ui.scrolling.modal:not(.fullscreen) {
        position: absolute;
        left: 50%;
    }

    /* Scrolling Content */
    .ui.modal > .scrolling.content {
        max-height: @scrollingContentMaxHeight;
        overflow: auto;
        overscroll-behavior: @overscrollBehavior;
    }
    & when (@variationTableResizable) {
        .ui.modal > .resizable.scrolling.content {
            resize: @resizableDirection;
            max-height: none;
        }
    }
}

& when (@variationModalOverlay) {
    .ui.overlay.fullscreen.modal > .content {
        min-height: @overlayFullscreenScrollingContentMaxHeight;
    }
    .ui.overlay.fullscreen.modal > .scrolling.content {
        max-height: @overlayFullscreenScrollingContentMaxHeight;
    }
}

& when (@variationModalFullscreen) or (@variationModalOverlay) or (@variationModalCloseInside) {
    /* --------------
       Full Screen
    --------------- */

    .ui.fullscreen.modal {
        width: @fullScreenWidth;
        left: @fullScreenOffset;
        margin: @fullScreenMargin;
    }
    & when (@variationModalOverlay) {
        .ui.overlay.fullscreen.modal {
            width: 100%;
            left: 0;
            margin: 0 auto;
            top: 0;
            border-radius: 0;
        }
    }
    .ui.modal > .close.inside + .header:not(.centered):not(.center):not(.icon),
    .ui.fullscreen.modal > .close + .header:not(.centered):not(.center):not(.icon) {
        padding-right: @closeHitbox;
    }
    .ui.modal > .close.inside,
    .ui.fullscreen.modal > .close {
        top: @innerCloseTop;
        right: @innerCloseRight;
        color: @innerCloseColor;
    }
    & when (@variationModalBasic) {
        .ui.basic.fullscreen.modal > .close {
            color: @basicInnerCloseColor;
        }
    }
}

/* --------------
      Size
--------------- */

.ui.modal {
    font-size: @medium;
}
& when not (@variationModalSizes = false) {
    each(@variationModalSizes, {
        @hs: @{value}HeaderSize;
        @mw: @{value}MobileWidth;
        @mm: @{value}MobileMargin;
        @tw: @{value}TabletWidth;
        @tm: @{value}TabletMargin;
        @cw: @{value}ComputerWidth;
        @cm: @{value}ComputerMargin;
        @lw: @{value}LargeMonitorWidth;
        @lm: @{value}LargeMonitorMargin;
        @ww: @{value}WidescreenMonitorWidth;
        @wm: @{value}WidescreenMonitorMargin;
        .ui.@{value}.modal > .header:not(.ui) {
            font-size: @@hs;
        }
        @media only screen and (max-width: @largestMobileScreen) {
            .ui.@{value}.modal {
                width: @@mw;
                margin: @@mm;
            }
        }
        @media only screen and (min-width: @tabletBreakpoint) {
            .ui.@{value}.modal {
                width: @@tw;
                margin: @@tm;
            }
        }
        @media only screen and (min-width: @computerBreakpoint) {
            .ui.@{value}.modal {
                width: @@cw;
                margin: @@cm;
            }
        }
        @media only screen and (min-width: @largeMonitorBreakpoint) {
            .ui.@{value}.modal {
                width: @@lw;
                margin: @@lm;
            }
        }
        @media only screen and (min-width: @widescreenMonitorBreakpoint) {
            .ui.@{value}.modal {
                width: @@ww;
                margin: @@wm;
            }
        }
    });
}

& when (@variationModalInverted) {
    /*****************************
              Inverted
    *******************************/

    .ui.inverted.modal {
        background: @invertedBackground;
    }

    .ui.inverted.modal > .header,
    .ui.inverted.modal > .content {
        background: @invertedBackground;
        color: @invertedHeaderColor;
    }
    & when (@variationModalActions) {
        .ui.inverted.modal > .actions {
            background: @invertedActionBackground;
            border-top: @invertedActionBorder;
            color: @invertedActionColor;
        }
    }

    .ui.inverted.dimmer > .modal > .close {
        color: @invertedDimmerCloseColor;
    }

    @media only screen and (max-width: @largestTabletScreen) {
        .ui.dimmer .inverted.modal > .close {
            color: @invertedCloseColor;
        }
    }
    & when (@variationModalFullscreen) or (@variationModalCloseInside) {
        .ui.inverted.modal > .close.inside,
        .ui.inverted.fullscreen.modal > .close {
            color: @invertedCloseColor;
        }
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
