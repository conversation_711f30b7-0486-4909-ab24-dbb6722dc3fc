/*!
 * # Fomantic-UI - Divider
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "divider";

@import (multiple) "../../theme.config";

/*******************************
            Divider
*******************************/

.ui.divider {
    margin: @margin;
    line-height: 1;
    height: 0;
    font-weight: @fontWeight;
    text-transform: @textTransform;
    letter-spacing: @letterSpacing;
    color: @color;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* --------------
      Basic
--------------- */

.ui.divider:not(.vertical):not(.horizontal) {
    border-top: @shadowWidth @borderStyle @shadowColor;
    border-bottom: @highlightWidth @borderStyle @highlightColor;
}

/* --------------
    Coupling
--------------- */

/* Allow divider between each column row */
.ui.grid > .column + .divider,
.ui.grid > .row > .column + .divider {
    left: auto;
}

& when (@variationDividerHorizontal) {
    /* --------------
       Horizontal
    --------------- */

    .ui.horizontal.divider {
        display: table;
        white-space: nowrap;
        height: auto;
        margin: @horizontalMargin;
        line-height: 1;
        text-align: center;
    }

    .ui.horizontal.divider::before,
    .ui.horizontal.divider::after {
        content: "";
        display: table-cell;
        position: relative;
        top: 50%;
        width: 50%;
        background-repeat: no-repeat;
    }

    .ui.horizontal.divider::before {
        background-position: right @horizontalDividerMargin top 50%;
    }
    .ui.horizontal.divider::after {
        background-position: left @horizontalDividerMargin top 50%;
    }
}

& when (@variationDividerVertical) {
    /* --------------
        Vertical
    --------------- */

    .ui.vertical.divider {
        position: absolute;
        z-index: 2;
        top: 50%;
        left: 50%;
        margin: 0;
        padding: 0;
        width: auto;
        height: 50%;
        line-height: 0;
        text-align: center;
        transform: translateX(-50%);
    }

    .ui.vertical.divider::before,
    .ui.vertical.divider::after {
        position: absolute;
        left: 50%;
        content: "";
        z-index: 3;
        border-left: @shadowWidth @borderStyle @shadowColor;
        border-right: @highlightWidth @borderStyle @highlightColor;
        width: 0;
        height: @verticalDividerHeight;
    }

    .ui.vertical.divider::before {
        top: -100%;
    }
    .ui.vertical.divider::after {
        top: auto;
        bottom: 0;
    }

    /* Inside grid */
    @media only screen and (max-width: @largestMobileScreen) {
        .ui.stackable.grid .ui.vertical.divider,
        .ui.grid .stackable.row .ui.vertical.divider {
            display: table;
            white-space: nowrap;
            height: auto;
            margin: @horizontalMargin;
            overflow: hidden;
            line-height: 1;
            text-align: center;
            position: static;
            top: 0;
            left: 0;
            transform: none;
        }

        .ui.stackable.grid .ui.vertical.divider::before,
        .ui.grid .stackable.row .ui.vertical.divider::before,
        .ui.stackable.grid .ui.vertical.divider::after,
        .ui.grid .stackable.row .ui.vertical.divider::after {
            left: 0;
            border-left: none;
            border-right: none;
            content: "";
            display: table-cell;
            position: relative;
            top: 50%;
            width: 50%;
            background-repeat: no-repeat;
        }

        .ui.stackable.grid .ui.vertical.divider::before,
        .ui.grid .stackable.row .ui.vertical.divider::before {
            background-position: right @horizontalDividerMargin top 50%;
        }
        .ui.stackable.grid .ui.vertical.divider::after,
        .ui.grid .stackable.row .ui.vertical.divider::after {
            background-position: left @horizontalDividerMargin top 50%;
        }
    }
}

& when (@variationDividerIcon) {
    /* --------------
          Icon
    --------------- */

    .ui.divider > .icon {
        margin: @dividerIconMargin;
        font-size: @dividerIconSize;
        height: 1em;
        vertical-align: middle;
    }
}

& when (@variationDividerHorizontal) {
    /* --------------
         Header
    --------------- */
    .ui.horizontal.divider[class*="left aligned"] {
        &::before {
            display: none;
        }
        &::after {
            width: 100%;
        }
    }
    .ui.horizontal.divider[class*="right aligned"] {
        &::before {
            width: 100%;
        }
        &::after {
            display: none;
        }
    }
}

/*******************************
          Variations
*******************************/

& when (@variationDividerHidden) {
    /* --------------
        Hidden
    --------------- */

    .ui.hidden.divider {
        border-color: transparent !important;
    }

    .ui.hidden.divider::before,
    .ui.hidden.divider::after {
        display: none;
    }
}

/* --------------
    Inverted
--------------- */
& when (@variationDividerInverted) {
    .ui.divider.inverted,
    .ui.vertical.inverted.divider,
    .ui.horizontal.inverted.divider {
        color: @invertedTextColor;
    }
    .ui.divider.inverted,
    .ui.divider.inverted::after,
    .ui.divider.inverted::before {
        border-color: @invertedShadowColor @invertedHighlightColor @invertedHighlightColor @invertedShadowColor !important;
    }
}

/* --------------
    Fitted
--------------- */
& when (@variationDividerFitted) {
    .ui.fitted.divider {
        margin: 0;
    }
}

& when (@variationDividerClearing) {
    /* --------------
        Clearing
    --------------- */

    .ui.clearing.divider {
        clear: both;
    }
}

& when (@variationDividerSection) {
    /* --------------
        Section
    --------------- */

    .ui.section.divider {
        margin-top: @sectionMargin;
        margin-bottom: @sectionMargin;
    }
}

/* --------------
     Sizes
--------------- */

.ui.divider {
    font-size: @medium;
}
& when not (@variationDividerSizes = false) {
    each(@variationDividerSizes, {
        @s: @@value;
        .ui.@{value}.divider {
            font-size: @s;
        }
    });
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
