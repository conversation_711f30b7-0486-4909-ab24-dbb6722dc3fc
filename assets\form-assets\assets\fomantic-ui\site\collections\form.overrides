/*******************************
         Site Overrides
*******************************/
// @floatlabelColor: red;

.ui.form .field {
	&:last-child {
		margin-bottom: 0;
	}

	&:has(+ h4) {
		margin-bottom: 0;
	}
}

.ui.checkbox label {
	font-weight: 500;
}

.label {
	display: block;
	font-size: @labelFontSize;
	color: @labelColor;
	// @color: @grey;
	font-weight: 500;
	text-transform: @labelTextTransform;
	margin: @labelMargin;
}

.ui.labeled.input .icon {
	color: @secondaryTextColor;
	vertical-align: sub;
}

.un-custom-label {
	position: relative;

	select.ui.dropdown {
		appearance: none;
		padding-right: 24px;

		&.error {
			border-color: @formErrorBorder !important;
		}
	}

	&::after {
		content: '\f107';
		font-family: 'Icons';
		font-size: 14px;
		position: absolute;
		top: 12px;
		right: 14px;
		color: @secondaryTextColor;
	}
}

.ui.pointing.label::before {
	display: none;
}

.ui.form .fields {
	&:has(+ [ref='messageContainer']:empty),
	&:has(+ [ref='messageContainer'] > .ui.label.pointing) {
		margin-bottom: 0 !important;
	}
}

// Mutli-Select
.ui.form input.choices__input {
	padding: 2px 0;
	min-width: 50px !important;
	line-height: 1.5;
	border: 0;
}

// Single-Select
.ui.form .choices[data-type*='select-one'] input.choices__input {
	font-size: 14px;
	padding: 8px 10px;
}

.formio-choices.choices[data-type*='select-one']:after {
	content: '\f107';
	font-family: 'Icons';
	font-size: 16px;
	color: @secondaryTextColor;
	border: 0;
	right: 10px;
	margin-top: -8px;
	width: 12px;
	height: 12px;
}

.formio-choices[data-type='select-multiple'] {
	position: relative;

	.ui.selection.dropdown {
		padding-top: 12px;
		padding-bottom: 6px;
	}

	.form-control {
		&:after {
			position: absolute;
			content: '\f107';
			font-family: 'Icons';
			font-size: 16px;
			color: @secondaryTextColor;
			right: 15px;
			top: 14px;
			width: 12px;
			height: 12px;
		}
	}
}

.formio-select-autocomplete-input {
	padding: 0 !important;
}

/* -----------------
	required options related custom code start
----------------- */

.ui.checkbox {
	&::after {
		display: none;
	}

	input + label.field-required {
		span::after {
			content: '*';
			display: inline-block;
			color: @requiredColor;
			margin: -0.2em 0 0 0.2em;
		}
	}
}

.formio-component-label-hidden {
	&:has(> label.field-required.control-label--hidden) {
		// .ui.checkbox {
		// 	&::after {
		// 		display: none;
		// 	}

		// 	label span::before {
		// 		content: '*';
		// 		display: inline-block;
		// 		color: @requiredColor;
		// 		margin: -0.2em 0.2em 0 0;
		// 	}
		// }

		label {
			&.control-label--hidden {
				display: none;
			}
		}

		// // checkbox
		// .ui.checkbox:not(.un-survey-checkbox) {
		// 	border-color: @red !important;
		// }

		// // file upload
		// .un-file-selector.fileSelector {
		// 	border-color: @red !important;
		// }

		// // dropdown
		// .ui.selection.dropdown {
		// 	border-color: @red !important;
		// }

		// // input fields
		// .ui.input {
		// 	.form-control {
		// 		border-color: @red !important;
		// 	}
		// }
	}
}

.formio-component-signature {
	.on-input-field {
		display: none;
		margin: 0 !important;
	}
}

.formio-component-day {
	.on-input-field,
	.on-select-field {
		display: none;
		margin: 0 !important;
	}
}

.ui.form .inline.fields {
	flex-wrap: wrap;
	row-gap: 8px;

	@media only screen and (max-width: @largestMobileScreen) {
		row-gap: 0;
	}
}

.form-required-text-field {
	font-size: unit((14 / 14), rem);
	font-weight: 400;
	color: @lightBrown;
	line-height: 1;
	margin: 0.42857143em 0 0 0 !important;

	&.on-radio-field {
		margin: -14px 0 0 0 !important;
	}
}

/* hide required text on error state */
.ui.input.fluid:has(> .error) + .form-required-text-field {
	display: none;
}

.form-required-text-field:has(+ * > .pointing) {
	display: none;
}

[ref='element']:has(+ * > .pointing) .form-required-text-field {
	display: none;
}

.ui.fluid.input:has(input.error) + .form-required-text-field {
	display: none;
}

/* -----------------
	required options related custom code end
----------------- */

// equal width logic
.ui.form .grouped.fields {
	display: inline-flex;
	flex-direction: column;
	.field {
		display: inline-flex;
		.ui.checkbox {
			flex: 1 1 0;
		}
	}
}

/* -----------------
		Floating label start
-------------------- */
.floating-label-wrapper {
	position: relative;

	.floating-label {
		position: absolute;
		top: 0.65rem;
		left: 1.14285714em;
		max-width: calc(100% - 1.14285714em - 1px);
		min-width: 45%;
		background-color: @white;
		color: @labelColor;
		font-size: 0.92857143rem;
		font-weight: 500;
		text-transform: none;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		padding: 0.2rem 0.4rem;
		pointer-events: none;
		transition-duration: 0.2s;
		transition-property: top, left;
		z-index: 99;

		&.floating-label-is-hidden-dp {
			display: none;
			// @media only screen and (max-width: @largestMobileScreen) {
			// 	display: block;
			// }
		}

		@media only screen and (max-width: @largestMobileScreen) {
			top: 0.85rem;
			min-width: 30%;
		}

		&.floating-label-required::after {
			margin: -0.2em 0 0 0.2em;
			content: '*';
			color: @requiredColor;
			display: inline-block;
			vertical-align: top;
		}
	}
}

.floating-label-wrapper:not(.field-has-value):has(input[disabled]) .floating-label {
	// background-color: rgba(@lightGrey, 0.3);
	background-color: transparent;
}

// .floating-label-wrapper input:not(:placeholder-shown) ~ label ,
.floating-label-wrapper.field-has-value > .floating-label,
// .floating-label-wrapper input[aria-invalid='false'] ~ .floating-label,
.floating-label-wrapper .form-control:focus ~ .floating-label,
.floating-label-wrapper:has(.is-focused) > .floating-label,
.floating-label-wrapper:has(input:focus)  .floating-label,
.floating-label-wrapper:has(select:focus)  .floating-label,
.floating-label-wrapper:has(select:focus)  .floating-label,
.floating-label-wrapper:has(textarea:focus)  .floating-label {
	top: -0.8rem;
	left: 0.8rem;
	min-width: auto;
	font-size: @relativeSmall;
	background-color: @white;
	padding: 0 0.4rem;
}

.floating-label-wrapper.left.labeled .floating-label,
.floating-label-wrapper:has([ref='prefix']) .floating-label {
	left: 3em !important;
}
.floating-label-wrapper:has(.floating-label):not(.field-has-value) > *:not(:focus)::placeholder {
	color: transparent;
}

/* -----------------
		Floating label end
-------------------- */
