/*!
 * # Fomantic-UI - Card
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "view";
@element: "card";

@import (multiple) "../../theme.config";

/*******************************
            Standard
*******************************/

/* --------------
      Card
--------------- */

.ui.cards > .card,
.ui.card {
    max-width: 100%;
    position: relative;
    display: @display;
    flex-direction: column;
    width: @width;
    min-height: @minHeight;
    background: @background;
    padding: @padding;
    border: @border;
    border-radius: @borderRadius;
    box-shadow: @boxShadow;
    transition: @transition;
    z-index: @zIndex;
    word-wrap: break-word;
}
.ui.card {
    margin: @margin;
}

.ui.cards > .card a,
.ui.card a {
    cursor: pointer;
}

.ui.card:first-child {
    margin-top: 0;
}
.ui.card:last-child {
    margin-bottom: 0;
}

/* --------------
      Cards
--------------- */

.ui.cards {
    display: @groupDisplay;
    margin: @groupMargin;
    flex-wrap: wrap;
}

.ui.cards > .card {
    display: @groupCardDisplay;
    margin: @groupCardMargin;
    float: @groupCardFloat;
}

/* Clearing */
.ui.cards::after,
.ui.card::after {
    display: block;
    content: " ";
    height: 0;
    clear: both;
    overflow: hidden;
    visibility: hidden;
}

/* Consecutive Card Groups Preserve Row Spacing */
.ui.cards ~ .ui.cards {
    margin-top: @consecutiveGroupDistance;
}

/* --------------
  Rounded Edges
--------------- */

.ui.cards > .card > :first-child,
.ui.card > :first-child {
    border-radius: @borderRadius @borderRadius 0 0 !important;
    border-top: none !important;
}

.ui.cards > .card > :last-child,
.ui.card > :last-child {
    border-radius: 0 0 @borderRadius @borderRadius !important;
}

.ui.cards > .card > :only-child,
.ui.card > :only-child {
    border-radius: @borderRadius !important;
}

& when (@variationCardImage) {
    /* --------------
         Images
    --------------- */

    .ui.cards > .card > .image,
    .ui.card > .image {
        position: relative;
        display: block;
        flex: 0 0 auto;
        padding: @imagePadding;
        background: @imageBackground;
    }
    .ui.cards > .card > .image > img,
    .ui.card > .image > img {
        display: block;
        width: 100%;
        height: auto;
        border-radius: inherit;
    }
    .ui.cards > .card > .image:not(.ui) > img,
    .ui.card > .image:not(.ui) > img {
        border: @imageBorder;
    }
}

/* --------------
     Content
--------------- */

.ui.cards > .card > .content,
.ui.card > .content {
    flex-grow: 1;
    border: @contentBorder;
    border-top: @contentDivider;
    background: @contentBackground;
    margin: @contentMargin;
    padding: @contentPadding;
    box-shadow: @contentBoxShadow;
    font-size: @contentFontSize;
    border-radius: @contentBorderRadius;
}

.ui.cards > .card > .content::after,
.ui.card > .content::after {
    display: block;
    content: " ";
    height: 0;
    clear: both;
    overflow: hidden;
    visibility: hidden;
}

& when (@variationCardHeader) {
    .ui.cards > .card > .content > .header,
    .ui.card > .content > .header {
        display: block;
        margin: @headerMargin;
        font-family: @headerFont;
        color: @headerColor;
    }

    /* Default Header Size */
    .ui.cards > .card > .content > .header:not(.ui),
    .ui.card > .content > .header:not(.ui) {
        font-weight: @headerFontWeight;
        font-size: @headerFontSize;
        margin-top: @headerLineHeightOffset;
        line-height: @headerLineHeight;
    }
}

& when (@variationCardDescription) {
    .ui.cards > .card > .content > .meta + .description,
    .ui.cards > .card > .content > .header + .description,
    .ui.card > .content > .meta + .description,
    .ui.card > .content > .header + .description {
        margin-top: @descriptionDistance;
    }
}

& when (@variationCardFloated) {
    /* ----------------
     Floated Content
    ----------------- */

    .ui.cards > .card [class*="left floated"],
    .ui.card [class*="left floated"] {
        float: left;
    }
    .ui.cards > .card [class*="right floated"],
    .ui.card [class*="right floated"] {
        float: right;
    }
}

& when (@variationCardAligned) {
    /* --------------
         Aligned
    --------------- */

    .ui.cards > .card [class*="left aligned"],
    .ui.card [class*="left aligned"] {
        text-align: left;
    }
    .ui.cards > .card [class*="center aligned"],
    .ui.card [class*="center aligned"] {
        text-align: center;
    }
    .ui.cards > .card [class*="right aligned"],
    .ui.card [class*="right aligned"] {
        text-align: right;
    }
}

& when (@variationCardImage) {
    /* --------------
      Content Image
    --------------- */

    .ui.cards > .card .content img,
    .ui.card .content img {
        display: inline-block;
        vertical-align: @contentImageVerticalAlign;
        width: @contentImageWidth;
    }
    .ui.cards > .card img.avatar,
    .ui.cards > .card .avatar img,
    .ui.card img.avatar,
    .ui.card .avatar img {
        width: @avatarSize;
        height: @avatarSize;
        border-radius: @avatarBorderRadius;
    }
}

& when (@variationCardDescription) {
    /* --------------
       Description
    --------------- */

    .ui.cards > .card > .content > .description,
    .ui.card > .content > .description {
        clear: both;
        color: @descriptionColor;
    }
}

/* --------------
    Paragraph
--------------- */

.ui.cards > .card > .content p,
.ui.card > .content p {
    margin: 0 0 @paragraphDistance;
}
.ui.cards > .card > .content p:last-child,
.ui.card > .content p:last-child {
    margin-bottom: 0;
}
& when (@variationCardMeta) {
    /* --------------
          Meta
    --------------- */

    .ui.cards > .card .meta,
    .ui.card .meta {
        font-size: @metaFontSize;
        color: @metaColor;
    }
    .ui.cards > .card .meta *,
    .ui.card .meta * {
        margin-right: @metaSpacing;
    }
    .ui.cards > .card .meta :last-child,
    .ui.card .meta :last-child {
        margin-right: 0;
    }

    & when (@variationCardFloated) {
        .ui.cards > .card .meta [class*="right floated"],
        .ui.card .meta [class*="right floated"] {
            margin-right: 0;
            margin-left: @metaSpacing;
        }
    }
}

/* --------------
      Links
--------------- */

/* Generic */
.ui.cards > .card > .content a:not(.ui),
.ui.card > .content a:not(.ui) {
    color: @contentLinkColor;
    transition: @contentLinkTransition;
}
.ui.cards > .card > .content a:not(.ui):hover,
.ui.card > .content a:not(.ui):hover {
    color: @contentLinkHoverColor;
}

& when (@variationCardHeader) {
    /* Header */
    .ui.cards > .card > .content > a.header,
    .ui.card > .content > a.header {
        color: @headerLinkColor;
    }
    .ui.cards > .card > .content > a.header:hover,
    .ui.card > .content > a.header:hover {
        color: @headerLinkHoverColor;
    }
}

& when (@variationCardMeta) {
    /* Meta */
    .ui.cards > .card .meta > a:not(.ui),
    .ui.card .meta > a:not(.ui) {
        color: @metaLinkColor;
    }
    .ui.cards > .card .meta > a:not(.ui):hover,
    .ui.card .meta > a:not(.ui):hover {
        color: @metaLinkHoverColor;
    }
}

& when (@variationCardButton) {
    /* --------------
         Buttons
    --------------- */

    .ui.cards > .card > .buttons,
    .ui.card > .buttons,
    .ui.cards > .card > .button,
    .ui.card > .button {
        margin: @buttonMargin;
        width: @buttonWidth;
        &:last-child {
            margin-bottom: -@borderWidth;
        }
    }
}

& when (@variationCardBasic) {
    /* --------------
       Basic
    --------------- */
    .ui.cards:not(.raised) > .basic.card:not(.raised),
    .ui.ui.ui.basic.cards:not(.raised) > .card:not(.raised),
    .ui.ui.ui.basic.card:not(.raised) {
        box-shadow: none;
        & > .buttons,
        > .button {
            margin: 0;
            width: 100%;
        }
    }
    & when (@variationCardLink) {
        .ui.ui.ui.ui.ui.basic.card:not(.raised):hover,
        .ui.ui.ui.ui.ui.basic.cards:not(.raised) .card:not(.raised):hover {
            box-shadow: none;
        }
    }
}

/* --------------
      Dimmer
--------------- */

.ui.cards > .card .dimmer,
.ui.card .dimmer {
    background: @dimmerColor;
    z-index: @dimmerZIndex;
}

/* --------------
     Labels
--------------- */

& when (@variationCardStar) {
    /* -----Star----- */

    /* Icon */
    .ui.cards > .card > .content .star.icon,
    .ui.card > .content .star.icon {
        cursor: pointer;
        opacity: @actionOpacity;
        transition: @actionTransition;
    }
    .ui.cards > .card > .content .star.icon:hover,
    .ui.card > .content .star.icon:hover {
        opacity: @actionHoverOpacity;
        color: @starColor;
    }
    .ui.cards > .card > .content .active.star.icon,
    .ui.card > .content .active.star.icon {
        color: @starActiveColor;
    }
}

& when (@variationCardLike) {
    /* -----Like----- */

    /* Icon */
    .ui.cards > .card > .content .like.icon,
    .ui.card > .content .like.icon {
        cursor: pointer;
        opacity: @actionOpacity;
        transition: @actionTransition;
    }
    .ui.cards > .card > .content .like.icon:hover,
    .ui.card > .content .like.icon:hover {
        opacity: @actionHoverOpacity;
        color: @likeColor;
    }
    .ui.cards > .card > .content .active.like.icon,
    .ui.card > .content .active.like.icon {
        color: @likeActiveColor;
    }
}

& when (@variationCardExtra) {
    /* ----------------
      Extra Content
    ----------------- */

    .ui.cards > .card > .extra,
    .ui.card > .extra {
        max-width: 100%;
        min-height: 0 !important;
        flex-grow: 0;
        border-top: @extraDivider !important;
        position: @extraPosition;
        background: @extraBackground;
        width: @extraWidth;
        margin: @extraMargin;
        padding: @extraPadding;
        top: @extraTop;
        left: @extraLeft;
        color: @extraColor;
        box-shadow: @extraBoxShadow;
        transition: @extraTransition;
    }
    .ui.cards > .card > .extra a:not(.ui),
    .ui.card > .extra a:not(.ui) {
        color: @extraLinkColor;
    }
    .ui.cards > .card > .extra a:not(.ui):hover,
    .ui.card > .extra a:not(.ui):hover {
        color: @extraLinkHoverColor;
    }
}

/*******************************
            States
*******************************/

& when (@variationCardDisabled) {
    /* --------------
        Disabled
    --------------- */

    .ui.disabled.card {
        opacity: @disabledOpacity;
        color: @disabledTextColor;
        pointer-events: none;
    }
}

& when (@variationCardLoading) {
    /* --------------
        Loading
    --------------- */

    .ui.loading.card {
        position: relative;
        cursor: default;
        pointer-events: none;
        transition: all 0s linear;
    }
    .ui.loading.card::before {
        position: absolute;
        content: "";
        top: 0;
        left: 0;
        background: @loaderDimmerColor;
        width: 100%;
        height: 100%;
        border-radius: @borderRadius;
        z-index: @loaderDimmerZIndex;
    }
    .ui.loading.card::after {
        position: absolute;
        content: "";
        top: 50%;
        left: 50%;
        margin: @loaderMargin;
        width: @loaderSize;
        height: @loaderSize;
        animation: loader @loaderSpeed infinite linear;
        border: @loaderLineWidth solid @loaderLineColor;
        border-radius: @circularRadius;
        box-shadow: 0 0 0 1px transparent;
        visibility: visible;
        z-index: @loaderLineZIndex;
    }
}

/*******************************
           Variations
*******************************/

& when (@variationCardHorizontal) {
    /* -------------------
          Horizontal
    -------------------- */

    .ui.horizontal.cards > .card,
    .ui.card.horizontal {
        flex-flow: row wrap;
        min-width: @horizontalMinWidth;
        width: @horizontalWidth;
        max-width: 100%;
    }

    .ui.horizontal.cards > .card > .image,
    .ui.card.horizontal > .image {
        border-radius: @defaultBorderRadius 0 0 @defaultBorderRadius;
        width: @horizontalImageWidth;
    }

    .ui.horizontal.cards > .card > .image > img,
    .ui.card.horizontal > .image > img {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        justify-content: center;
        align-items: center;
        display: flex;
        width: 100%;
        border-radius: @defaultBorderRadius 0 0 @defaultBorderRadius;
    }
    .ui.horizontal.cards > .card > .image:last-child > img,
    .ui.card.horizontal > .image:last-child > img {
        border-radius: 0 @defaultBorderRadius @defaultBorderRadius 0;
    }
    .ui.horizontal.cards > .card > .content,
    .ui.horizontal.card > .content {
        border-top: none;
        flex-basis: 1px;
    }
    .ui.horizontal.cards > .card > .extra,
    .ui.horizontal.card > .extra {
        flex-basis: 100%;
    }
}

& when (@variationCardRaised) {
    /* -------------------
           Raised
    -------------------- */

    .ui.cards > .raised.card,
    .ui.raised.cards > .card,
    .ui.raised.card {
        box-shadow: @raisedShadow;
    }
    & when (@variationCardLink) {
        .ui.raised.cards a.card:hover,
        .ui.link.cards .raised.card:hover,
        a.ui.raised.card:hover,
        .ui.link.raised.card:hover {
            box-shadow: @raisedShadowHover;
        }
    }
    & when (@variationCardBasic) {
        .ui.basic.cards > .raised.card,
        .ui.basic.raised.cards > .card,
        .ui.raised.cards > .basic.card,
        .ui.basic.raised.card {
            box-shadow: @basicRaisedShadow;
        }
        & when (@variationCardLink) {
            .ui.basic.raised.cards a.card:hover,
            .ui.link.cards .basic.raised.card:hover,
            .ui.link.basic.cards .raised.card:hover,
            .ui.link.basic.raised.cards .card:hover,
            a.ui.basic.raised.card:hover,
            .ui.link.basic.raised.card:hover {
                box-shadow: @basicRaisedShadowHover;
            }
        }
    }
}

& when (@variationCardCentered) {
    /* -------------------
           Centered
    -------------------- */

    .ui.centered.cards {
        justify-content: center;
    }
    .ui.centered.card {
        margin-left: auto;
        margin-right: auto;
    }
}

& when (@variationCardFluid) {
    /* -------------------
            Fluid
    -------------------- */

    .ui.fluid.card {
        width: 100%;
        max-width: 9999px;
    }
}

& when (@variationCardLink) {
    /* -------------------
            Link
    -------------------- */

    .ui.cards a.card,
    .ui.link.cards .card,
    a.ui.card,
    .ui.link.card {
        transform: none;
    }

    .ui.cards a.card:hover,
    .ui.link.cards .card:not(.icon):hover,
    a.ui.card:hover,
    .ui.link.card:hover {
        cursor: pointer;
        z-index: @linkHoverZIndex;
        background: @linkHoverBackground;
        border: @linkHoverBorder;
        box-shadow: @linkHoverBoxShadow;
        transform: @linkHoverTransform;
    }
}

/* -------------------
       Colors
-------------------- */
& when not (@variationCardColors = false) {
    each(@variationCardColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @h: @colors[@@color][hover];
        @l: @colors[@@color][light];
        @lh: @colors[@@color][lightHover];
        @isVeryDark: @colors[@@color][isVeryDark];

        .ui.@{color}.cards > .card,
        .ui.cards > .@{color}.card,
        .ui.@{color}.card {
            box-shadow:
                @borderShadow,
                0 @coloredShadowDistance 0 0 @c,
                @shadowBoxShadow;
        }
        & when (@variationCardLink) {
            .ui.cards a.@{color}.card:hover,
            .ui.@{color}.cards a.card:hover,
            .ui.link.@{color}.cards .card:not(.icon):hover,
            .ui.link.cards .@{color}.card:not(.icon):hover,
            a.ui.@{color}.card:hover,
            .ui.link.@{color}.card:hover {
                box-shadow:
                    @borderShadow,
                    0 @coloredShadowDistance 0 0 @h,
                    @shadowHoverBoxShadow;
            }
        }
        & when (@variationCardBasic) {
            .ui.cards > .basic.@{color}.card,
            .ui.basic.@{color}.cards > .card,
            .ui.basic.cards > .@{color}.card,
            .ui.basic.@{color}.card {
                background: @l;
                & when (@isVeryDark) {
                    & .header,
                    .content,
                    .meta,
                    .description {
                        color: @white;
                    }
                }
            }
            & when (@variationCardLink) {
                .ui.basic.cards a.@{color}.card:hover,
                .ui.cards a.basic.@{color}.card:hover,
                .ui.basic.@{color}.cards a.card:hover,
                .ui.link.cards .basic.@{color}.card:not(.icon):hover,
                .ui.link.basic.@{color}.cards .card:not(.icon):hover,
                .ui.link.basic.cards .@{color}.card:not(.icon):hover,
                a.ui.basic.@{color}.card:hover,
                .ui.link.basic.@{color}.card:hover {
                    background: @lh;
                }
            }
        }
        & when (@variationCardInverted) {
            .ui.inverted.@{color}.cards > .card,
            .ui.inverted.cards > .@{color}.card,
            .ui.inverted.@{color}.card {
                box-shadow:
                    0 @shadowDistance 3px 0 @solidWhiteBorderColor,
                    0 @coloredShadowDistance 0 0 @l,
                    0 0 0 @borderWidth @solidWhiteBorderColor;
            }
            & when (@variationCardLink) {
                .ui.inverted.cards a.@{color}.card:hover,
                .ui.inverted.@{color}.cards a.card:hover,
                .ui.link.inverted.@{color}.cards .card:not(.icon):hover,
                .ui.link.inverted.cards .@{color}.card:not(.icon):hover,
                a.ui.inverted.@{color}.card:hover,
                .ui.link.inverted.@{color}.card:hover {
                    box-shadow:
                        0 @shadowDistance 3px 0 @solidWhiteBorderColor,
                        0 @coloredShadowDistance 0 0 @lh,
                        0 0 0 @borderWidth @solidWhiteBorderColor;
                }
            }
            & when (@variationCardBasic) {
                .ui.inverted.cards > .basic.@{color}.card,
                .ui.basic.inverted.@{color}.cards > .card,
                .ui.basic.inverted.cards > .@{color}.card,
                .ui.basic.inverted.@{color}.card {
                    background: @c;
                }
                & when (@variationCardLink) {
                    .ui.inverted.cards a.basic.@{color}.card:hover,
                    .ui.basic.inverted.cards a.@{color}.card:hover,
                    .ui.basic.inverted.@{color}.cards a.card:hover,
                    .ui.link.inverted.cards .basic.@{color}.card:not(.icon):hover,
                    .ui.link.basic.inverted.@{color}.cards .card:not(.icon):hover,
                    .ui.link.basic.inverted.cards .@{color}.card:not(.icon):hover,
                    a.ui.basic.inverted.@{color}.card:hover,
                    .ui.link.basic.inverted.@{color}.card:hover {
                        background: @h;
                    }
                }
            }
        }
    });
}

& when (@variationCardEqualWidth) {
    /* --------------
       Card Count
    --------------- */

    .ui.one.cards {
        margin-left: @oneCardOffset;
        margin-right: @oneCardOffset;
    }
    .ui.one.cards > .card {
        width: @oneCard;
    }

    .ui.two.cards {
        margin-left: @twoCardOffset;
        margin-right: @twoCardOffset;
    }
    .ui.two.cards > .card {
        width: @twoCard;
        margin-left: @twoCardSpacing;
        margin-right: @twoCardSpacing;
    }

    .ui.three.cards {
        margin-left: @threeCardOffset;
        margin-right: @threeCardOffset;
    }
    .ui.three.cards > .card {
        width: @threeCard;
        margin-left: @threeCardSpacing;
        margin-right: @threeCardSpacing;
    }

    .ui.four.cards {
        margin-left: @fourCardOffset;
        margin-right: @fourCardOffset;
    }
    .ui.four.cards > .card {
        width: @fourCard;
        margin-left: @fourCardSpacing;
        margin-right: @fourCardSpacing;
    }

    .ui.five.cards {
        margin-left: @fiveCardOffset;
        margin-right: @fiveCardOffset;
    }
    .ui.five.cards > .card {
        width: @fiveCard;
        margin-left: @fiveCardSpacing;
        margin-right: @fiveCardSpacing;
    }

    .ui.six.cards {
        margin-left: @sixCardOffset;
        margin-right: @sixCardOffset;
    }
    .ui.six.cards > .card {
        width: @sixCard;
        margin-left: @sixCardSpacing;
        margin-right: @sixCardSpacing;
    }

    .ui.seven.cards {
        margin-left: @sevenCardOffset;
        margin-right: @sevenCardOffset;
    }
    .ui.seven.cards > .card {
        width: @sevenCard;
        margin-left: @sevenCardSpacing;
        margin-right: @sevenCardSpacing;
    }

    .ui.eight.cards {
        margin-left: @eightCardOffset;
        margin-right: @eightCardOffset;
    }
    .ui.eight.cards > .card {
        width: @eightCard;
        margin-left: @eightCardSpacing;
        margin-right: @eightCardSpacing;
        font-size: 11px;
    }

    .ui.nine.cards {
        margin-left: @nineCardOffset;
        margin-right: @nineCardOffset;
    }
    .ui.nine.cards > .card {
        width: @nineCard;
        margin-left: @nineCardSpacing;
        margin-right: @nineCardSpacing;
        font-size: 10px;
    }

    .ui.ten.cards {
        margin-left: @tenCardOffset;
        margin-right: @tenCardOffset;
    }
    .ui.ten.cards > .card {
        width: @tenCard;
        margin-left: @tenCardSpacing;
        margin-right: @tenCardSpacing;
    }
}

& when (@variationCardDoubling) {
    /* -------------------
          Doubling
    -------------------- */

    /* Mobile Only */
    @media only screen and (max-width: @largestMobileScreen) {
        .ui.two.doubling.cards {
            margin-left: @oneCardOffset;
            margin-right: @oneCardOffset;
        }
        .ui.two.doubling.cards > .card {
            width: @oneCard;
            margin-left: @oneCardSpacing;
            margin-right: @oneCardSpacing;
        }
        .ui.three.doubling.cards {
            margin-left: @twoCardOffset;
            margin-right: @twoCardOffset;
        }
        .ui.three.doubling.cards > .card {
            width: @twoCard;
            margin-left: @twoCardSpacing;
            margin-right: @twoCardSpacing;
        }
        .ui.four.doubling.cards {
            margin-left: @twoCardOffset;
            margin-right: @twoCardOffset;
        }
        .ui.four.doubling.cards > .card {
            width: @twoCard;
            margin-left: @twoCardSpacing;
            margin-right: @twoCardSpacing;
        }
        .ui.five.doubling.cards {
            margin-left: @twoCardOffset;
            margin-right: @twoCardOffset;
        }
        .ui.five.doubling.cards > .card {
            width: @twoCard;
            margin-left: @twoCardSpacing;
            margin-right: @twoCardSpacing;
        }
        .ui.six.doubling.cards {
            margin-left: @twoCardOffset;
            margin-right: @twoCardOffset;
        }
        .ui.six.doubling.cards > .card {
            width: @twoCard;
            margin-left: @twoCardSpacing;
            margin-right: @twoCardSpacing;
        }
        .ui.seven.doubling.cards {
            margin-left: @threeCardOffset;
            margin-right: @threeCardOffset;
        }
        .ui.seven.doubling.cards > .card {
            width: @threeCard;
            margin-left: @threeCardSpacing;
            margin-right: @threeCardSpacing;
        }
        .ui.eight.doubling.cards {
            margin-left: @threeCardOffset;
            margin-right: @threeCardOffset;
        }
        .ui.eight.doubling.cards > .card {
            width: @threeCard;
            margin-left: @threeCardSpacing;
            margin-right: @threeCardSpacing;
        }
        .ui.nine.doubling.cards {
            margin-left: @threeCardOffset;
            margin-right: @threeCardOffset;
        }
        .ui.nine.doubling.cards > .card {
            width: @threeCard;
            margin-left: @threeCardSpacing;
            margin-right: @threeCardSpacing;
        }
        .ui.ten.doubling.cards {
            margin-left: @threeCardOffset;
            margin-right: @threeCardOffset;
        }
        .ui.ten.doubling.cards > .card {
            width: @threeCard;
            margin-left: @threeCardSpacing;
            margin-right: @threeCardSpacing;
        }
    }

    /* Tablet Only */
    @media only screen and (min-width: @tabletBreakpoint) and (max-width: @largestTabletScreen) {
        .ui.two.doubling.cards {
            margin-left: @oneCardOffset;
            margin-right: @oneCardOffset;
        }
        .ui.two.doubling.cards > .card {
            width: @oneCard;
            margin-left: @oneCardSpacing;
            margin-right: @oneCardSpacing;
        }
        .ui.three.doubling.cards {
            margin-left: @twoCardOffset;
            margin-right: @twoCardOffset;
        }
        .ui.three.doubling.cards > .card {
            width: @twoCard;
            margin-left: @twoCardSpacing;
            margin-right: @twoCardSpacing;
        }
        .ui.four.doubling.cards {
            margin-left: @twoCardOffset;
            margin-right: @twoCardOffset;
        }
        .ui.four.doubling.cards > .card {
            width: @twoCard;
            margin-left: @twoCardSpacing;
            margin-right: @twoCardSpacing;
        }
        .ui.five.doubling.cards {
            margin-left: @threeCardOffset;
            margin-right: @threeCardOffset;
        }
        .ui.five.doubling.cards > .card {
            width: @threeCard;
            margin-left: @threeCardSpacing;
            margin-right: @threeCardSpacing;
        }
        .ui.six.doubling.cards {
            margin-left: @threeCardOffset;
            margin-right: @threeCardOffset;
        }
        .ui.six.doubling.cards > .card {
            width: @threeCard;
            margin-left: @threeCardSpacing;
            margin-right: @threeCardSpacing;
        }
        .ui.eight.doubling.cards {
            margin-left: @threeCardOffset;
            margin-right: @threeCardOffset;
        }
        .ui.eight.doubling.cards > .card {
            width: @threeCard;
            margin-left: @threeCardSpacing;
            margin-right: @threeCardSpacing;
        }
        .ui.eight.doubling.cards {
            margin-left: @fourCardOffset;
            margin-right: @fourCardOffset;
        }
        .ui.eight.doubling.cards > .card {
            width: @fourCard;
            margin-left: @fourCardSpacing;
            margin-right: @fourCardSpacing;
        }
        .ui.nine.doubling.cards {
            margin-left: @fourCardOffset;
            margin-right: @fourCardOffset;
        }
        .ui.nine.doubling.cards > .card {
            width: @fourCard;
            margin-left: @fourCardSpacing;
            margin-right: @fourCardSpacing;
        }
        .ui.ten.doubling.cards {
            margin-left: @fiveCardOffset;
            margin-right: @fiveCardOffset;
        }
        .ui.ten.doubling.cards > .card {
            width: @fiveCard;
            margin-left: @fiveCardSpacing;
            margin-right: @fiveCardSpacing;
        }
    }
}

& when (@variationCardStackable) {
    /* -------------------
          Stackable
    -------------------- */

    @media only screen and (max-width: @largestMobileScreen) {
        .ui.stackable.cards {
            display: block !important;
        }
        .ui.stackable.cards .card:first-child {
            margin-top: 0 !important;
        }
        .ui.stackable.cards > .card {
            display: block !important;
            height: auto !important;
            margin: @stackableRowSpacing @stackableCardSpacing;
            padding: 0 !important;
            width: @stackableMargin !important;
        }
    }
}

/* --------------
      Size
--------------- */

.ui.cards > .card {
    font-size: @medium;
}
& when not (@variationCardSizes = false) {
    each(@variationCardSizes, {
        @s: @@value;
        .ui.@{value}.card,
        .ui.@{value}.cards .card {
            font-size: @s;
        }
    });
}

& when (@variationCardInverted) {
    /* -----------------
          Inverted
    ------------------ */

    .ui.inverted.cards > .card,
    .ui.inverted.card {
        background: @invertedBackground;
        box-shadow: @invertedBoxShadow;
    }

    /* Content */
    .ui.inverted.cards > .card > .content,
    .ui.inverted.card > .content {
        border-top: @invertedContentDivider;
    }

    & when (@variationCardHeader) {
        /* Header */
        .ui.inverted.cards > .card > .content > .header,
        .ui.inverted.card > .content > .header {
            color: @invertedHeaderColor;
        }
        .ui.inverted.cards > .card > .content > a.header,
        .ui.inverted.card > .content > a.header {
            color: @invertedHeaderLinkColor;
            &:hover {
                color: @invertedHeaderLinkHoverColor;
            }
        }
    }

    & when (@variationCardDescription) {
        /* Description */
        .ui.inverted.cards > .card > .content > .description,
        .ui.inverted.card > .content > .description {
            color: @invertedDescriptionColor;
        }
    }

    & when (@variationCardMeta) {
        /* Meta */
        .ui.inverted.cards > .card .meta,
        .ui.inverted.card .meta {
            color: @invertedMetaColor;
        }
        .ui.inverted.cards > .card .meta > a:not(.ui),
        .ui.inverted.card .meta > a:not(.ui) {
            color: @invertedMetaLinkColor;
        }
        .ui.inverted.cards > .card .meta > a:not(.ui):hover,
        .ui.inverted.card .meta > a:not(.ui):hover {
            color: @invertedMetaLinkHoverColor;
        }
    }

    & when (@variationCardExtra) {
        /* Extra */
        .ui.inverted.cards > .card > .extra,
        .ui.inverted.card > .extra {
            border-top: @invertedExtraDivider !important;
            color: @invertedExtraColor;
        }
        .ui.inverted.cards > .card > .extra a:not(.ui),
        .ui.inverted.card > .extra a:not(.ui) {
            color: @invertedExtraLinkColor;
        }
        .ui.inverted.cards > .card > .extra a:not(.ui):hover,
        .ui.inverted.card > .extra a:not(.ui):hover {
            color: @extraLinkHoverColor;
        }
    }

    & when (@variationCardLink) {
        /* Link card(s) */
        .ui.inverted.cards a.card:hover,
        .ui.inverted.link.cards .card:not(.icon):hover,
        a.inverted.ui.card:hover,
        .ui.inverted.link.card:hover {
            background: @invertedLinkHoverBackground;
        }
    }

    & when (@variationCardLoading) {
        /* Loading */
        .ui.inverted.loading.card {
            color: @invertedLoaderLineColor;
        }
        .ui.inverted.loading.card::before {
            background: @loaderInvertedDimmerColor;
        }
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
