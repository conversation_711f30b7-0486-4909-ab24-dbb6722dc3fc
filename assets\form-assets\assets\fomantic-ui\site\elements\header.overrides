/*******************************
         Site Overrides
*******************************/

.un-accordion {
	@unAccordionHeaderTop: 22px;
	position: relative;
	background: @white;
	padding: 0 @spaceCommonPad @spaceCommonPad @spaceCommonPad !important;
	border: 1px solid @fieldBorderColor;
	border-radius: 0.57142857rem;
	margin-top: calc(unit((20 / 14), em) + @unAccordionHeaderTop) !important;

	&:not(:has(.ui.segment)) .ui.top.attached.header {
		border-radius: @blockBorderRadius;
	}

	.ui.block.header {
		width: auto !important;
		padding: @attachedVerticalPadding;
		margin-top: -@unAccordionHeaderTop;
		border-radius: @attachedBorderRadius;
		cursor: pointer;
		transition: all 0.4s;

		&:hover {
			background: @white;
		}
	}

	.un-accordion-header {
		display: inline-flex !important;
		justify-content: space-between;
		align-items: center;
		gap: 8px;
		font-size: @relativeLarge;
		padding: calc(@spaceCommonPad / 2) !important;

		// @media only screen and (max-width: @largestMobileScreen) {
		// 	padding: @spaceCommonPadResponsive !important;
		// }
	}

	/* override the default styles for the nested elements */
	.ui.segment .ui.segment:not(.un-panel-body):not(.tab),
	// .formio-component-datagrid,
	.formio-component-tabs {
		background: transparent;
		padding: 0;
		border: 0;
	}
}
