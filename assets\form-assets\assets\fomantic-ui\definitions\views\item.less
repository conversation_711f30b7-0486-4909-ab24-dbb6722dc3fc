/*!
 * # Fomantic-UI - Item
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "view";
@element: "item";

@import (multiple) "../../theme.config";

/*******************************
            Standard
*******************************/

/* --------------
      Item
--------------- */

.ui.items > .item {
    display: @display;
    margin: @itemSpacing 0;
    width: @width;
    min-height: @minHeight;
    background: @background;
    padding: @padding;
    border: @border;
    border-radius: @borderRadius;
    box-shadow: @boxShadow;
    transition: @transition;
    z-index: @zIndex;
}
.ui.items > .item a {
    cursor: pointer;
}

/* --------------
      Items
--------------- */

.ui.items {
    margin: @groupMargin;
}

.ui.items:first-child {
    margin-top: 0 !important;
}
.ui.items:last-child {
    margin-bottom: 0 !important;
}

/* --------------
      Item
--------------- */

.ui.items > .item::after {
    display: block;
    content: " ";
    height: 0;
    clear: both;
    overflow: hidden;
    visibility: hidden;
}
.ui.items > .item:first-child {
    margin-top: 0;
}
.ui.items > .item:last-child {
    margin-bottom: 0;
}

& when (@variationItemImage) {
    /* --------------
         Images
    --------------- */

    .ui.items > .item > .image {
        position: relative;
        flex: 0 0 auto;
        display: @imageDisplay;
        float: @imageFloat;
        margin: @imageMargin;
        padding: @imagePadding;
        max-height: @imageMaxHeight;
        align-self: @imageVerticalAlign;
    }
    .ui.items > .item > .image > img {
        display: block;
        width: 100%;
        height: auto;
        border-radius: @imageBorderRadius;
        border: @imageBorder;
    }

    .ui.items > .item > .image:only-child > img {
        border-radius: @borderRadius;
    }
}

/* --------------
     Content
--------------- */

.ui.items > .item > .content {
    display: block;
    flex: 1 1 auto;
    background: @contentBackground;
    color: @contentColor;
    margin: @contentMargin;
    padding: @contentPadding;
    box-shadow: @contentBoxShadow;
    font-size: @contentFontSize;
    border: @contentBorder;
    border-radius: @contentBorderRadius;
}
.ui.items > .item > .content::after {
    display: block;
    content: " ";
    height: 0;
    clear: both;
    overflow: hidden;
    visibility: hidden;
}

.ui.items > .item > .image + .content {
    min-width: 0;
    width: @contentWidth;
    display: @contentDisplay;
    margin-left: @contentOffset;
    align-self: @contentVerticalAlign;
    padding-left: @contentImageDistance;
}

& when (@variationItemHeader) {
    .ui.items > .item > .content > .header {
        display: inline-block;
        margin: @headerMargin;
        font-family: @headerFont;
        font-weight: @headerFontWeight;
        color: @headerColor;
    }

    /* Default Header Size */
    .ui.items > .item > .content > .header:not(.ui) {
        font-size: @headerFontSize;
    }
}

& when (@variationItemFloated) {
    /* --------------
         Floated
    --------------- */

    .ui.items > .item [class*="left floated"] {
        float: left;
    }
    .ui.items > .item [class*="right floated"] {
        float: right;
    }
}

& when (@variationItemImage) {
    /* --------------
      Content Image
    --------------- */

    .ui.items > .item .content img {
        align-self: @contentImageVerticalAlign;
        width: @contentImageWidth;
    }
    .ui.items > .item img.avatar,
    .ui.items > .item .avatar img {
        width: @avatarSize;
        height: @avatarSize;
        border-radius: @avatarBorderRadius;
    }
}

& when (@variationItemDescription) {
    /* --------------
       Description
    --------------- */

    .ui.items > .item > .content > .description {
        margin-top: @descriptionDistance;
        max-width: @descriptionMaxWidth;
        font-size: @descriptionFontSize;
        line-height: @descriptionLineHeight;
        color: @descriptionColor;
    }
}

/* --------------
    Paragraph
--------------- */

.ui.items > .item > .content p {
    margin: 0 0 @paragraphDistance;
}
.ui.items > .item > .content p:last-child {
    margin-bottom: 0;
}

& when (@variationItemMeta) {
    /* --------------
          Meta
    --------------- */

    .ui.items > .item .meta {
        margin: @metaMargin;
        font-size: @metaFontSize;
        line-height: @metaLineHeight;
        color: @metaColor;
    }
    .ui.items > .item .meta * {
        margin-right: @metaSpacing;
    }
    .ui.items > .item .meta :last-child {
        margin-right: 0;
    }

    & when (@variationItemFloated) {
        .ui.items > .item .meta [class*="right floated"] {
            margin-right: 0;
            margin-left: @metaSpacing;
        }
    }
}

/* --------------
      Links
--------------- */

/* Generic */
.ui.items > .item > .content a:not(.ui) {
    color: @contentLinkColor;
    transition: @contentLinkTransition;
}
.ui.items > .item > .content a:not(.ui):hover {
    color: @contentLinkHoverColor;
}

& when (@variationItemHeader) {
    /* Header */
    .ui.items > .item > .content > a.header {
        color: @headerLinkColor;
    }
    .ui.items > .item > .content > a.header:hover {
        color: @headerLinkHoverColor;
    }
}

& when (@variationItemMeta) {
    /* Meta */
    .ui.items > .item .meta > a:not(.ui) {
        color: @metaLinkColor;
    }
    .ui.items > .item .meta > a:not(.ui):hover {
        color: @metaLinkHoverColor;
    }
}

/* --------------
     Labels
--------------- */

/* -----Star----- */

& when (@variationItemFavorite) {
    /* Icon */
    .ui.items > .item > .content .favorite.icon {
        cursor: pointer;
        opacity: @actionOpacity;
        transition: @actionTransition;
    }
    .ui.items > .item > .content .favorite.icon:hover {
        opacity: @actionHoverOpacity;
        color: @favoriteColor;
    }
    .ui.items > .item > .content .active.favorite.icon {
        color: @favoriteActiveColor;
    }
}

& when (@variationItemLike) {
    /* -----Like----- */

    /* Icon */
    .ui.items > .item > .content .like.icon {
        cursor: pointer;
        opacity: @actionOpacity;
        transition: @actionTransition;
    }
    .ui.items > .item > .content .like.icon:hover {
        opacity: @actionHoverOpacity;
        color: @likeColor;
    }
    .ui.items > .item > .content .active.like.icon {
        color: @likeActiveColor;
    }
}

& when (@variationItemExtra) {
    /* ----------------
      Extra Content
    ----------------- */

    .ui.items > .item .extra {
        display: @extraDisplay;
        position: @extraPosition;
        background: @extraBackground;
        margin: @extraMargin;
        width: @extraWidth;
        padding: @extraPadding;
        top: @extraTop;
        left: @extraLeft;
        color: @extraColor;
        box-shadow: @extraBoxShadow;
        transition: @extraTransition;
        border-top: @extraDivider;
    }
    .ui.items > .item .extra > * {
        margin: (@extraRowSpacing / 2) @extraHorizontalSpacing (@extraRowSpacing / 2) 0;
    }
    & when (@variationItemFloated) {
        .ui.items > .item .extra > [class*="right floated"] {
            margin: (@extraRowSpacing / 2) 0 (@extraRowSpacing / 2) @extraHorizontalSpacing;
        }
    }

    .ui.items > .item .extra::after {
        display: block;
        content: " ";
        height: 0;
        clear: both;
        overflow: hidden;
        visibility: hidden;
    }
}

/*******************************
          Responsive
*******************************/

/* Default Image Width */
.ui.items > .item > .image:not(.ui) {
    width: @imageWidth;
}

/* Tablet Only */
@media only screen and (min-width: @tabletBreakpoint) and (max-width: @largestTabletScreen) {
    .ui.items > .item {
        margin: @tabletItemSpacing 0;
    }
    .ui.items > .item > .image:not(.ui) {
        width: @tabletImageWidth;
    }
    .ui.items > .item > .image + .content {
        display: block;
        padding: 0 0 0 @tabletContentImageDistance;
    }
}

/* Mobile Only */
@media only screen and (max-width: @largestMobileScreen) {
    .ui.items:not(.unstackable) > .item {
        flex-direction: column;
        margin: @mobileItemSpacing 0;
    }
    .ui.items:not(.unstackable) > .item > .image {
        display: block;
        margin-left: auto;
        margin-right: auto;
    }
    .ui.items:not(.unstackable) > .item > .image,
    .ui.items:not(.unstackable) > .item > .image > img {
        max-width: 100% !important;
        width: @mobileImageWidth !important;
        max-height: @mobileImageMaxHeight !important;
    }
    .ui.items:not(.unstackable) > .item > .image + .content {
        display: block;
        padding: @mobileContentImageDistance 0 0;
    }
}

/*******************************
           Variations
*******************************/

& when (@variationItemAligned) {
    /* -------------------
           Aligned
    -------------------- */

    .ui.items > .item > .image + [class*="top aligned"].content {
        align-self: flex-start;
    }
    .ui.items > .item > .image + [class*="middle aligned"].content {
        align-self: center;
    }
    .ui.items > .item > .image + [class*="bottom aligned"].content {
        align-self: flex-end;
    }
}

& when (@variationItemRelaxed) {
    /* --------------
         Relaxed
    --------------- */

    .ui.relaxed.items > .item {
        margin: @relaxedItemSpacing 0;
    }
    & when (@variationItemVeryRelaxed) {
        .ui[class*="very relaxed"].items > .item {
            margin: @veryRelaxedItemSpacing 0;
        }
    }
}

& when (@variationItemDivided) {
    /* -------------------
          Divided
    -------------------- */

    .ui.divided.items > .item {
        border-top: @dividedBorder;
        margin: @dividedMargin;
        padding: @dividedPadding;
    }
    .ui.divided.items > .item:first-child {
        border-top: none;
        margin-top: @dividedFirstLastMargin !important;
        padding-top: @dividedFirstLastPadding !important;
    }
    .ui.divided.items > .item:last-child {
        margin-bottom: @dividedFirstLastMargin !important;
        padding-bottom: @dividedFirstLastPadding !important;
    }
    & when (@variationItemRelaxed) {
        /* Relaxed Divided */
        .ui.relaxed.divided.items > .item {
            margin: 0;
            padding: @relaxedItemSpacing 0;
        }
        & when (@variationItemVeryRelaxed) {
            .ui[class*="very relaxed"].divided.items > .item {
                margin: 0;
                padding: @veryRelaxedItemSpacing 0;
            }
        }
    }
}

& when (@variationItemLink) {
    /* -------------------
            Link
    -------------------- */

    .ui.items a.item:hover,
    .ui.link.items > .item:hover {
        cursor: pointer;
    }

    & when (@variationItemHeader) {
        .ui.items a.item:hover .content .header,
        .ui.link.items > .item:hover .content .header {
            color: @headerLinkHoverColor;
        }
    }
}

/* --------------
      Size
--------------- */

.ui.items > .item {
    font-size: @relativeMedium;
}
& when not (@variationItemSizes = false) {
    each(@variationItemSizes, {
        @s: @{value}ItemSize;
        .ui.@{value}.items > .item {
            font-size: @@s;
        }
    });
}

& when (@variationItemUnstackable) {
    /* ---------------
       Unstackable
    ---------------- */

    @media only screen and (max-width: @largestMobileScreen) {
        .ui.unstackable.items > .item > .image,
        .ui.unstackable.items > .item > .image > img {
            width: @unstackableMobileImageWidth !important;
        }
    }
}

& when (@variationItemInverted) {
    /* --------------
         Inverted
    --------------- */

    .ui.inverted.items > .item {
        background: @invertedBackground;
    }
    .ui.inverted.items > .item > .content {
        background: @invertedContentBackground;
        color: @invertedContentColor;
    }
    & when (@variationItemExtra) {
        .ui.inverted.items > .item .extra {
            background: @invertedExtraBackground;
        }
    }
    & when (@variationItemHeader) {
        .ui.inverted.items > .item > .content > .header {
            color: @invertedHeaderColor;
        }
    }
    & when (@variationItemDescription) {
        .ui.inverted.items > .item > .content > .description {
            color: @invertedDescriptionColor;
        }
    }
    & when (@variationItemMeta) {
        .ui.inverted.items > .item .meta {
            color: @invertedMetaColor;
        }
    }
    .ui.inverted.items > .item > .content a:not(.ui) {
        color: @invertedContentLinkColor;
    }
    .ui.inverted.items > .item > .content a:not(.ui):hover {
        color: @invertedContentLinkHoverColor;
    }
    & when (@variationItemHeader) {
        .ui.inverted.items > .item > .content > a.header {
            color: @invertedHeaderLinkColor;
        }
        .ui.inverted.items > .item > .content > a.header:hover {
            color: @invertedHeaderLinkHoverColor;
        }
    }
    & when (@variationItemMeta) {
        .ui.inverted.items > .item .meta > a:not(.ui) {
            color: @invertedMetaLinkColor;
        }
        .ui.inverted.items > .item .meta > a:not(.ui):hover {
            color: @invertedMetaLinkHoverColor;
        }
    }
    & when (@variationItemFavorite) {
        .ui.inverted.items > .item > .content .favorite.icon:hover {
            color: @invertedFavoriteColor;
        }
        .ui.inverted.items > .item > .content .active.favorite.icon {
            color: @invertedFavoriteActiveColor;
        }
    }
    & when (@variationItemLike) {
        .ui.inverted.items > .item > .content .like.icon:hover {
            color: @invertedLikeColor;
        }
        .ui.inverted.items > .item > .content .active.like.icon {
            color: @invertedLikeActiveColor;
        }
    }
    & when (@variationItemExtra) {
        .ui.inverted.items > .item .extra {
            color: @invertedExtraColor;
        }
    }
    & when (@variationItemHeader) {
        .ui.inverted.items a.item:hover .content .header,
        .ui.inverted.link.items > .item:hover .content .header {
            color: @invertedHeaderLinkHoverColor;
        }
    }
    & when (@variationItemDivided) {
        .ui.inverted.divided.items > .item {
            border-top: @invertedDividedBorder;
        }
        .ui.inverted.divided.items > .item:first-child {
            border-top: none;
        }
    }
}
& when (@variationItemDisabled) {
    .ui.disabled.items,
    .ui.items > .disabled.item {
        opacity: @disabledOpacity;
        pointer-events: @disabledPointerEvents;
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
