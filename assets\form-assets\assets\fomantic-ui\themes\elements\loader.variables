/*******************************
             Loader
*******************************/

/* Some global loader styles defined in site.variables */
// @loaderSpeed
// @loaderLineWidth
// @loaderFillColor
// @loaderLineColor
// @invertedLoaderFillColor
// @invertedLoaderLineColor

/* -------------------
      Standard
-------------------- */

@loaderTopOffset: 50%;
@loaderLeftOffset: 50%;

@shapeBorderColor: @loaderLineColor;
@invertedShapeBorderColor: @invertedLoaderLineColor;

/* -------------------
        Types
-------------------- */

/* Text */
@textDistance: @relativeMini;
@loaderTextColor: @textColor;
@invertedLoaderTextColor: @invertedTextColor;

/* -------------------
        States
-------------------- */

@indeterminateDirection: reverse;
@indeterminateSpeed: (2 * @loaderSpeed);

/* -------------------
      Variations
-------------------- */

@inlineVerticalAlign: middle;
@inlineMargin: 0;

/* Exact Sizes (Avoids Rounding Errors) */
@mini: @14px;
@tiny: @16px;
@small: @24px;
@medium: @32px;
@large: @48px;
@big: @52px;
@huge: @58px;
@massive: @64px;

@miniOffset: 0 0 0 -(@mini / 2);
@tinyOffset: 0 0 0 -(@tiny / 2);
@smallOffset: 0 0 0 -(@small / 2);
@mediumOffset: 0 0 0 -(@medium / 2);
@largeOffset: 0 0 0 -(@large / 2);
@bigOffset: 0 0 0 -(@big / 2);
@hugeOffset: 0 0 0 -(@huge / 2);
@massiveOffset: 0 0 0 -(@massive / 2);

@tinyFontSize: @relativeTiny;
@miniFontSize: @relativeMini;
@smallFontSize: @relativeSmall;
@mediumFontSize: @relativeMedium;
@largeFontSize: @relativeLarge;
@bigFontSize: @relativeBig;
@hugeFontSize: @relativeHuge;
@massiveFontSize: @relativeMassive;
