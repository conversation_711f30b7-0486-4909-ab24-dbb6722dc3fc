/*!
 * # Fomantic-UI - Sticky
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "sticky";

@import (multiple) "../../theme.config";

/*******************************
            Sticky
*******************************/

.ui.sticky {
    position: static;
    transition: @transition;
    z-index: @zIndex;
}

/*******************************
            States
*******************************/

/* Bound */
.ui.sticky.bound {
    position: absolute;
    left: auto;
    right: auto;
}

/* Fixed */
.ui.sticky.fixed {
    position: fixed;
    left: auto;
    right: auto;
}

/* Bound/Fixed Position */
.ui.sticky.bound.top,
.ui.sticky.fixed.top {
    top: 0;
    bottom: auto;
}
.ui.sticky.bound.bottom,
.ui.sticky.fixed.bottom {
    top: auto;
    bottom: 0;
}

/*******************************
            Types
*******************************/

.ui.native.sticky {
    position: sticky;
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
