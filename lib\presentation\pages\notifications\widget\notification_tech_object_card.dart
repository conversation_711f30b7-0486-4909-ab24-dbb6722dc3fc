import 'package:auto_size_text/auto_size_text.dart';
import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/EQUIP_INPUT_HEADER.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/FUNC_LOC_INPUT_HEADER.dart';
import 'package:eam/be/NOTIF_HEADER.dart';
import 'package:eam/helpers/equipment_helper.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/functional_location_helper.dart';
import 'package:eam/helpers/notification_helper.dart';
import 'package:eam/helpers/pa_helper.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/models/single_select_model.dart';
import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/presentation/eam_packages/dropdown/controller/controller.dart';
import 'package:eam/presentation/eam_packages/dropdown/drop_down_search.dart';
import 'package:eam/presentation/common_widgets/cancel_save_row.dart';
import 'package:eam/presentation/common_widgets/edit_view.dart';
import 'package:eam/presentation/pages/notifications/create_notification/widgets/helper.dart';
import 'package:eam/presentation/widgets/atoms_layer/general/texts.dart';
import 'package:eam/presentation/widgets/molecules_layer/general/general_helper_widgets.dart';
import 'package:eam/provider/notification/notification_field_value_provider.dart';
import 'package:eam/provider/notification/selected_notification_provider.dart';
import 'package:eam/screens/notification/add_edit_notification_page.dart';
import 'package:eam/screens/notification/add_edit_notification_page2.dart';

import 'package:eam/screens/other/single_selection_page.dart';
import 'package:eam/screens/param/route_param.dart';
import 'package:eam/presentation/pages/tech_objects/equipments/equipment_details_page.dart';
import 'package:eam/presentation/pages/tech_objects/floc/floc_details/floc_details_page.dart';
import 'package:eam/services/navigation_service.dart';
import 'package:eam/utils/app_dimension.dart';
import 'package:eam/utils/utils.dart';
import 'package:eam/widgets/details_card_widget.dart';
import 'package:eam/widgets/label_value_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:logger/Logger.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../notification_details/general/notification_general.dart';

class NotificationTechObjectCard extends StatefulWidget {
  final double SPACE_1 = 8;
  final double SPACE_2 = 16;
  final NotificationModel notificationModel;
  final bool isNewNotification;
  final bool showEditIconFromDbConfig;
  final Function() onTechObjectSave;
  final Function() onTechObjectCancel;
  final Function(String) updateWorkCenter;

  const NotificationTechObjectCard({
    Key? key,
    required this.notificationModel,
    required this.isNewNotification,
    required this.onTechObjectSave,
    required this.onTechObjectCancel,
    required this.updateWorkCenter,
    required this.showEditIconFromDbConfig,
  }) : super(key: key);

  @override
  State<NotificationTechObjectCard> createState() =>
      _NotificationTechObjectCardState();
}

class _NotificationTechObjectCardState
    extends State<NotificationTechObjectCard> {
  late TextEditingController functionalLocationController;
  late TextEditingController equipmentController;
  late NotificationModel techModel;
  late DropDownController dropDownController;
  NotificationModel notifModel = new NotificationModel();
  NotificationModel selectedNotifModel = new NotificationModel();

  late NOTIF_HEADER selectedNotifHeader;

  bool isEditedMode = false;
  bool showEditIcon = false;
  late final SinglePageFieldValueProvider fieldProvider;

  int _numOfTechDetails = 0;

  @override
  void initState() {
    fieldProvider =
        Provider.of<SinglePageFieldValueProvider>(context, listen: false);
    dropDownController =
        Provider.of<DropDownController>(context, listen: false);
    if (widget.isNewNotification) {
      isEditedMode = true;
      showEditIcon = false;
    } else {
      if (widget.showEditIconFromDbConfig == true) {
        showEditIcon = true;
      } else {
        showEditIcon = false;
      }
    }
    techModel = NotificationModel(
      functionalLocation: widget.notificationModel.functionalLocation,
      functionalLocationDesc: widget.notificationModel.functionalLocationDesc,
      equipment: widget.notificationModel.equipment,
      equipmentDesc: widget.notificationModel.equipmentDesc,
    );

    _initDefaultValues();
    super.initState();
  }

  _functionalLocationTitle() {
    return GeneralSubTitle(
        title: AppLocalizations.of(context)!.functionalLocation);
  }

  _equipmentTitle() {
    return GeneralSubTitle(
      title: AppLocalizations.of(context)!.equipment,
    );
  }

  _getFunctionalLocation() {
    return Container(
      child: Consumer<SelectedNotificationProvider>(
          builder: (context, pro, child) {
        return GeneralSubTitle2(
          title: functionalLocationController.text,
          onTap: () => _navigateToFlocDetailPage(),
          color: Theme.of(context).primaryColor,
        );
      }),
    );
  }

  _getEquipment() {
    return GeneralSubTitle2(
      title: equipmentController.text,
      onTap: () => _navigateToEquipmentDetailPage(),
      color: Theme.of(context).primaryColor,
    );
  }

  _titleRow() {
    return GeneralTitleRow(
      showEdit: widget.showEditIconFromDbConfig,
      title: AppLocalizations.of(context)!.technicalObjects,
      onTapEdit: () {
        NavigationService.pushNamed(EditView.routeName,
            arguments: EditViewArguments(
                title: AppLocalizations.of(context)!.edit,
                body: _editNotificationTechnicalObject(),
                bottomNavigation: _onEditSave()));
      },
    );
  }

  _editNotificationTechnicalObject() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: Dimensions.padding(context),
          child: CustomCardForTexFields(
            title: AppLocalizations.of(context)!.technicalObject,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // TabPortraitOrMobile.isTabPortraitOrMobile(context)
                //     ? _mobileOrTabPortraitView()
                //     : _webView(),

                // Flexible(
                //   child: AutoSizeText(
                //     "Edit Technical objects",
                //     maxLines: 1,
                //     style: AppStyles.headLine20_700.copyWith(height: 1.2),
                //   ),
                // ),
                EamDropDownSearch(
                  showScanner: true,
                  isRequiredField: true,
                  dropDownType: DropDownType.functionalLocation,
                  title: functionalLocationController.text,
                  labelName: AppLocalizations.of(context)!.functionalLocation,
                  orderEditIntent: SingleScreenIntent(
                      fieldFuncLocationId:
                          widget.notificationModel.functionalLocation,
                      fieldPMode: SingleSelectionPage.FUNCTIONAL_LIST_MODE,
                      orderSearch: 'SELECTED_FUNCTION_LOCATION'),
                  onChanged: (value) async {
                    if (value is SingleSelectModel) {
                      FUNC_LOC_HEADER? funcLocHeader =
                          await FunctionalLocationHelper.getFLocHeader(
                              floc: value.id);
                      if (funcLocHeader != null) {
                        widget.notificationModel.functionalLocation =
                            funcLocHeader.func_loc!;
                        widget.notificationModel.functionalLocationDesc =
                            funcLocHeader.shtxt!;
                        functionalLocationController.text =
                            '${funcLocHeader.func_loc!} (${funcLocHeader.shtxt!})';

                        ///SET EQUIPMENT AS EMPTY FIELD
                        widget.notificationModel.equipment = '';
                        widget.notificationModel.equipmentDesc = '';
                        equipmentController.text = '';

                        // set the equipment as empty
                        dropDownController.removeString(
                            dropDownType: DropDownType.equipment);
                        //set other field value
                        if (!Utils.isNullOrEmpty(
                            funcLocHeader.org_plnt_wrk_cntr)) {
                          widget.notificationModel.mainPlanningPlant =
                              funcLocHeader.org_plnt_wrk_cntr!;
                          widget.notificationModel.mainPlanningPlantDesc =
                              funcLocHeader.loc_main_plant_desc!;
                          fieldProvider.updateMaintenancePlant(
                              plant: funcLocHeader.org_plnt_wrk_cntr! +
                                  " (" +
                                  funcLocHeader.loc_main_plant_desc! +
                                  ")");
                          // update maintenancePlant
                          dropDownController.updateString(
                              dropDownType: DropDownType.maintenancePlant,
                              text: funcLocHeader.org_plnt_wrk_cntr! +
                                  " (" +
                                  funcLocHeader.loc_main_plant_desc! +
                                  ")");
                        } else {
                          widget.notificationModel.mainPlanningPlant = '';
                          widget.notificationModel.mainPlanningPlantDesc = '';
                          fieldProvider.updateMaintenancePlant(plant: '');
                          dropDownController.removeString(
                              dropDownType: DropDownType.maintenancePlant);
                        }
                        if (!Utils.isNullOrEmpty(
                            funcLocHeader.org_plnt_wrk_cntr)) {
                          widget.notificationModel.workCenterPlant =
                              funcLocHeader.org_plnt_wrk_cntr!;
                          //binding.workCenterPlant.setText(funcLocHeader.getORG_PLNT_WRK_CNTR());
                          fieldProvider.updateWorkCenterPlant(
                              workCenterPlant:
                                  funcLocHeader.org_plnt_wrk_cntr!);
                          dropDownController.updateString(
                              dropDownType: DropDownType.workCenter,
                              text: funcLocHeader.org_plnt_wrk_cntr!);
                        } else {
                          widget.notificationModel.workCenterPlant = '';
                          fieldProvider.updateWorkCenterPlant(
                              workCenterPlant: '');
                          dropDownController.removeString(
                              dropDownType: DropDownType.workCenter);
                        }
                        if (!Utils.isNullOrEmpty(
                            funcLocHeader.org_main_wrk_cntr)) {
                          widget.notificationModel.workCenter =
                              funcLocHeader.org_main_wrk_cntr!;
                          widget.notificationModel.workCenterDesc =
                              funcLocHeader.org_main_wrk_cntr_desc!;
                          fieldProvider.updateWorkCenter(
                              workCenter: funcLocHeader.org_main_wrk_cntr! +
                                  " (" +
                                  funcLocHeader.org_main_wrk_cntr_desc! +
                                  ")");

                          dropDownController.updateString(
                              dropDownType: DropDownType.workCenter,
                              text: funcLocHeader.org_main_wrk_cntr! +
                                  " (" +
                                  funcLocHeader.org_main_wrk_cntr_desc! +
                                  ")");
                        } else {
                          widget.notificationModel.workCenter = '';
                          widget.notificationModel.workCenterDesc = '';
                          fieldProvider.updateWorkCenter(workCenter: '');
                          dropDownController.removeString(
                              dropDownType: DropDownType.workCenter);
                        }
                        setState(() {});
                      }
                    }
                  },
                ),

                // EamDropDownSearch(
                //   dropDownType: DropDownType.functionalLocation,
                //   labelName: AppLocalizations.of(context)!.funcLocation,
                //   title: functionalLocationController.text.toString(),
                //   orderEditIntent: SingleScreenIntent(
                //       fieldFuncLocationId:
                //           widget.notificationModel.functionalLocation,
                //       fieldPMode: SingleSelectionPage.FUNCTIONAL_LIST_MODE,
                //       orderSearch: 'SELECTED_FUNCTION_LOCATION'),
                //   onChanged: (value) async {
                //     if (value is SingleSelectModel) {
                //       FUNC_LOC_HEADER? funcLocHeader =
                //           await FunctionalLocationHelper.getFLocHeader(
                //               floc: value.id);
                //       if (funcLocHeader != null) {
                //         widget.notificationModel.functionalLocation =
                //             funcLocHeader.func_loc!;
                //         widget.notificationModel.functionalLocationDesc =
                //             funcLocHeader.shtxt!;
                //         functionalLocationController.text =
                //             '${funcLocHeader.func_loc!} (${funcLocHeader.shtxt!})';

                //         ///SET EQUIPMENT AS EMPTY FIELD
                //         widget.notificationModel.equipment = '';
                //         widget.notificationModel.equipmentDesc = '';
                //         equipmentController.text = '';
                //         //set other field value
                //         if (!Utils.isNullOrEmpty(
                //             funcLocHeader.loc_main_plant)) {
                //           widget.notificationModel.mainPlanningPlant =
                //               funcLocHeader.loc_main_plant!;
                //           widget.notificationModel.mainPlanningPlantDesc =
                //               funcLocHeader.loc_main_plant_desc!;
                //           fieldProvider.updateMaintenancePlant(
                //               plant: funcLocHeader.loc_main_plant! +
                //                   " (" +
                //                   funcLocHeader.loc_main_plant_desc! +
                //                   ")");
                //         } else {
                //           widget.notificationModel.mainPlanningPlant = '';
                //           widget.notificationModel.mainPlanningPlantDesc = '';
                //           fieldProvider.updateMaintenancePlant(plant: '');
                //         }
                //         if (!Utils.isNullOrEmpty(
                //             funcLocHeader.org_plnt_wrk_cntr)) {
                //           widget.notificationModel.workCenterPlant =
                //               funcLocHeader.org_plnt_wrk_cntr!;
                //           //binding.workCenterPlant.setText(funcLocHeader.getORG_PLNT_WRK_CNTR());
                //           fieldProvider.updateWorkCenterPlant(
                //               workCenterPlant:
                //                   funcLocHeader.org_plnt_wrk_cntr!);
                //         } else {
                //           widget.notificationModel.workCenterPlant = '';
                //           fieldProvider.updateWorkCenterPlant(
                //               workCenterPlant: '');
                //         }
                //         if (!Utils.isNullOrEmpty(
                //             funcLocHeader.org_main_wrk_cntr)) {
                //           widget.notificationModel.workCenter =
                //               funcLocHeader.org_main_wrk_cntr!;
                //           widget.notificationModel.workCenterDesc =
                //               funcLocHeader.org_main_wrk_cntr_desc!;
                //           fieldProvider.updateWorkCenter(
                //               workCenter: funcLocHeader.org_main_wrk_cntr! +
                //                   " (" +
                //                   funcLocHeader.org_main_wrk_cntr_desc! +
                //                   ")");
                //         } else {
                //           widget.notificationModel.workCenter = '';
                //           widget.notificationModel.workCenterDesc = '';
                //           fieldProvider.updateWorkCenter(workCenter: '');
                //         }
                //         setState(() {});
                //       }
                //     }
                //   },
                // ),

                SizedBox(
                  height: 20,
                ),

                // Flexible(
                //   child: AutoSizeText(
                //     AppLocalizations.of(context)!.equipment,
                //     maxLines: 1,
                //     style: TextStyle(
                //         color: HexColor("#0F1419"),
                //         fontWeight: FontWeight.w600,
                //         fontSize: 14),
                //   ),
                // ),
                // SizedBox(
                //   height: 8,
                // ),

                EamDropDownSearch(
                  showScanner: true,
                  dropDownType: DropDownType.equipment,
                  title: equipmentController.text,
                  labelName: AppLocalizations.of(context)!.equipment,
                  orderEditIntent: SingleScreenIntent(
                      fieldFuncLocationId:
                          widget.notificationModel.functionalLocation,
                      fieldPMode: SingleSelectionPage.EQUIPMENT_MODE,
                      orderSearch: 'SELECTED_EQUIPMENT'),
                  onChanged: (value) async {
                    if (value is SingleSelectModel) {
                      EQUIP_HEADER? equipHeader =
                          await EquipmentHelper.getEquipmentHeaderForDetail(
                              equipNo: value.id);
                      if (equipHeader != null) {
                        widget.notificationModel.equipment = equipHeader.equnr!;
                        widget.notificationModel.equipmentDesc =
                            equipHeader.shtxt!;

                        equipmentController.text =
                            '${equipHeader.equnr!} (${equipHeader.shtxt!})';

                        widget.notificationModel.functionalLocation =
                            (equipHeader.super_func_loc!);
                        widget.notificationModel.functionalLocationDesc =
                            (equipHeader.super_func_loc_desc!);
                        functionalLocationController.text =
                            equipHeader.super_func_loc! +
                                " (" +
                                equipHeader.super_func_loc_desc! +
                                ")";

                        //update floc
                        dropDownController.updateString(
                            dropDownType: DropDownType.functionalLocation,
                            text: equipHeader.super_func_loc! +
                                " (" +
                                equipHeader.super_func_loc_desc! +
                                ")");
                        if (!Utils.isNullOrEmpty(
                            equipHeader.org_plnt_wrk_cntr)) {
                          widget.notificationModel.mainPlanningPlant =
                              (equipHeader.org_plnt_wrk_cntr!);
                          widget.notificationModel.mainPlanningPlantDesc =
                              (equipHeader.loc_main_plant_desc!);
                          fieldProvider.updateMaintenancePlant(
                              plant: equipHeader.org_plnt_wrk_cntr! +
                                  " (" +
                                  equipHeader.loc_main_plant_desc! +
                                  ")");
                          dropDownController.updateString(
                              dropDownType: DropDownType.maintenancePlant,
                              text: equipHeader.org_plnt_wrk_cntr! +
                                  " (" +
                                  equipHeader.loc_main_plant_desc! +
                                  ")");
                        } else {
                          widget.notificationModel.mainPlanningPlant = '';
                          widget.notificationModel.mainPlanningPlantDesc = '';
                          fieldProvider.updateMaintenancePlant(plant: '');
                          dropDownController.removeString(
                              dropDownType: DropDownType.maintenancePlant);
                        }
                        if (!Utils.isNullOrEmpty(
                            equipHeader.org_plnt_wrk_cntr)) {
                          widget.notificationModel.workCenterPlant =
                              (equipHeader.org_plnt_wrk_cntr!);
                          fieldProvider.updateWorkCenterPlant(
                              workCenterPlant: equipHeader.org_plnt_wrk_cntr!);
                          dropDownController.updateString(
                              dropDownType: DropDownType.workCenter,
                              text: equipHeader.org_plnt_wrk_cntr!);

                          //binding.workCenterPlant.setText(equipHeader.getORG_PLNT_WRK_CNTR());
                        } else {
                          widget.notificationModel.workCenterPlant = '';
                          fieldProvider.updateWorkCenterPlant(
                              workCenterPlant: '');
                          dropDownController.removeString(
                              dropDownType: DropDownType.workCenter);
                        }
                        if (!Utils.isNullOrEmpty(
                            equipHeader.org_plnt_wrk_cntr)) {
                          widget.notificationModel.workCenter =
                              (equipHeader.org_main_wrk_cntr!);
                          widget.notificationModel.workCenterDesc =
                              (equipHeader.org_main_wrk_cntr_desc!);
                          fieldProvider.updateWorkCenter(
                              workCenter: equipHeader.org_main_wrk_cntr! +
                                  " (" +
                                  equipHeader.org_main_wrk_cntr_desc! +
                                  ")");

                          dropDownController.updateString(
                              dropDownType: DropDownType.workCenter,
                              text: equipHeader.org_main_wrk_cntr! +
                                  " (" +
                                  equipHeader.org_main_wrk_cntr_desc! +
                                  ")");
                          /* binding.workCenter.setText(equipHeader.getORG_MAIN_WRK_CNTR() +
                    " (" +
                    equipHeader.getORG_MAIN_WRK_CNTR_DESC() +
                    ")");*/
                        } else {
                          widget.notificationModel.workCenter = '';
                          widget.notificationModel.workCenterDesc = '';
                          fieldProvider.updateWorkCenter(workCenter: '');
                          dropDownController.removeString(
                              dropDownType: DropDownType.workCenter);
                        }
                        setState(() {});
                      }
                    }
                  },
                ),

                // EamDropDownSearch(
                //   dropDownType: DropDownType.equipment,
                //   title: equipmentController.text.toString(),
                //   orderEditIntent: SingleScreenIntent(
                //       fieldFuncLocationId:
                //           widget.notificationModel.functionalLocation,
                //       fieldPMode: SingleSelectionPage.EQUIPMENT_MODE,
                //       orderSearch: 'SELECTED_EQUIPMENT'),
                //   onChanged: (value) async {
                //     if (value is SingleSelectModel) {
                //       EQUIP_HEADER? equipHeader =
                //           await EquipmentHelper.getEquipmentHeaderForDetail(
                //               equipNo: value.id);
                //       if (equipHeader != null) {
                //         widget.notificationModel.equipment = equipHeader.equnr!;
                //         widget.notificationModel.equipmentDesc =
                //             equipHeader.shtxt!;
                //         equipmentController.text =
                //             '${equipHeader.equnr!} (${equipHeader.shtxt!})';

                //         widget.notificationModel.functionalLocation =
                //             (equipHeader.super_func_loc!);
                //         widget.notificationModel.functionalLocationDesc =
                //             (equipHeader.super_func_loc_desc!);
                //         functionalLocationController.text =
                //             equipHeader.super_func_loc! +
                //                 " (" +
                //                 equipHeader.super_func_loc_desc! +
                //                 ")";
                //         if (!Utils.isNullOrEmpty(equipHeader.loc_main_plant)) {
                //           widget.notificationModel.mainPlanningPlant =
                //               (equipHeader.loc_main_plant!);
                //           widget.notificationModel.mainPlanningPlantDesc =
                //               (equipHeader.loc_main_plant_desc!);
                //           fieldProvider.updateMaintenancePlant(
                //               plant: equipHeader.loc_main_plant! +
                //                   " (" +
                //                   equipHeader.loc_main_plant_desc! +
                //                   ")");
                //         } else {
                //           widget.notificationModel.mainPlanningPlant = '';
                //           widget.notificationModel.mainPlanningPlantDesc = '';
                //           fieldProvider.updateMaintenancePlant(plant: '');
                //         }
                //         if (!Utils.isNullOrEmpty(
                //             equipHeader.org_plnt_wrk_cntr)) {
                //           widget.notificationModel.workCenterPlant =
                //               (equipHeader.org_plnt_wrk_cntr!);
                //           fieldProvider.updateWorkCenterPlant(
                //               workCenterPlant: equipHeader.org_plnt_wrk_cntr!);

                //           //binding.workCenterPlant.setText(equipHeader.getORG_PLNT_WRK_CNTR());
                //         } else {
                //           widget.notificationModel.workCenterPlant = '';
                //           fieldProvider.updateWorkCenterPlant(
                //               workCenterPlant: '');
                //         }
                //         if (!Utils.isNullOrEmpty(
                //             equipHeader.org_plnt_wrk_cntr)) {
                //           widget.notificationModel.workCenter =
                //               (equipHeader.org_main_wrk_cntr!);
                //           widget.notificationModel.workCenterDesc =
                //               (equipHeader.org_main_wrk_cntr_desc!);
                //           fieldProvider.updateWorkCenter(
                //               workCenter: equipHeader.org_main_wrk_cntr! +
                //                   " (" +
                //                   equipHeader.org_main_wrk_cntr_desc! +
                //                   ")");
                //           /* binding.workCenter.setText(equipHeader.getORG_MAIN_WRK_CNTR() +
                //   " (" +
                //   equipHeader.getORG_MAIN_WRK_CNTR_DESC() +
                //   ")");*/
                //         } else {
                //           widget.notificationModel.workCenter = '';
                //           widget.notificationModel.workCenterDesc = '';
                //           fieldProvider.updateWorkCenter(workCenter: '');
                //         }
                //         setState(() {});
                //       }
                //     }
                //   },
                // ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _onEditSave() {
    return CancelSaveButtonsRow(cancelOnTap: () {
      NavigationService.goBack();
    }, saveOnTap: () async {
      widget.onTechObjectSave();
      setState(() {});

      Navigator.of(context).pop();
    });
  }

  _checkIsMedium() {
    if (MediaQuery.of(context).size.width > 850) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleRow(),
        18.0.spaceY,
        PlatformDetails.isMobileScreen(context) ||
                PlatformDetails.isTabPortraitScreen(context)
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 45,
                    child: _funLoc(),
                  ),
                  24.0.spaceY,
                  Container(
                    height: 45,
                    child: _equipment(),
                  ),
                ],
              )
            : Container(
                height: 45,
                child: Row(
                  children: [
                    Expanded(child: _funLoc()),
                    Expanded(child: _equipment()),
                    _checkIsMedium() ? SizedBox() : Expanded(child: SizedBox())
                  ],
                ),
              ),
      ],
    );
    // Column(
    //   crossAxisAlignment: CrossAxisAlignment.start,
    //   children: [
    //     _titleRow(),
    //     SizedBox(
    //       height: 18,
    //     ),
    //     Container(
    //       height: 45,
    //       // color: Colors.amber,
    //       child: Row(
    //         children: [
    //           Expanded(
    //             child: Container(
    //               // color: Colors.black,
    //               // width: double.maxFinite,
    //               child: Column(
    //                 crossAxisAlignment: CrossAxisAlignment.start,
    //                 children: [
    //                   _functionalLocationTitle(),
    //                   Spacer(),
    //                   _getFunctionalLocation()
    //                 ],
    //               ),
    //             ),
    //           ),
    //           // Expanded(
    //           //   child: SizedBox(
    //           //     child: _checkIsMedium()
    //           //         ? SizedBox()
    //           //         : Column(
    //           //             crossAxisAlignment: CrossAxisAlignment.start,
    //           //             children: [
    //           //                 _equipmentTitle(),
    //           //                 Spacer(),
    //           //                 _getEquipment()
    //           //               ]),
    //           //   ),
    //           // ),
    //           // _checkIsMedium() ? SizedBox() : Expanded(child: SizedBox())
    //         ],
    //       ),
    //     ),
    //     SizedBox(height: _checkIsMedium() ? 24 : 0),
    //     !_checkIsMedium()
    //         ? SizedBox()
    //         : Container(
    //             height: 45,
    //             //color: Colors.amber,
    //             child: SizedBox(
    //               child: Column(
    //                 crossAxisAlignment: CrossAxisAlignment.start,
    //                 children: [
    //                   _equipmentTitle(),
    //                   Spacer(),
    //                   _getEquipment(),
    //                 ],
    //               ),
    //             ),
    //           ),
    //     // DetailsCardWidget(
    //     //   title: AppLocalizations.of(context)!.technicalObjects,
    //     //   content: _getCardContent(),
    //     //   isEditable: showEditIcon,
    //     //   onTapEdit: () {
    //     //     setState(() {
    //     //       isEditedMode = true;
    //     //     });
    //     //     //_navigateToEdit(OrderEditPage.ORDER_MODE_EDIT_GENERAL);
    //     //   },
    //     //   onTapCancel: () {
    //     //     isEditedMode = false;
    //     //     _initDefaultValues();
    //     //     widget.onTechObjectCancel();
    //     //     setState(() {});
    //     //   },
    //     //   onTapSave: () {
    //     //     isEditedMode = false;
    //     //     widget.onTechObjectSave();
    //     //     setState(() {});
    //     //   },
    //     //   validateCardInput: () {
    //     //     return validateNotificationTechObject(
    //     //         viewModel: widget.notificationModel, context: context);
    //     //   },
    //     // ),
    //   ],
    // );
  }

  _funLoc() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _functionalLocationTitle(),
        Spacer(),
        _getFunctionalLocation()
      ],
    );
  }

  _equipment() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _equipmentTitle(),
        Spacer(),
        _getEquipment(),
      ],
    );
  }

  _navigateToSelectionEquipment() {
    Navigator.pushNamed(
      context,
      SingleSelectionPage.routeName,
      arguments: SingleScreenIntent(
          fieldFuncLocationId: widget.notificationModel.functionalLocation,
          fieldPMode: SingleSelectionPage.EQUIPMENT_MODE,
          orderSearch: 'SELECTED_EQUIPMENT'),
    ).then((value) async {
      if (value is SingleSelectModel) {
        EQUIP_HEADER? equipHeader =
            await EquipmentHelper.getEquipmentHeaderForDetail(
                equipNo: value.id);
        if (equipHeader != null) {
          widget.notificationModel.equipment = equipHeader.equnr!;
          widget.notificationModel.equipmentDesc = equipHeader.shtxt!;
          equipmentController.text =
              '${equipHeader.equnr!} (${equipHeader.shtxt!})';

          widget.notificationModel.functionalLocation =
              (equipHeader.super_func_loc!);
          widget.notificationModel.functionalLocationDesc =
              (equipHeader.super_func_loc_desc!);
          functionalLocationController.text = equipHeader.super_func_loc! +
              " (" +
              equipHeader.super_func_loc_desc! +
              ")";
          if (!Utils.isNullOrEmpty(equipHeader.loc_main_plant)) {
            widget.notificationModel.mainPlanningPlant =
                (equipHeader.loc_main_plant!);
            widget.notificationModel.mainPlanningPlantDesc =
                (equipHeader.loc_main_plant_desc!);
            fieldProvider.updateMaintenancePlant(
                plant: equipHeader.loc_main_plant! +
                    " (" +
                    equipHeader.loc_main_plant_desc! +
                    ")");
          } else {
            widget.notificationModel.mainPlanningPlant = '';
            widget.notificationModel.mainPlanningPlantDesc = '';
            fieldProvider.updateMaintenancePlant(plant: '');
          }
          if (!Utils.isNullOrEmpty(equipHeader.org_plnt_wrk_cntr)) {
            widget.notificationModel.workCenterPlant =
                (equipHeader.org_plnt_wrk_cntr!);
            fieldProvider.updateWorkCenterPlant(
                workCenterPlant: equipHeader.org_plnt_wrk_cntr!);

            //binding.workCenterPlant.setText(equipHeader.getORG_PLNT_WRK_CNTR());
          } else {
            widget.notificationModel.workCenterPlant = '';
            fieldProvider.updateWorkCenterPlant(workCenterPlant: '');
          }
          if (!Utils.isNullOrEmpty(equipHeader.org_plnt_wrk_cntr)) {
            widget.notificationModel.workCenter =
                (equipHeader.org_main_wrk_cntr!);
            widget.notificationModel.workCenterDesc =
                (equipHeader.org_main_wrk_cntr_desc!);
            fieldProvider.updateWorkCenter(
                workCenter: equipHeader.org_main_wrk_cntr! +
                    " (" +
                    equipHeader.org_main_wrk_cntr_desc! +
                    ")");
            /* binding.workCenter.setText(equipHeader.getORG_MAIN_WRK_CNTR() +
                  " (" +
                  equipHeader.getORG_MAIN_WRK_CNTR_DESC() +
                  ")");*/
          } else {
            widget.notificationModel.workCenter = '';
            widget.notificationModel.workCenterDesc = '';
            fieldProvider.updateWorkCenter(workCenter: '');
          }
          setState(() {});
        }
      }
    });
  }

  _navigateToSelectionFLoc() {
    Navigator.pushNamed(
      context,
      SingleSelectionPage.routeName,
      arguments: SingleScreenIntent(
          fieldFuncLocationId: widget.notificationModel.functionalLocation,
          fieldPMode: SingleSelectionPage.FUNCTIONAL_LIST_MODE,
          orderSearch: 'SELECTED_FUNCTION_LOCATION'),
    ).then((value) async {
      if (value is SingleSelectModel) {
        FUNC_LOC_HEADER? funcLocHeader =
            await FunctionalLocationHelper.getFLocHeader(floc: value.id);
        if (funcLocHeader != null) {
          widget.notificationModel.functionalLocation = funcLocHeader.func_loc!;
          widget.notificationModel.functionalLocationDesc =
              funcLocHeader.shtxt!;
          functionalLocationController.text =
              '${funcLocHeader.func_loc!} (${funcLocHeader.shtxt!})';

          ///SET EQUIPMENT AS EMPTY FIELD
          widget.notificationModel.equipment = '';
          widget.notificationModel.equipmentDesc = '';
          equipmentController.text = '';
          //set other field value
          if (!Utils.isNullOrEmpty(funcLocHeader.loc_main_plant)) {
            widget.notificationModel.mainPlanningPlant =
                funcLocHeader.loc_main_plant!;
            widget.notificationModel.mainPlanningPlantDesc =
                funcLocHeader.loc_main_plant_desc!;
            fieldProvider.updateMaintenancePlant(
                plant: funcLocHeader.loc_main_plant! +
                    " (" +
                    funcLocHeader.loc_main_plant_desc! +
                    ")");
          } else {
            widget.notificationModel.mainPlanningPlant = '';
            widget.notificationModel.mainPlanningPlantDesc = '';
            fieldProvider.updateMaintenancePlant(plant: '');
          }
          if (!Utils.isNullOrEmpty(funcLocHeader.org_plnt_wrk_cntr)) {
            widget.notificationModel.workCenterPlant =
                funcLocHeader.org_plnt_wrk_cntr!;
            //binding.workCenterPlant.setText(funcLocHeader.getORG_PLNT_WRK_CNTR());
            fieldProvider.updateWorkCenterPlant(
                workCenterPlant: funcLocHeader.org_plnt_wrk_cntr!);
          } else {
            widget.notificationModel.workCenterPlant = '';
            fieldProvider.updateWorkCenterPlant(workCenterPlant: '');
          }
          if (!Utils.isNullOrEmpty(funcLocHeader.org_main_wrk_cntr)) {
            widget.notificationModel.workCenter =
                funcLocHeader.org_main_wrk_cntr!;
            widget.notificationModel.workCenterDesc =
                funcLocHeader.org_main_wrk_cntr_desc!;
            fieldProvider.updateWorkCenter(
                workCenter: funcLocHeader.org_main_wrk_cntr! +
                    " (" +
                    funcLocHeader.org_main_wrk_cntr_desc! +
                    ")");
          } else {
            widget.notificationModel.workCenter = '';
            widget.notificationModel.workCenterDesc = '';
            fieldProvider.updateWorkCenter(workCenter: '');
          }
          setState(() {});
        }
      }
    });
  }

  void _initDefaultValues() {
    functionalLocationController = TextEditingController(
      text:
          '${techModel.functionalLocation} ${techModel.functionalLocationDesc.isNotEmpty ? '(${techModel.functionalLocationDesc})' : ''}',
    );
    equipmentController = TextEditingController(
      text:
          '${techModel.equipment} ${techModel.equipmentDesc.isNotEmpty ? '(${techModel.equipmentDesc})' : ''}',
    );
  }

  _getCardContent() {
    return Column(
      children: [
        LabelValueWidget(
          label: AppLocalizations.of(context)!.functionalLocation,
          isValueEditable: true,
          isValueReadOnly: true,
          isValueLink: true,
          disableBorder: !isEditedMode,
          valueController: functionalLocationController,
          onValueClear: () {
            widget.notificationModel.functionalLocation = '';
            widget.notificationModel.functionalLocationDesc = '';
            functionalLocationController.text = '';
            widget.notificationModel.equipment = '';
            widget.notificationModel.equipmentDesc = '';
            equipmentController.text = '';
            fieldProvider.updateWorkCenter(workCenter: '');
            fieldProvider.updateMaintenancePlant(plant: '');
            setState(() {});
          },
          onValueTap: () => {
            isEditedMode
                ? _navigateToSelectionFLoc()
                : _navigateToFlocDetailPage()
          },
        ),
        SizedBox(
          height: widget.SPACE_2,
        ),
        LabelValueWidget(
          label: AppLocalizations.of(context)!.equipment,
          isValueEditable: true,
          isValueReadOnly: true,
          isValueLink: true,
          disableBorder: !isEditedMode,
          valueController: equipmentController,
          onValueClear: () {
            widget.notificationModel.equipment = '';
            widget.notificationModel.equipmentDesc = '';
            equipmentController.text = '';
            setState(() {});
          },
          onValueTap: () => {
            isEditedMode
                ? _navigateToSelectionEquipment()
                : _navigateToEquipmentDetailPage()
          },
        ),
        SizedBox(
          height: widget.SPACE_2,
        ),
      ],
    );
  }

  _navigateToFlocDetailPage() async {
    final String floc = techModel.functionalLocation;
    if (Utils.isNullOrEmpty(floc)) {
      return;
    }

    FUNC_LOC_HEADER? flocHeader =
        await FunctionalLocationHelper.getFuncLocHeader(funcLoc: floc);
    if (flocHeader == null) {
      await downloadFunctionalLocation(floc: floc);
      return;
    } else {
      // Future.microtask(() => _incrementTechontap());
      NavigationService.pushNamed(
        FlocDetailsPage.routeName,
        arguments: techModel.functionalLocation,
      ).then((value) async {
        // Future.microtask(
        //   () => _reloadAll(context),
        // );
      });
    }
  }

  _incrementTechontap() {
    if (_numOfTechDetails == 0) {
      selectedNotifModel = notifModel;
      selectedNotifHeader =
          Provider.of<SelectedNotificationProvider>(context, listen: false)
              .notifHeader;
    }
    _numOfTechDetails++;
    print(_numOfTechDetails);
  }

  _reloadAll(BuildContext context) async {
    _numOfTechDetails--;
    print(_numOfTechDetails);
    if (_numOfTechDetails == 0) {
      notifModel = selectedNotifModel;
      context.read<SelectedNotificationProvider>().notifHeader =
          selectedNotifHeader;
      // Provider.of<SelectedNotificationProvider>(context, listen: false)
      //     .notifHeader = selectedNotifHeader;
      await Provider.of<SelectedNotificationProvider>(context)
          .getNotificationModel(
              context,
              isCreateNewNotification: false,
              notificationParam: NotificationParam(
                  type: AddEditNotificationPage2.NOTIFICATION_MODE_EDIT));
      await Provider.of<SelectedNotificationProvider>(context, listen: false)
          .getNotificationByNotificationNo(
              notifNo: Provider.of<SelectedNotificationProvider>(context,
                      listen: false)
                  .selectedNotifNo);
    }
  }

  // _updateEditable() async {
  //   var _notif = await NotificationHelper.getNotifHeader(
  //       notifNo:
  //           Provider.of<SelectedNotificationProvider>(context, listen: false)
  //               .selectedNotifNo);
  //   if (_notif != null) {
  //     var res = await NotificationHelper.isEditable(notifHeader: _notif);
  //     Provider.of<SelectedNotificationProvider>(context, listen: false)
  //         .isEditable();
  //   }
  // }

  _navigateToEquipmentDetailPage() async {
    final String equipNo = techModel.equipment;
    if (Utils.isNullOrEmpty(equipNo)) {
      return;
    }

    EQUIP_HEADER? equipHeader =
        await EquipmentHelper.getEquipmentHeaderForDetail(equipNo: equipNo);

    if (equipHeader == null) {
      await downloadEquipment(equipNo: equipNo);
      return;
    } else {
      // Future.microtask(() => _incrementTechontap());
      NavigationService.pushNamed(
        EquipmentDetailsPage.routeName,
        arguments: EquipmentParam(eqno: techModel.equipment),
      ).then((value) async {
        // Future.microtask(
        //   () => _reloadAll(context),
        // );
      });
    }
  }

  Future<void> downloadEquipment({required String equipNo}) async {
    try {
      EQUIP_INPUT_HEADER equipHeader = EQUIP_INPUT_HEADER(
        equip_no: equipNo,
      );

      await getEquipments(searchHeader: equipHeader);
    } catch (e) {
      Logger.logError('sourceClass', 'sourceMethod', e.toString());
    }
  }

  Future<void> getEquipments({required EQUIP_INPUT_HEADER searchHeader}) async {
    try {
      UIHelper.showEamProgressDialog2(
        context,
        title: AppLocalizations.of(context)!.refreshInitiatedString,
        showCancelIcon: false,
        barrierDismissible: false,
      );
      Result result = await PAHelper.getEquipmentsInSyncMode(
        equipInputHeader: searchHeader,
      );
      if (result.statusCode == Status.httpBadRequest ||
          result.statusCode == Status.httpNotFound) {
        // Navigator.of(context, rootNavigator: true).pop();
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
          UIHelper.showEamDialog2(
            context,
            title: AppLocalizations.of(context)!.alertString,
            description: result.body['InfoMessage'][0]['message'],
            dismissible: false,
            positiveActionLabel: AppLocalizations.of(context)!.okayString,
            onPositiveClickListener: () => UIHelper.closeDialog(context),
          );
        }
      } else if (result.statusCode == Status.httpOk ||
          result.statusCode == Status.httpCreated) {
        // Navigator.of(context, rootNavigator: true).pop();

        if (result.body['InfoMessage'] != null) {
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
            UIHelper.showEamDialog2(
              context,
              title: AppLocalizations.of(context)!.alertString,
              description: result.body['InfoMessage'][0]['message'],
              dismissible: false,
              positiveActionLabel: AppLocalizations.of(context)!.okayString,
              onPositiveClickListener: () => UIHelper.closeDialog(context),
            );
          } else {
            UIHelper.showEamDialog2(
              context,
              title: AppLocalizations.of(context)!.infoString,
              description: result.body['InfoMessage'][0]['message'],
              dismissible: false,
              positiveActionLabel: AppLocalizations.of(context)!.okayString,
              onPositiveClickListener: () {
                UIHelper.closeDialog(context);
              },
            );
          }
        } else {
          UIHelper.closeDialog(context);
          UIHelper.showSnackBar(
            context,
            message:
                AppLocalizations.of(context)!.equipmentDownloadSuccessfully,
          );
        }
      }
    } catch (e) {
      UIHelper.closeDialog(context);
      UIHelper.showSnackBar(
        context,
        message: e.toString(),
      );
    }
  }

  Future<void> downloadFunctionalLocation({required String floc}) async {
    try {
      FUNC_LOC_INPUT_HEADER flocInputHeader = FUNC_LOC_INPUT_HEADER(
        fl_val: floc,
      );

      await getFunctionalLocation(searchHeader: flocInputHeader);
    } catch (e) {
      Logger.logError('sourceClass', 'sourceMethod', e.toString());
    }
  }

  Future<void> getFunctionalLocation(
      {required FUNC_LOC_INPUT_HEADER searchHeader}) async {
    try {
      UIHelper.showEamProgressDialog2(
        context,
        title: AppLocalizations.of(context)!.refreshInitiatedString,
        showCancelIcon: false,
        barrierDismissible: false,
      );
      Result result = await PAHelper.getFunctionalLocationInSyncMode(
        functionalLocation: searchHeader,
      );
      if (result.statusCode == Status.httpBadRequest ||
          result.statusCode == Status.httpNotFound) {
        // Navigator.of(context, rootNavigator: true).pop();
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
          UIHelper.showEamDialog2(context,
              title: AppLocalizations.of(context)!.alertString,
              description: result.body['InfoMessage'][0]['message'],
              dismissible: false,
              positiveActionLabel: AppLocalizations.of(context)!.okayString,
              onPositiveClickListener: () => UIHelper.closeDialog(context));
        }
      } else if (result.statusCode == Status.httpOk ||
          result.statusCode == Status.httpCreated) {
        // Navigator.of(context, rootNavigator: true).pop();

        if (result.body['InfoMessage'] != null) {
          if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
            UIHelper.showEamDialog2(context,
                title: AppLocalizations.of(context)!.alertString,
                description: result.body['InfoMessage'][0]['message'],
                dismissible: false,
                positiveActionLabel: AppLocalizations.of(context)!.okayString,
                onPositiveClickListener: () => UIHelper.closeDialog(context));
          } else {
            UIHelper.showEamDialog2(
              context,
              title: AppLocalizations.of(context)!.infoString,
              description: result.body['InfoMessage'][0]['message'],
              dismissible: false,
              positiveActionLabel: AppLocalizations.of(context)!.okayString,
              onPositiveClickListener: () {
                UIHelper.closeDialog(context);
              },
            );
          }
        } else {
          UIHelper.closeDialog(context);
          UIHelper.showSnackBar(
            context,
            message: AppLocalizations.of(context)!
                .functionalLocationDownloadSuccessfully,
          );
        }
      }
    } catch (e) {
      UIHelper.closeDialog(context);
      UIHelper.showSnackBar(
        context,
        message: e.toString(),
      );
    }
  }
}
