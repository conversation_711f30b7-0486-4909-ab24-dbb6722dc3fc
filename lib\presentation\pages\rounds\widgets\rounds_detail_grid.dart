import 'package:auto_size_text/auto_size_text.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/rounds_helper.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/rounds/round_hostory.dart';
import 'package:eam/presentation/pages/rounds/widgets/history/roundHistory.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';

import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/provider/round/select_round_provider.dart';
import 'package:eam/provider/round_detail/round_detail_provider.dart';
import 'package:eam/models/rounds/rounds_list_model.dart';
import 'package:eam/utils/app_color.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/enum.dart';

import 'package:eam/widgets/common_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

const int _roundGridWidth = 483;

class RoundsDetailGrid extends StatefulWidget {
  final isMobile;
  final String status;
  final String roundId;
  const RoundsDetailGrid(
      {Key? key, this.isMobile, required this.status, required this.roundId})
      : super(key: key);

  @override
  State<RoundsDetailGrid> createState() => _RoundsDetailGridState();
}

class _RoundsDetailGridState extends State<RoundsDetailGrid> {
  loadRounds(BuildContext context, String status) {
    var roundDetailProvider =
        Provider.of<RoundsDetailProvider>(context, listen: false);
    if (status == RoundStatus.All.name) {
      roundDetailProvider.getAllRoundObjectList(
          roundId: widget.roundId, filterCharSeq: '');
    } else {
      roundDetailProvider.getAllRoundObjectList(
          roundId: widget.roundId, filterCharSeq: Constants.ENABLED);
    }
  }

  @override
  void initState() {
    loadRounds(context, RoundStatus.All.name);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RoundsDetailProvider>(builder: (context, proObj, child) {
      if (proObj.isFetching) {
        return Center(
          child: CircularProgressIndicator(),
        );
      }
      List<RoundsListModel> roundItem = proObj.filteredRoundListModel;
      if (proObj.filteredRoundListModel.isNotEmpty) {
        return MasonryGridView.count(
          shrinkWrap: true,
          physics: ScrollPhysics(),
          itemCount: roundItem.length,
          // crossAxisCount: ResponsiveHelper.getCrossAxisCount(
          //   maxScreenWidth: MediaQuery.of(context).size.width,
          //   maxItemWidth: _roundGridWidth,
          // ),
          crossAxisCount: 1,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          itemBuilder: (context, index) {
            return RoundsGridSubItem(
              onTap: () {
                // NavigationService.gotoMeasuringPage(
                //   orderId: proObj.orderNo!,
                //   roundsListModel: roundItem[index],
                //   context: context);
                // NavigationService.pushNamed(roundMeasuringPage,
                //         arguments: MeasuringArguments(
                //             orderNo: widget.orderNumber,
                //             roundsItem: roundItem[index]))
                //     .then((value) {
                //   loadRounds(context, widget.status);
                // });

                // if (roundItem[index].total > 0) {
                //   NavigationService.pushNamed(roundMeasuringPage,
                //           arguments: MeasuringArguments(
                //               orderNo: widget.orderNumber,
                //               roundsItem: roundItem[index]))
                //       .then((value) {
                //     loadRounds(context, widget.status);
                //   });
                // }
              },
              roundsListModel: roundItem[index],
              index: index,
              isMobile: widget.isMobile,
            );
          },
        );
      }
      return EamNoRecordWidget();
    });
  }
}

final _decoration = BoxDecoration(
    color: HexColor("#F6F6F7"),
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: HexColor("#D5DADD")));

class RoundsGridSubItem extends StatefulWidget {
  final RoundsListModel roundsListModel;
  final VoidCallback onTap;
  final int index;
  final isMobile;
  const RoundsGridSubItem({
    Key? key,
    required this.roundsListModel,
    required this.index,
    required this.isMobile,
    required this.onTap,
  }) : super(key: key);

  @override
  State<RoundsGridSubItem> createState() => _RoundsGridSubItemState();
}

class _RoundsGridSubItemState extends State<RoundsGridSubItem> {
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    // TODO: implement dispose
    widget.roundsListModel.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    widget.roundsListModel.counterFocusNode.addListener(() {
      if (!widget.roundsListModel.counterFocusNode.hasFocus) {
        var response = validateReading(
            currentReading:
                widget.roundsListModel.counterTextController.text.isEmpty
                    ? 0.0
                    : double.parse(
                        widget.roundsListModel.counterTextController.text),
            lastRecorded: widget.roundsListModel.recorded,
            lastOilChange: widget
                    .roundsListModel.lastOilChangeTextController.text.isEmpty
                ? 0.0
                : double.parse(
                    widget.roundsListModel.lastOilChangeTextController.text),
            isToggled: widget.roundsListModel.isToggled,
            tripCounter: widget.roundsListModel.lastOilChange);

        if (response != null) {
          if (response['type'] == "alert") {
            UIHelper.showEamDialog(
              context,
              title: "Alert",
              positiveActionLabel: "OK",
              onPositiveClickListener: () {
                UIHelper.closeDialog(context);
                widget.roundsListModel.counterTextController.clear();
              },
              description: response['message'],
            );
          } else {
            UIHelper.showEamDialog(context,
                title: "Alert",
                description: response['message'],
                positiveActionLabel: "Yes",
                negativeActionLabel: "No", onPositiveClickListener: () {
              UIHelper.closeDialog(context);
            }, onNegativeClickListener: () {
              // UIHelper.closeDialog(context);
              widget.roundsListModel.counterTextController.clear();
            });
          }
        }

        _formKey.currentState!.validate();
      }
    });

    widget.roundsListModel.lastOilChangeFocusNode.addListener(() {
      if (!widget.roundsListModel.lastOilChangeFocusNode.hasFocus) {
        var response = validateLastOilChange(
            currentReading:
                widget.roundsListModel.counterTextController.text.isEmpty
                    ? 0.0
                    : double.parse(
                        widget.roundsListModel.counterTextController.text),
            lastRecorded: widget.roundsListModel.recorded,
            lastOilChange: widget
                    .roundsListModel.lastOilChangeTextController.text.isEmpty
                ? 0.0
                : double.parse(
                    widget.roundsListModel.lastOilChangeTextController.text),
            isToggled: widget.roundsListModel.isToggled,
            tripCounter: widget.roundsListModel.lastOilChange);

        if (response != null) {
          if (response['type'] == "alert") {
            UIHelper.showEamDialog(
              context,
              title: "Warning",
              positiveActionLabel: "OK",
              onPositiveClickListener: () {
                UIHelper.closeDialog(context);
                widget.roundsListModel.lastOilChangeTextController.clear();
              },
              description: response['message'],
            );
          } else {
            UIHelper.showEamDialog(context, title: "Alert");
          }
        }

        _formKey.currentState!.validate();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Container(
        decoration: AppStyles.deFaultBoxDecoration,
        width: MediaQuery.of(context).size.width,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Stack(
              children: [
                Container(
                  // color: Colors.amber,
                  width: MediaQuery.of(context).size.width,
                  //  height: 80,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 15.0),
                        child: Text(widget.roundsListModel.measPointDesc,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold)),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          Container(
                            height: 24,
                            width: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: HexColor("#A0D7E9"),
                            ),
                            child: Center(
                                child: EamIcon(
                                        iconName: EamIcon.location, height: 16)
                                    .icon()),
                          ),
                          SizedBox(width: 8),
                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.only(right: 20.0),
                              child: Text(
                                widget.roundsListModel.funcLoc,
                                softWrap: false,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    fontSize: 13, fontWeight: FontWeight.w600),
                              ),
                            ),
                          ),

                          // isMobile ? SizedBox() : SizedBox(width: MediaQuery.of(context).size.width * ,),
                          // isMobile ? SizedBox() : getCount(index),
                        ],
                      ),
                      12.0.spaceY,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          Container(
                            height: 24,
                            width: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: HexColor("#B8B0E5"),
                            ),
                            child: Center(
                                child:
                                    EamIcon(iconName: EamIcon.equip, height: 16)
                                        .icon()),
                          ),
                          SizedBox(width: 8),

                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.only(right: 20.0),
                              child: Text(
                                widget.roundsListModel.equipment,
                                softWrap: false,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    fontSize: 13, fontWeight: FontWeight.w600),
                              ),
                            ),
                          ),

                          // isMobile ? SizedBox() : SizedBox(width: MediaQuery.of(context).size.width * ,),
                          // isMobile ? SizedBox() : getCount(index),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 10.0, bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(AppLocalizations.of(context)!.current_reading,
                                style: TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w600)),
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Flexible(
                                    child: TextFormField(
                                      enabled: context
                                                  .read<SelectRoundProvider>()
                                                  .selectedRoundHeader
                                                  .round_status ==
                                              Constants.ROUNDOPEN
                                          ? true
                                          : false,
                                      focusNode: widget
                                          .roundsListModel.counterFocusNode,
                                      controller: widget.roundsListModel
                                          .counterTextController,
                                      keyboardType: TextInputType.number,
                                      // autovalidateMode:
                                      //     AutovalidateMode.onUserInteraction,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please enter a value';
                                        }

                                        var response = validateReading(
                                            currentReading: double.parse(widget
                                                .roundsListModel
                                                .counterTextController
                                                .text),
                                            lastRecorded:
                                                widget.roundsListModel.recorded,
                                            lastOilChange: double.parse(widget
                                                .roundsListModel
                                                .counterTextController
                                                .text),
                                            isToggled: widget
                                                .roundsListModel.isToggled,
                                            tripCounter: widget
                                                .roundsListModel.lastOilChange);

                                        if (response != null) {
                                          return response['message'];
                                        } else {
                                          return null;
                                        }
                                      },
                                      style: TextStyle(
                                          fontSize:
                                              14), // Good size for 40px height
                                      decoration: InputDecoration(
                                        isDense: true,
                                        helperStyle:
                                            TextStyle(height: 0, fontSize: 0),
                                        contentPadding: EdgeInsets.symmetric(
                                            vertical: 12, horizontal: 10),
                                        hintText:
                                            widget.roundsListModel.isToggled
                                                ? "difference"
                                                : "counter",
                                        hintStyle: TextStyle(fontSize: 12),
                                        suffixIcon: Padding(
                                          padding: EdgeInsets.only(right: 6),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                "h",
                                                style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight:
                                                        FontWeight.w600),
                                              ),
                                              IconButton(
                                                padding: EdgeInsets.zero,
                                                constraints: BoxConstraints(
                                                    minHeight: 24,
                                                    minWidth: 24),
                                                iconSize: 18,
                                                onPressed: () => widget
                                                    .roundsListModel
                                                    .counterTextController
                                                    .clear(),
                                                icon: Icon(Icons.clear),
                                              ),
                                            ],
                                          ),
                                        ),
                                        suffixIconConstraints: BoxConstraints(
                                            minHeight: 24, minWidth: 24),
                                        border: OutlineInputBorder(),
                                        helperText: ' ',
                                      ),
                                    ),
                                  ),
                                  CupertinoSwitch(
                                    value: widget.roundsListModel.isToggled,
                                    activeColor: Color(AppColor.blue),
                                    onChanged: context
                                                .read<SelectRoundProvider>()
                                                .selectedRoundHeader
                                                .round_status ==
                                            Constants.ROUNDOPEN
                                        ? (value) {
                                            context
                                                .read<RoundsDetailProvider>()
                                                .onToggleChanged(
                                                    roundsListModel:
                                                        widget.roundsListModel,
                                                    value: value);
                                          }
                                        : null,
                                  )
                                ],
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                    "${AppLocalizations.of(context)!.last_recorded}: ${widget.roundsListModel.recorded} h",
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600)),
                                Padding(
                                  padding: const EdgeInsets.only(left: 10),
                                  child: InkWell(
                                    onTap: () async {
                                      //fetch Round History;
                                      RoundHistoryViewModel roundHistoryModel =
                                          await RoundsHelper
                                              .getRoundHistoryModel(
                                                  roundObject: widget
                                                      .roundsListModel
                                                      .roundObject);
                                      //
                                      showDialog(
                                        context: context,
                                        builder: (context) {
                                          final screenWidth =
                                              MediaQuery.of(context).size.width;
                                          final screenHeight =
                                              MediaQuery.of(context)
                                                  .size
                                                  .height;
                                          final dialogWidth = screenWidth * 0.8;
                                          final dialogHeight =
                                              screenHeight * 0.7;
                                          return Dialog(
                                            child: SizedBox(
                                              width: dialogWidth,
                                              height: dialogHeight,
                                              child: RoundHistoryPage(
                                                roundHistory: roundHistoryModel,
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    },
                                    child: Icon(
                                      Icons.history,
                                      size: 20,
                                      color: Color(AppColor.blue),
                                    ),
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                              "${AppLocalizations.of(context)!.last_oil_change} @",
                              style: TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.w600)),
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: TextFormField(
                              enabled: context
                                          .read<SelectRoundProvider>()
                                          .selectedRoundHeader
                                          .round_status ==
                                      Constants.ROUNDOPEN
                                  ? true
                                  : false,
                              controller: widget
                                  .roundsListModel.lastOilChangeTextController,
                              keyboardType: TextInputType.number,
                              autovalidateMode:
                                  AutovalidateMode.onUserInteraction,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a value';
                                }

                                // var response = validateLastOilChange(
                                //     currentReading: double.parse(
                                //         currentReadngController.text),
                                //     lastRecorded: roundsListModel.recorded,
                                //     lastOilChange: double.parse(
                                //         lastOilChangeController.text),
                                //     isToggled: roundsListModel.isToggled,
                                //     tripCounter: roundsListModel.lastOilChange);

                                // if (response != null) {
                                //   if (response['type'] == "alert") {
                                //     UIHelper.showEamDialog(
                                //       context,
                                //       title: "Alert",
                                //       onPositiveClickListener: () {
                                //         UIHelper.closeDialog(context);
                                //       },
                                //       description: response['message'],
                                //     );
                                //   }

                                //   return response['message'];
                                // } else {
                                //   return null;
                                // }
                              },
                              style: TextStyle(fontSize: 14),
                              decoration: InputDecoration(
                                isDense: true,
                                helperStyle: TextStyle(height: 0, fontSize: 0),
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 10),
                                hintText: AppLocalizations.of(context)!
                                    .last_oil_change,
                                hintStyle: TextStyle(fontSize: 12),
                                suffixIcon: Padding(
                                  padding: EdgeInsets.only(right: 6),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        "h",
                                        style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600),
                                      ),
                                      IconButton(
                                        padding: EdgeInsets.zero,
                                        constraints: BoxConstraints(
                                            minHeight: 24, minWidth: 24),
                                        iconSize: 18,
                                        onPressed: () => widget.roundsListModel
                                            .lastOilChangeTextController
                                            .clear(),
                                        icon: Icon(Icons.clear),
                                      ),
                                    ],
                                  ),
                                ),
                                suffixIconConstraints:
                                    BoxConstraints(minHeight: 24, minWidth: 24),
                                border: OutlineInputBorder(),
                                helperText: ' ',
                              ),
                            ),
                          ),
                          Text(
                              "${AppLocalizations.of(context)!.hrs_since_last_oil_change}: ${getHrsSinceLastOilChange()} h",
                              style: TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.w600)),
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Consumer<OrderNotifier>(builder: (context, pro, child) {
                  return !pro.isEditable
                      ? SizedBox()
                      : Material(
                          elevation: 0.5,
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                          child: Container(
                            constraints: BoxConstraints(maxWidth: 141),
                            height: 42,
                            width: MediaQuery.of(context).size.width * 0.32,
                            // width: 157,
                            // decoration: AppStyles.btnBoxDecoration.copyWith(
                            //     color: roundsListModel.total > 0
                            //         ? Color(AppColor.blue)
                            //         : Colors.grey),
                            child: Center(
                                child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: AutoSizeText(
                                AppLocalizations.of(context)!.takeReadings,
                                maxLines: 1,
                                maxFontSize: 15,
                                style: AppStyles.buttonTitle16_600,
                              ),
                            )),
                          ),
                        );
                }),
                // !isMobile ? SizedBox() : getCount(index),
                // getCount(index),
              ],
            )
          ]),
        ),
      ),
    );
  }

  double getHrsSinceLastOilChange() {
    double hrsSinceLastOilChange = 0;
    if (!widget.roundsListModel.isToggled) {
      hrsSinceLastOilChange = widget.roundsListModel.recorded -
          widget.roundsListModel.lastOilChange;
    } else {
      if (widget.roundsListModel.counterTextController.text.isEmpty) {
        hrsSinceLastOilChange = widget.roundsListModel.recorded -
            widget.roundsListModel.lastOilChange;
      } else {
        hrsSinceLastOilChange = widget.roundsListModel.recorded +
            double.parse(widget.roundsListModel.counterTextController.text) -
            widget.roundsListModel.lastOilChange;
      }
    }
    return hrsSinceLastOilChange;
  }

  Map<String, String>? validateReading({
    required double currentReading,
    required double lastRecorded,
    required double lastOilChange,
    required bool isToggled,
    required double tripCounter,
  }) {
    // 1. Counter check
    if (!isToggled && currentReading < lastRecorded) {
      return {
        'message':
            "The value must be greater than or equal to the last recorded value: $lastRecorded.",
        'type': "alert",
      };
    }

    // 2. OpHours special checks
    if (isToggled) {
      if (currentReading > 168) {
        return {
          'message':
              "You have exceeded the total of 168 hours allowed to enter weekly. Please contact your local Equipment System Team for additional support.",
          'type': "alert",
        };
      } else if (currentReading > 100) {
        return {
          'message':
              "You have entered a DAILY ROUND exceeding 100 hours. Do you want to proceed?",
          'type': "confirm",
        };
      }
    }

    // All validations passed
    return null;
  }

  Map<String, String>? validateLastOilChange({
    required double currentReading,
    required double lastRecorded,
    required double lastOilChange,
    required bool isToggled,
    required double tripCounter,
  }) {
    // 3. Oil change check
    if (!isToggled && currentReading < tripCounter) {
      return {
        'message':
            "Last Oil Change cannot be greater than the Current Reading.",
        'type': "alert",
      };
    }

    if (isToggled) {
      currentReading = currentReading + lastRecorded;
      if (currentReading < tripCounter) {
        return {
          'message':
              "Last Oil Change cannot be greater than the Current Reading.",
          'type': "alert",
        };
      }
    }
    // All validations passed
    return null;
  }
}
