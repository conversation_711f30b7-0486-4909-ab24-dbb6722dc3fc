/*!
 * # Fomantic-UI - Text
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "text";

@import (multiple) "../../theme.config";

/*******************************
             Text
*******************************/
span.ui.text {
    line-height: @lineHeight;
}

& when not (@variationTextColors = false) {
    each(@variationTextColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @l: @colors[@@color][light];

        span.ui.@{color}.text {
            color: @c;
        }
        & when (@variationTextInverted) {
            span.ui.inverted.@{color}.text {
                color: @l;
            }
        }
    });
}

& when not (@variationTextStates = false) {
    each(@variationTextStates, {
        @state: @value;
        @c: @textStates[@@state][color];

        span.ui.@{state}.text {
            color: @c;
        }
    });
}

& when (@variationTextDisabled) {
    span.ui.disabled.text {
        opacity: @disabledOpacity;
    }
}

/* Sizes */
span.ui.medium.text {
    font-size: @medium;
}
& when not (@variationTextSizes = false) {
    each(@variationTextSizes, {
        @s: @@value;
        span.ui.@{value}.text {
            font-size: @s;
        }
    });
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
