import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/single_select_model.dart';
import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/presentation/eam_packages/dropdown/controller/controller.dart';
import 'package:eam/presentation/common_widgets/searchbar_new.dart';
import 'package:eam/provider/single_selection_provider.dart';
import 'package:eam/widgets/common_widget.dart';
import 'package:eam/widgets/eam_appbar.dart';
import 'package:eam/widgets/eam_search_bar.dart';
import 'package:eam/widgets/single_selection_list_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';

double _defaultPadding = 16;

class SingleSelectionPage2 extends StatefulWidget {
  static const routeName = 'single-selection2';
  static const EQUIPMENT_MODE = "EQUIPMENT_MODE";
  static const FUNCTIONAL_LIST_MODE = "FUNCTIONAL_LIST_MODE";
  final SingleScreenIntent orderEditIntent;
  const SingleSelectionPage2({Key? key, required this.orderEditIntent})
      : super(key: key);

  @override
  _SingleSelectionPage2State createState() => _SingleSelectionPage2State();
}

class _SingleSelectionPage2State extends State<SingleSelectionPage2> {
  IconData actionIcon = FontAwesomeIcons.magnifyingGlass;
  late TextEditingController searchTextController;
  late SingleSelectProvider singleSelectProvider;

  @override
  void initState() {
    searchTextController = TextEditingController();
    var singleSelectProvider =
        Provider.of<SingleSelectProvider>(context, listen: false);
    SchedulerBinding.instance.addPostFrameCallback((_) {
      singleSelectProvider.getSingleSelectionList(
          context: context, orderEditIntent: widget.orderEditIntent, title: '');
    });

    super.initState();
  }

  @override
  void dispose() {
    searchTextController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    singleSelectProvider = Provider.of<SingleSelectProvider>(context);
    return Scaffold(
      appBar: _getSelectionAppBar(),
      body: _getSingleSelectionBody(),
    );
  }

  _getSelectionAppBar() {
    return EamAppBar(
      title: singleSelectProvider.appBarTitle,
    );
  }

  _getSelectionBody() {
    return Container(
      margin: EdgeInsets.only(bottom: kToolbarHeight),
      child: ListView.separated(
        itemCount: singleSelectProvider.singleSelectData.length,
        separatorBuilder: (ctx, index) {
          return SizedBox(
            height: 10,
          );
        },
        itemBuilder: (context, index) {
          SingleSelectModel singleSelectModel =
              singleSelectProvider.singleSelectData[index];
          return InkResponse(
            onTap: () {
              Navigator.pop(context, singleSelectModel);
            },
            child: SingleSelectionListItem(
                singleSelectModel: singleSelectModel,
                onSelect: (singleSelectModel) {
                  Navigator.pop(context, singleSelectModel);
                }),
          );
        },
      ),
    );
  }

  _getSingleSelectionBody() {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        _getSingleScreenListWidget(),
        EamSearchBar(
          onScanned: (res) {
            singleSelectProvider.filterSingleDataList(
                searchString: res.toLowerCase());
          },
          onSearchValueChange: (res) {
            singleSelectProvider.filterSingleDataList(
                searchString: res.toLowerCase());
          },
        ),
      ],
    );
  }

  _getSingleScreenListWidget() {
    if (singleSelectProvider.fetchingSingleSelectModel) {
      return Center(
        child: CircularProgressIndicator(),
      );
    } else {
      if (singleSelectProvider.singleSelectData.isNotEmpty) {
        return _getSelectionBody();
      } else {
        return EamNoRecordWidget(
          showImage: false,
        );
      }
    }
  }
}

class SingleSelectionPageDropdown extends StatefulWidget {
  static const EQUIPMENT_MODE = "EQUIPMENT_MODE";
  static const FUNCTIONAL_LIST_MODE = "FUNCTIONAL_LIST_MODE";
  final SingleScreenIntent orderEditIntent;
  final Function() onTap;
  final VoidCallback onSave;
  final VoidCallback onClear;
  final ValueChanged<SingleSelectModel> onchanged;
  final ValueChanged<List<SingleSelectModel>>? selectedItems;
  bool? searchRequired;
  final bool isMultiSelect;
  final bool showScanner;
  final selectedTitle;
  SingleSelectionPageDropdown(
      {required this.orderEditIntent,
      required this.onTap,
      required this.onchanged,
      this.searchRequired = true,
      required this.showScanner,
      required this.isMultiSelect,
      this.selectedItems,
      required this.selectedTitle,
      required this.onSave,
      required this.onClear});

  @override
  _SingleSelectionPageDropdownState createState() =>
      _SingleSelectionPageDropdownState();
}

class _SingleSelectionPageDropdownState
    extends State<SingleSelectionPageDropdown> {
  late TextEditingController searchTextController;
  late SingleSelectProvider singleSelectProvider;
  bool _init = false;
  @override
  void initState() {
    searchTextController = TextEditingController();
    var singleSelectProvider =
        Provider.of<SingleSelectProvider>(context, listen: false);
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      await singleSelectProvider.getSingleSelectionList(
          context: context,
          orderEditIntent: widget.orderEditIntent,
          title: widget.selectedTitle);

      if (singleSelectProvider.singleSelectData.length == 0) {
        widget.searchRequired = false;
      }

      setState(() {
        _init = true;
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    searchTextController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    singleSelectProvider = Provider.of<SingleSelectProvider>(context);
    return !_init
        ? UIHelper.progressBarWidget(context)
        : Container(
            child: Padding(
              padding: EdgeInsets.only(
                  left: _defaultPadding,
                  right: _defaultPadding,
                  top: _defaultPadding,
                  bottom: widget.isMultiSelect ? 0 : _defaultPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _searchField(),
                  SizedBox(
                    height: 16,
                  ),
                  Consumer<SingleSelectProvider>(
                      builder: (context, pro, child) {
                    if (pro.fetchingSingleSelectModel) {
                      return UIHelper.progressBarWidget(context);
                    }
                    if (pro.singleSelectData.isEmpty) {
                      return Expanded(
                        child: SizedBox(
                            height: 200,
                            width: 200,
                            child: EamNoRecordWidget(
                              showImage: false,
                            )),
                      );
                    }
                    return Expanded(
                      child: ListView.builder(
                        physics: BouncingScrollPhysics(),
                        itemCount: pro.singleSelectData.length,
                        itemBuilder: (context, index) {
                          return SingleSelectItemTile(
                            multiSelect: widget.isMultiSelect,
                            item: pro.singleSelectData[index],
                            onChanged: (SingleSelectModel value) {
                              widget.onchanged(pro.singleSelectData[index]);
                              if (widget.isMultiSelect) {
                                context
                                    .read<SingleSelectProvider>()
                                    .updateSelectItemByGuid(value.guId);
                                if (widget.selectedItems != null) {
                                  widget.selectedItems!(context
                                      .read<SingleSelectProvider>()
                                      .getSelectedItems());
                                }
                              }
                            },
                            onTap: () {
                              widget.onTap();
                            },
                          );
                        },
                      ),
                    );
                  }),
                  widget.isMultiSelect
                      ? Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: Colors.black,
                                  elevation: 0.0,
                                  backgroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                ),
                                onPressed: () {
                                  context
                                      .read<SingleSelectProvider>()
                                      .resetSelectItem();
                                  widget.onClear();
                                  context
                                      .read<DropDownController>()
                                      .updateString(
                                          dropDownType: DropDownType.workCenter,
                                          text: "");
                                },
                                child: Text(
                                  context.locale.clear,
                                  style: AppStyles.headLine16,
                                ),
                              ),
                              SizedBox(width: 16),
                              SizedBox(
                                // height: 46,

                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                      shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  )),
                                  onPressed: () {
                                    if (widget.selectedItems != null) {
                                      widget.selectedItems!(context
                                          .read<SingleSelectProvider>()
                                          .getSelectedItems());
                                    }
                                    widget.onSave();
                                  },
                                  child: Text(
                                    context.locale.save,
                                    style: AppStyles.headLine15_600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : SizedBox()
                ],
              ),
            ),
          );
  }

  _searchField() {
    return widget.searchRequired!
        ? Container(
            height: 50,
            width: double.maxFinite,
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Color.fromRGBO(229, 232, 234, 1),
              ),
            ),
            child: EamSearchBar2(
              key: UniqueKey(),
              isTab: false,
              showScanner: widget.showScanner,
              onScanned: (res) {
                singleSelectProvider.filterSingleDataList(
                    searchString: res.toLowerCase());

                Provider.of<DropDownController>(context, listen: false)
                    .getDropDownHeight(
                        singleSelectProvider.singleSelectData.length);
              },
              onSearchValueChange: (res) {
                singleSelectProvider.filterSingleDataList(
                    searchString: res.toLowerCase());

                Provider.of<DropDownController>(context, listen: false)
                    .getDropDownHeight(
                        singleSelectProvider.singleSelectData.length);
              },
              isRightPaddingRequired: true,
            ))
        : SizedBox();
  }
}

class SingleSelectItemTile extends StatefulWidget {
  final SingleSelectModel item;
  final VoidCallback onTap;
  final ValueChanged<SingleSelectModel> onChanged;
  final bool? multiSelect;
  SingleSelectItemTile(
      {required this.item,
      required this.onTap,
      required this.onChanged,
      this.multiSelect = false});

  @override
  State<SingleSelectItemTile> createState() => _SingleSelectItemTileState();
}

class _SingleSelectItemTileState extends State<SingleSelectItemTile> {
  bool isChecked = false;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          isChecked = !isChecked; // Toggle the checkbox state.
        });
        widget.onTap();
        widget.onChanged(widget.item);
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getCode(widget.item),
                  style: TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
                ),
                widget.multiSelect!
                    ? CheckboxWidget(
                        isChecked: widget.item.select ?? false,
                        onChanged: (val) {
                          setState(() {
                            isChecked =
                                !isChecked; // Toggle the checkbox state.
                          });
                          widget.onChanged(widget.item);
                        },
                      )
                    : SizedBox()
              ],
            ),
            SizedBox(
              height: 3,
            ),
            Text(
              _getDescription(widget.item),
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
            ),
            SizedBox(
              height: 15,
            ),
          ],
        ),
      ),
    );
  }

  _getCode(SingleSelectModel singleSelectModel) {
    return singleSelectModel.id;
  }

  _getDescription(SingleSelectModel singleSelectModel) {
    return singleSelectModel.description +
        (singleSelectModel.number.isNotEmpty
            ? '(${singleSelectModel.number})'
            : '');
  }
}

class CheckboxWidget extends StatelessWidget {
  final bool isChecked;
  final ValueChanged<bool> onChanged;

  CheckboxWidget({
    required this.isChecked,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChanged(!isChecked);
      },
      child: Container(
        width: 24.0, // Adjust size as needed
        height: 24.0, // Adjust size as needed
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.black,
            width: 2.0,
          ),
        ),
        child: isChecked
            ? Icon(
                Icons.check,
                size: 20.0,
                color: Colors.black,
              )
            : null,
      ),
    );
  }
}
