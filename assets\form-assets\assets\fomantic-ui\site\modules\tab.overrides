/*******************************
        User Overrides
*******************************/

.formio-component-tabs {
	background-color: @white;
	padding: @spaceCommonPad;
	border: 1px solid @solidBorderColor;
	border-radius: @8px;

	@media only screen and (max-width: @largestMobileScreen) {
		padding: @spaceCommonPadResponsive;
	}

	.card-vertical {
		column-gap: unit((48 / 14), em);
		margin-top: 0;

		@media only screen and (max-width: @largestMobileScreen) {
			flex-direction: column;
		}

		.un-tab-header {
			@media only screen and (max-width: @largestMobileScreen) {
				margin-bottom: @spaceCommonPad;
			}
		}

		.ui.tabular.menu .item {
			padding: unit((8 / 14), em) unit((12 / 14), em);
			border-bottom: 0;
			border-left: 3px solid transparent;

			@media only screen and (max-width: @largestMobileScreen) {
				padding: 0 0 unit((12 / 14), em) 0;
				border-left: 0;
				border-bottom: 3px solid transparent;
			}
		}

		.ui.attached.tabular.menu {
			width: auto;
			row-gap: unit((4 / 14), em);
			border-bottom: 0;
			border-left: 1px solid @solidBorderColor;

			@media only screen and (max-width: @largestMobileScreen) {
				flex-direction: row;
				border-left: 0;
				border-bottom: 1px solid @borderColor;
				overflow-x: auto;
			}
		}
	}

	.nav-tabs-vertical {
		padding-left: 0;
	}

	.ui.tabular.menu .item {
		padding: 0 0 unit((12 / 14), em) 0;
		border-bottom: 3px solid transparent;

		&:active {
			background-color: transparent !important;
		}

		&.active {
			border-color: @primaryBorderColor;
		}
	}

	.ui.attached.tabular.menu {
		column-gap: unit((16 / 14), em);
		border-bottom: 1px solid @solidBorderColor;
		overflow-x: auto;
		border-radius: 0 !important;

		@media only screen and (max-width: @largestMobileScreen) {
			margin: 0 -@spaceCommonPadResponsive;
			padding-left: @spaceCommonPadResponsive;
			min-width: calc(100% + @spaceCommonPadResponsive * 2);

			&::-webkit-scrollbar {
				width: 0;
				height: 0;
			}
		}
	}
}

.ui.attached.segment.tab {
	padding: @spaceCommonPad 0 0 0;
	border: 0 !important;
	margin: 0;
}

.card-vertical {
	.ui.attached.segment.tab {
		background-color: @white;
		flex: 1 1 0;
		padding: unit((16 / 14), em);
		border: 1px solid @solidBorderColor !important;
		border-radius: @8px;
	}
}

/* pagination */
.un-tab-header {
	&.un-has-tab-pagination {
		display: flex;
		overflow: hidden;
		position: relative;

		.un-tab-labels {
			flex-grow: 1;
			overflow: hidden;
			z-index: 1;
		}

		.un-tab-header-pagination {
			display: none;
			justify-content: center;
			align-items: center;
			min-width: 30px;
			font-size: 18px;
			border: 1px solid @solidBorderColor;
			border-radius: 50%;
			aspect-ratio: 1;
			cursor: pointer;
			z-index: 2;
			transition: all 0.4s;

			.icon {
				margin: 0;
			}

			&:hover {
				color: @primaryColor;
				border-color: @primaryColor;
			}
		}

		.un-tab-header-pagination-before {
			margin-right: 8px;
		}

		.un-tab-header-pagination-after {
			margin-left: 8px;
		}

		.un-tab-header-pagination-disabled {
			opacity: 0.8;
			pointer-events: none;

			.icon {
				opacity: 0.6;
			}
		}

		&.un-tab-header-pagination-controls-enabled {
			@media only screen and (max-width: @largestMobileScreen) {
				margin-left: -10px;
				margin-right: -10px;
			}

			&:not(.un-tab-header-vertical) {
				.un-tab-header-pagination {
					display: flex;
				}
			}

			&.un-tab-header-vertical {
				@media only screen and (max-width: @largestMobileScreen) {
					.un-tab-header-pagination {
						display: flex;
					}
				}
			}
		}
	}
}
