/*******************************
             Table
*******************************/

/* -------------------
       Element
-------------------- */

@verticalMargin: 1em;
@horizontalMargin: 0;
@margin: @verticalMargin @horizontalMargin;
@borderCollapse: separate;
@borderSpacing: 0;
@borderRadius: @defaultBorderRadius;
@transition:
    background @defaultDuration @defaultEasing,
    color @defaultDuration @defaultEasing;
@background: @white;
@color: @textColor;
@borderWidth: 1px;
@border: @borderWidth solid @borderColor;
@boxShadow: none;
@textAlign: left;
@verticalAlign: middle;

/* --------------
     Parts
--------------- */

/* Table Row */
@rowBorder: 1px solid @internalBorderColor;

/* Table Cell */
@cellVerticalPadding: @relativeMini;
@cellHorizontalPadding: @relativeMini;
@cellVerticalAlign: inherit;
@cellTextAlign: inherit;
@cellBorder: 1px solid @internalBorderColor;

/* Table Header */
@headerBorder: 1px solid @internalBorderColor;
@headerDivider: none;
@headerBackground: @offWhite;
@headerBackgroundHex: @offWhite;
@headerAlign: inherit;
@headerVerticalAlign: inherit;
@headerColor: @textColor;
@headerVerticalPadding: @relativeSmall;
@headerHorizontalPadding: @cellHorizontalPadding;
@headerFontStyle: none;
@headerFontWeight: @bold;
@headerTextTransform: none;
@headerBoxShadow: none;

/* Table Footer */
@footerBoxShadow: none;
@footerBorder: 1px solid @borderColor;
@footerDivider: none;
@footerBackground: @offWhite;
@footerBackgroundHex: @offWhite;
@footerAlign: inherit;
@footerVerticalAlign: inherit;
@footerColor: @textColor;
@footerVerticalPadding: @cellVerticalPadding;
@footerHorizontalPadding: @cellHorizontalPadding;
@footerFontStyle: @normal;
@footerFontWeight: @normal;
@footerTextTransform: none;

/* Responsive Size */
@responsiveHeaderDisplay: block;
@responsiveFooterDisplay: block;
@responsiveRowVerticalPadding: 1em;
@responsiveRowBoxShadow: 0 -1px 0 0 rgba(0, 0, 0, 0.1) inset;
@responsiveCellVerticalPadding: 0.25em;
@responsiveCellHorizontalPadding: 0.75em;
@responsiveCellBoxShadow: none;
@responsiveCellHeaderFontWeight: @bold;

/* -------------------
       Types
-------------------- */

/* Definition */
@definitionPageBackground: @white;

@definitionHeaderBackground: @white;
@definitionHeaderColor: @unselectedTextColor;
@definitionHeaderFontWeight: @normal;

@definitionFooterBackground: @definitionHeaderBackground;
@definitionFooterColor: @definitionHeaderColor;
@definitionFooterFontWeight: @definitionHeaderFontWeight;

@definitionColumnBackground: @subtleTransparentBlack;
@definitionColumnFontWeight: @bold;
@definitionColumnColor: @selectedTextColor;
@definitionColumnFontSize: @relativeMedium;
@definitionColumnTextTransform: "";
@definitionColumnBoxShadow: "";
@definitionColumnTextAlign: "";
@definitionColumnHorizontalPadding: "";

/* --------------
    Couplings
--------------- */

@iconVerticalAlign: baseline;

/* --------------
     States
--------------- */

@stateMarkerWidth: 0;

/* Positive */
@positiveColor: @positiveTextColor;
@positiveBoxShadow: @stateMarkerWidth 0 0 @positiveBorderColor inset;
@positiveBackgroundHover: if(iscolor(@positiveBackgroundColor), darken(@positiveBackgroundColor, 3), @positiveBackgroundColor);
@positiveColorHover: if(iscolor(@positiveColor), darken(@positiveColor, 3), @positiveColor);
@invertedPositiveColor: @positiveTextColor;
@invertedPositiveBackgroundColor: @positiveBorderColor;

/* Negative */
@negativeColor: @negativeTextColor;
@negativeBoxShadow: @stateMarkerWidth 0 0 @negativeBorderColor inset;
@negativeBackgroundHover: if(iscolor(@negativeBackgroundColor), darken(@negativeBackgroundColor, 3), @negativeBackgroundColor);
@negativeColorHover: if(iscolor(@negativeColor), darken(@negativeColor, 3), @negativeColor);
@invertedNegativeColor: @negativeTextColor;
@invertedNegativeBackgroundColor: @negativeBorderColor;

/* Error */
@errorColor: @errorTextColor;
@errorBoxShadow: @stateMarkerWidth 0 0 @errorBorderColor inset;
@errorBackgroundHover: if(iscolor(@errorBackgroundColor), darken(@errorBackgroundColor, 3), @errorBackgroundColor);
@errorColorHover: if(iscolor(@errorColor), darken(@errorColor, 3), @errorColor);
@invertedErrorColor: @errorTextColor;
@invertedErrorBackgroundColor: @errorBorderColor;

/* Warning */
@warningColor: @warningTextColor;
@warningBoxShadow: @stateMarkerWidth 0 0 @warningBorderColor inset;
@warningBackgroundHover: if(iscolor(@warningBackgroundColor), darken(@warningBackgroundColor, 3), @warningBackgroundColor);
@warningColorHover: if(iscolor(@warningColor), darken(@warningColor, 3), @warningColor);
@invertedWarningColor: @warningTextColor;
@invertedWarningBackgroundColor: #eec97e;

/* Active */
@activeColor: @textColor;
@activeBackgroundColor: #e0e0e0;
@activeBoxShadow: @stateMarkerWidth 0 0 @activeColor inset;
@invertedActiveColor: @textColor;
@invertedActiveBackgroundColor: #a0a0a0;

@activeBackgroundHover: #efefef;
@activeColorHover: @selectedTextColor;

/* --------------
     Types
--------------- */

/* Attached */
@attachedTopOffset: 0;
@attachedBottomOffset: 0;
@attachedHorizontalOffset: -@borderWidth;
@attachedWidth: e(%("calc(100%% + %d)", -@attachedHorizontalOffset * 2));
@attachedBoxShadow: none;
@attachedBorder: @borderWidth solid @solidBorderColor;
@attachedBottomBoxShadow: @attachedBoxShadow;

/* Striped */
@stripedBackground: rgba(0, 0, 50, 0.02);
@invertedStripedBackground: rgba(255, 255, 255, 0.05);

/* Selectable */
@selectableBackground: @transparentBlack;
@selectableTextColor: @selectedTextColor;
@selectableInvertedBackground: @transparentWhite;
@selectableInvertedTextColor: @invertedSelectedTextColor;

/* Sortable */
@sortableBackground: "";
@sortableColor: @textColor;

@sortableBorder: 1px solid @borderColor;
@sortableIconWidth: auto;
@sortableIconDistance: 0.5em;
@sortableIconOpacity: 0.8;
@sortableIconFont: "Icons";
@sortableIconAscending: "\f0d8";
@sortableIconDescending: "\f0d7";
@sortableDisabledColor: @disabledTextColor;

@sortableHoverBackground: @transparentBlack;
@sortableHoverColor: @hoveredTextColor;

@sortableActiveBackground: @transparentBlack;
@sortableActiveColor: @selectedTextColor;

@sortableActiveHoverBackground: @transparentBlack;
@sortableActiveHoverColor: @selectedTextColor;

@sortableInvertedBorderColor: transparent;
@sortableInvertedHoverBackground: @transparentWhite @subtleGradient;
@sortableInvertedHoverColor: @invertedHoveredTextColor;
@sortableInvertedActiveBackground: @strongTransparentWhite @subtleGradient;
@sortableInvertedActiveColor: @invertedSelectedTextColor;

/* Colors */
@coloredBorderSize: 0.2em;
@coloredBorderRadius: 0 0 @borderRadius @borderRadius;
@coloredBorderSizeCover: (@coloredBorderSize / 2);

/* Inverted */
@invertedBackground: #333;
@invertedBorder: none;
@invertedCellBorder: 1px solid @whiteBorderColor;
@invertedCellBorderColor: @whiteBorderColor;
@invertedCellColor: @invertedTextColor;

@invertedHeaderBackground: @veryStrongTransparentBlack;
@invertedHeaderBackgroundHex: #252525;
@invertedHeaderColor: @invertedTextColor;
@invertedHeaderBorderColor: @invertedCellBorderColor;

@invertedFooterBackground: @invertedHeaderBackground;
@invertedFooterBackgroundHex: @invertedHeaderBackgroundHex;
@invertedFooterColor: @invertedHeaderColor;
@invertedFooterBorderColor: @invertedHeaderBorderColor;

@invertedDefinitionColumnBackground: @subtleTransparentWhite;
@invertedDefinitionColumnColor: @invertedSelectedTextColor;
@invertedDefinitionColumnFontWeight: @bold;

/* Basic */
@basicTableBackground: transparent;
@basicTableBorder: @borderWidth solid @borderColor;
@basicBoxShadow: none;

@basicTableHeaderBackground: transparent;
@basicTableCellBackground: transparent;
@basicTableHeaderDivider: none;
@basicTableCellBorder: 1px solid rgba(0, 0, 0, 0.1);
@basicTableCellPadding: "";
@basicTableStripedBackground: @transparentBlack;

/* Padded */
@paddedVerticalPadding: 1em;
@paddedHorizontalPadding: 1em;
@veryPaddedVerticalPadding: 1.5em;
@veryPaddedHorizontalPadding: 1.5em;

/* Compact */
@compactVerticalPadding: 0.5em;
@compactHorizontalPadding: 0.7em;
@veryCompactVerticalPadding: 0.4em;
@veryCompactHorizontalPadding: 0.6em;

/* Scrolling */
@scrollingMobileMaxItems: 3;
@scrollingTabletMaxItems: 4;
@scrollingComputerMaxItems: 6;
@scrollingWidescreenMaxItems: 8;

@scrollingItemHeight: (@cellVerticalPadding * 2) + @lineHeight;
@scrollingMobileMaxBodyHeight: (@scrollingItemHeight * @scrollingMobileMaxItems);
@scrollingTabletMaxBodyHeight: (@scrollingItemHeight * @scrollingTabletMaxItems);
@scrollingComputerMaxBodyHeight: (@scrollingItemHeight * @scrollingComputerMaxItems);
@scrollingWidescreenMaxBodyHeight: (@scrollingItemHeight * @scrollingWidescreenMaxItems);

/* Stuck */
@stuckBackground: @background;
@stuckHeaderBackground: @offWhite;
@stuckFooterBackground: @stuckHeaderBackground;

@invertedStuckBackground: @invertedBackground;
@invertedStuckHeaderBackground: @invertedHeaderBackgroundHex;
@invertedStuckFooterBackground: @invertedStuckHeaderBackground;

@stuckZIndex: 800;

@overflowingMobileMaxItems: 5;
@overflowingTabletMaxItems: 6;
@overflowingComputerMaxItems: 8;
@overflowingWidescreenMaxItems: 10;

@overflowingItemHeight: (@cellVerticalPadding * 2) + @lineHeight;
@overflowingMobileMaxHeight: (@overflowingItemHeight * @overflowingMobileMaxItems);
@overflowingTabletMaxHeight: (@overflowingItemHeight * @overflowingTabletMaxItems);
@overflowingComputerMaxHeight: (@overflowingItemHeight * @overflowingComputerMaxItems);
@overflowingWidescreenMaxHeight: (@overflowingItemHeight * @overflowingWidescreenMaxItems);

/* Sizes */
@small: 0.9em;
@medium: 1em;
@large: 1.1em;

/* Resizable */
@resizableDirection: vertical;
