import 'package:auto_size_text/auto_size_text.dart';
import 'package:eam/helpers/extensions.dart';

import 'package:eam/models/generic_ron_model.dart';

import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/presentation/common_widgets/inkwell.dart';
import 'package:eam/presentation/common_widgets/priority_label.dart';
import 'package:eam/presentation/common_widgets/sync_button.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/presentation/widgets/generic_item/generic_item.dart';
import 'package:eam/presentation/widgets/molecules_layer/ea_card.dart';
import 'package:eam/utils/app_color.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';

class GenericModelWebItem extends StatefulWidget {
  final GenericRONModel genericRONModel;
  ValueChanged<GenericRONModel>? onSubmitCallBack;
  final bool isHistoryPage;
  final GenericModelType? genericModelType;
  GenericModelWebItem(
      {Key? key,
      required this.genericRONModel,
      this.onSubmitCallBack,
      required this.isHistoryPage,
      this.genericModelType})
      : super(key: key);

  @override
  State<GenericModelWebItem> createState() => _GenericModelWebItemState();
}

class _GenericModelWebItemState extends State<GenericModelWebItem> {
  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                flex: 3,
                child: _notificationDetails(),
                fit: FlexFit.tight,
              ),
              Visibility(
                visible: widget.genericModelType != GenericModelType.round,
                child: Flexible(
                  flex: 2,
                  child: _priorityCard(),
                  fit: FlexFit.tight,
                ),
              ),
              Visibility(
                visible: widget.genericModelType != GenericModelType.round,
                child: Flexible(
                  flex: 2,
                  child: _notificationType(),
                  fit: FlexFit.tight,
                ),
              ),
              Flexible(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _startDate(),
                        _endDate(),
                      ],
                    ),
                    SizedBox(
                      height:
                          (widget.genericModelType == GenericModelType.round ||
                                  widget.genericRONModel.equipmentName.isEmpty)
                              ? 30
                              : 50,
                    ),
                    SyncButton(
                      genericRONModel: widget.genericRONModel,
                      onTapaction: () async {
                        _handleSubmit();
                      },
                      show: widget.isHistoryPage!,
                    )
                  ],
                ),
                fit: FlexFit.tight,
              ),
              // Flexible(
              //   flex: 1,
              //   child: Container(
              //     height:
              //         widget.genericModelType == GenericModelType.round ? 90 : 120,
              //     child: Column(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //       children: [
              //         _endDate(),
              //         SyncButton(
              //           genericRONModel: widget.genericRONModel,
              //           onTapaction: () async {
              //             _handleSubmit();
              //           },
              //           show: widget.isHistoryPage!,
              //         )
              //       ],
              //     ),
              //   ),
              // ),
              // Container(
              //   child: _menuIcon(),
              // ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Visibility(
                visible:
                    widget.genericModelType == GenericModelType.notification ||
                        widget.genericModelType == GenericModelType.order,
                child: Column(
                  children: [
                    Divider(),
                    Padding(
                      padding: const EdgeInsets.only(top: 5.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            height: 25,
                            child: Row(
                              children: [
                                Container(
                                  height: 24,
                                  width: 24,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.green[100],
                                  ),
                                  child: Center(
                                      child: Icon(
                                    Icons.account_circle_outlined,
                                    size: 16,
                                  )),
                                ),
                                SizedBox(width: 8),
                                Text(
                                  widget.genericRONModel.reportedBy ?? "",
                                  maxLines: 1,
                                  overflow: TextOverflow.visible,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 25,
                            child: Row(
                              children: [
                                Text(
                                  (widget.genericRONModel.assgnedTo == null ||
                                          widget.genericRONModel.assgnedTo!
                                              .isEmpty)
                                      ? "NA"
                                      : widget.genericRONModel.assgnedTo!,
                                  maxLines: 1,
                                  overflow: TextOverflow.visible,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14),
                                ),
                                SizedBox(width: 8),
                                Container(
                                  height: 24,
                                  width: 24,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.orange[100],
                                  ),
                                  child: Center(
                                      child: Icon(
                                    Icons.account_circle_outlined,
                                    size: 16,
                                  )),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Visibility(
                visible: widget.genericModelType == GenericModelType.round,
                child: Column(
                  children: [
                    Divider(),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 5.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 5,
                                backgroundColor: Color(0xff285FE7),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 5.0),
                                child: Text(
                                    "Total : ${widget.genericRONModel.subTitle3}"),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 5,
                                backgroundColor: Colors.greenAccent,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 5.0),
                                child: Text(
                                    "Completed : ${widget.genericRONModel.subTitle2}"),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 5,
                                backgroundColor: AppColor.redColor,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 5.0),
                                child: Text(
                                    "Pending : ${widget.genericRONModel.subTitle1}"),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  _notificationDetails() {
    var size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "${widget.genericRONModel.title}",
          maxLines: 1,
          style: TextStyle(
            color: HexColor("#0F1419"),
            fontWeight: FontWeight.w700,
            fontSize: 16,
          ),
        ),
        Visibility(
          visible: widget.genericModelType == GenericModelType.notification,
          child: Column(
            children: [
              SizedBox(
                height: size.height * 0.01,
              ),
              SizedBox(
                height: 25,
                child: Row(
                  children: [
                    Container(
                      height: 24,
                      width: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.green[100],
                      ),
                      child: Center(
                          child: Icon(
                        Icons.notifications_none_rounded,
                        size: 16,
                      )),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                        child: Text(
                      "${widget.genericRONModel.key}",
                      maxLines: 1,
                      overflow: TextOverflow.visible,
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    )),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Row(
        //   children: [
        //     Text(
        //       _notificationNumber(),
        //       maxLines: 1,
        //       style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        //     ),
        //     SizedBox(width: 10),
        //     // widget.genericRONModel.operationRunning
        //     //     ? EACard(
        //     //         height: 24,
        //     //         color: Colors.green,
        //     //         title: "Operation is running",
        //     //       )
        //     //     : SizedBox()
        //     widget.genericRONModel.operationRunning ? clockIcon : SizedBox()
        //   ],
        // ),

        SizedBox(height: 15),
        Row(
          children: [
            Container(
              height: 24,
              width: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: HexColor("#A0D7E9"),
              ),
              child: Center(
                child: EamIcon(iconName: EamIcon.location, height: 16).icon(),
              ),
            ),
            SizedBox(width: 8),
            Flexible(
              child: Text(
                getFunctionalLocation(),
                maxLines: 1,
                overflow: TextOverflow.visible,
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
              ),
            ),
          ],
        ),
        widget.genericModelType == GenericModelType.round
            ? SizedBox()
            : SizedBox(height: 12),
        widget.genericModelType == GenericModelType.round
            ? SizedBox()
            : widget.genericRONModel.equipmentName.isNotEmpty
                ? Row(
                    children: [
                      Container(
                        height: 24,
                        width: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: HexColor("#B8B0E5"),
                        ),
                        child: Center(
                          child: EamIcon(iconName: EamIcon.equip, height: 16)
                              .icon(),
                        ),
                      ),
                      SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          "${widget.genericRONModel.equipmentName}",
                          maxLines: 1,
                          overflow: TextOverflow.visible,
                          style: TextStyle(
                              fontWeight: FontWeight.w600, fontSize: 14),
                        ),
                      )
                    ],
                  )
                : SizedBox(),
        widget.genericModelType == GenericModelType.round ||
                widget.genericRONModel.equipmentName.isEmpty
            ? SizedBox()
            : SizedBox(
                height: 12,
              ),
        Visibility(
          visible: widget.genericModelType == GenericModelType.notification ||
              widget.genericModelType == GenericModelType.order,
          child: Column(
            children: [
              SizedBox(
                height: 25,
                child: Row(
                  children: [
                    Container(
                      height: 24,
                      width: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.green[100],
                      ),
                      child: Center(
                          child: Icon(
                        Icons.straighten,
                        size: 16,
                      )),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                        child: Text(
                      "${widget.genericRONModel.mainWorkCntr} (${widget.genericRONModel.mainWorkCntrDesc})",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style:
                          TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                    )),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String getFunctionalLocation() {
    if (widget.genericModelType == GenericModelType.round) {
      return widget.genericRONModel.rigName ?? "NA";
    } else {
      return widget.genericRONModel.functionalLocation;
    }
  }

  _notificationNumber() {
    if (widget.genericRONModel.key
        .toLowerCase()
        .startsWith(AppLocalizations.of(context)!.newString.toLowerCase())) {
      return AppLocalizations.of(context)!.newString;
    }
    return widget.genericRONModel.key;
  }

  _priorityCard() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: PriorityLabel(
        priority: widget.genericRONModel.priority,
        priorityType: widget.genericRONModel.priority,
        priorityDesc: widget.genericRONModel.priorityDesc,
      ),
    );
  }

  _notificationType() {
    return Text(
      widget.genericRONModel.type,
      maxLines: 1,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  _startDate() {
    return AutoSizeText(
      widget.genericRONModel.date,
      maxLines: 1,
      maxFontSize: 14,
      style: TextStyle(
        color: HexColor("#0F1419"),
        fontWeight: FontWeight.w600,
      ),
    );
  }

  _endDate() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AutoSizeText(
          widget.genericRONModel.endDate,
          maxLines: 1,
          maxFontSize: 14,
          style: TextStyle(
            color: HexColor("#0F1419"),
            fontWeight: FontWeight.w600,
          ),
        ),
        // widget.genericRONModel.operationRunning ? clockIcon : SizedBox()
      ],
    );
  }

  _handleSubmit() {
    if (widget.onSubmitCallBack != null) {
      // setState(() {
      //   widget.genericRONModel.isSubmitRequired = false;
      //   widget.genericRONModel.status = Colors.orange;
      // });

      widget.onSubmitCallBack!(widget.genericRONModel);
    }
  }
}

class GenericGridTitles extends StatefulWidget {
  const GenericGridTitles({Key? key, required this.genericModelType})
      : super(key: key);

  final GenericModelType genericModelType;

  @override
  State<GenericGridTitles> createState() => _GenericGridTitlesState();
}

class _GenericGridTitlesState extends State<GenericGridTitles> {
  String _title1 = "";
  String _title3 = "";

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _gettitles(context);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: const EdgeInsets.only(left: 20, bottom: 12, right: 20),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Container(
                // constraints: BoxConstraints(maxWidth: 1000),
                // width: MediaQuery.of(context).size.width * 0.30,
                child: _notificationTitles(_title1),
              ),
            ),
            Visibility(
              visible: widget.genericModelType != GenericModelType.round,
              child: Expanded(
                flex: 2,
                child: Container(
                  // constraints: BoxConstraints(maxWidth: width * 0.13),
                  // width: MediaQuery.of(context).size.width * 0.30,
                  child: _notificationTitles(context.locale.priority),
                ),
              ),
            ),
            Visibility(
              visible: widget.genericModelType != GenericModelType.round,
              child: Expanded(
                flex: 2,
                child: Container(
                  // constraints: BoxConstraints(maxWidth: width * 10),
                  child: _notificationTitles(_title3),
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        child: _notificationTitles(context.locale.start_date),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 25),
                        child: Container(
                          child: _notificationTitles(context.locale.end_date),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Expanded(
            //   flex: 1,
            //   child: Container(
            //     child: _notificationTitles(context.locale.end_date),
            //   ),
            // ),
            // Container(constraints: BoxConstraints(maxWidth: 50), width: 20),
          ],
        ),
      ),
    );
  }

  _gettitles(BuildContext context) {
    switch (widget.genericModelType) {
      case GenericModelType.order:
        _title1 = context.locale.order_details;
        _title3 = context.locale.orderType;
        break;
      case GenericModelType.notification:
        _title1 = context.locale.notificationDetails;
        _title3 = context.locale.notificationType;
        break;

      case GenericModelType.round:
        _title1 = context.locale.round_details;
        _title3 = context.locale.roundType;
        break;
      case GenericModelType.none:
        // TODO: Handle this case.
        break;
    }
    return "";
  }

  _notificationTitles(String titleName) {
    return Text(
      titleName,
      maxLines: 1,
      style: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w600,
        color: HexColor("#4F5051"),
      ),
    );
  }
}
