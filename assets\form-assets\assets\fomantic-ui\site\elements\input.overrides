/*******************************
         Site Overrides
*******************************/

// .ui.labeled.input:not([class*='corner labeled']) .label:first-child + input:not(:invalid):focus {
// 	border-left-color: @focusBorderColor;
// }

/*******************************
            States
*******************************/
& when (@variationInputDisabled) {
	/* --------------------
            Disabled
    --------------------- */

	.ui.disabled.input,
	.ui.input:not(.disabled) input[disabled] {
		background: rgba(@lightGrey, 0.3);
		// border-color: rgba(@borderColor, 0.3);
	}
}

input[type='time']::-webkit-calendar-picker-indicator {
	position: absolute;
	left: -8px;
	top: 0;
	bottom: 0;
	width: calc(100% - 0px);
	height: auto;
	z-index: 999;
	background: none;
	pointer-events: all;
	cursor: text;
}

.ui.form .field .ui.labeled.input {
	&:has(> :disabled) > .label {
		background: rgba(@lightGrey, 0.3) !important;
		// border-color: rgba(@borderColor, 0.3) !important;
		pointer-events: none;
	}
}
