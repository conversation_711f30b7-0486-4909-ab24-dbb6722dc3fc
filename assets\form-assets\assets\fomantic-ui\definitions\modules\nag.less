/*!
 * # Fomantic-UI - Nag
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "nag";

@import (multiple) "../../theme.config";

/*******************************
             Nag
*******************************/

.ui.nag {
    display: none;
    opacity: @opacity;
    position: @position;
    top: @top;
    left: 0;
    z-index: @zIndex;
    min-height: @minHeight;
    width: @width;
    margin: @margin;
    padding: @padding;
    background: @background;
    box-shadow: @boxShadow;
    font-size: @fontSize;
    text-align: @textAlign;
    color: @color;
    border-radius: @topBorderRadius;
    transition: @transition;
}

a.ui.nag {
    cursor: pointer;
}

.ui.nag > .title {
    display: inline-block;
    margin: @titleMargin;
    color: @titleColor;
}

.ui.nag > .close.icon {
    cursor: pointer;
    opacity: @closeOpacity;
    position: absolute;
    top: @closeTop;
    right: @closeRight;
    font-size: @closeSize;
    margin: @closeMargin;
    color: @closeColor;
    transition: @closeTransition;
}

.ui.nag:not(.overlay):not(.fixed) {
    border-radius: @borderRadius;
}

/*******************************
             States
*******************************/

/* Hover */
.ui.nag:hover {
    background: @nagHoverBackground;
    opacity: @nagHoverOpacity;
}

.ui.nag > .close:hover {
    opacity: @closeHoverOpacity;
}

/*******************************
           Variations
*******************************/

& when(@variationNagOverlay) {
    /* --------------
         Static
    --------------- */
    .ui.overlay.nags,
    .ui.overlay.nag {
        position: absolute;
    }
}

& when(@variationNagFixed) {
    /* --------------
         Fixed
    --------------- */
    .ui.fixed.nags,
    .ui.fixed.nag {
        position: fixed;
    }
}
& when(@variationNagBottom) {
    /* --------------
         Bottom
    --------------- */
    .ui.bottom.nags,
    .ui.bottom.nag {
        border-radius: @bottomBorderRadius;
        top: auto;
        bottom: @bottom;
    }
}

& when(@variationNagInverted) {
    /* --------------
         Inverted
    --------------- */

    .ui.inverted.nags .nag,
    .ui.inverted.nag {
        background-color: @invertedBackground;
        color: @darkTextColor;
    }
    .ui.inverted.nags .nag > .close,
    .ui.inverted.nag > .close {
        color: @invertedCloseColor;
    }
    .ui.inverted.nags .nag > .title,
    .ui.inverted.nag > .title {
        color: @invertedTitleColor;
    }
}

& when not (@variationNagSizes = false) {
    /* -------------------
            Sizes
    -------------------- */
    each(@variationNagSizes, {
        @s: @@value;
        @sr: "@{value}Raw";
        .ui.@{value}.nag,
        .ui.@{value}.nags .nag {
            font-size: @s;
            & when (@@sr > 1.4) {
                line-height: 1;
            }
        }
    });
}

/* --------------
     Colors
-------------- */
& when not (@variationNagColors = false) {
    each(@variationNagColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @l: @colors[@@color][light];
        @isVeryDark: @colors[@@color][isVeryDark];

        .ui.@{color}.nag {
            background-color: @c;
            & when (@isVeryDark) {
                color: @invertedTextColor;
            }
        }
        & when (@variationNagInverted) {
            .ui.inverted.@{color}.nag {
                background-color: @l;
                & .title when (@isVeryDark) {
                    color: @titleColor;
                }
            }
        }
    });
}

& when (@variationNagGroups) {
    /*******************************
               Groups
    *******************************/
    .ui.nags {
        top: @top;
        left: 0;
        width: @width;
    }
    .ui.ui.nags .nag {
        border-radius: @groupedBorderRadius;
    }
    .ui.nags:not(.bottom) .nag:last-child {
        border-radius: @topBorderRadius;
    }
    & when(@variationNagBottom) {
        .ui.bottom.nags .nag:first-child {
            border-radius: @bottomBorderRadius;
        }
    }
    .ui.nags:not(.fixed):not(.overlay) .nag:first-child {
        border-radius: @bottomBorderRadius;
    }
    .ui.nags:not(.fixed):not(.overlay) .nag:only-child {
        border-radius: @borderRadius;
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
