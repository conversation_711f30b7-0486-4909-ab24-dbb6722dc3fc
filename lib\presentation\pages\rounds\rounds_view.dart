import 'dart:async';
import 'dart:developer';

import 'package:eam/be/ORDER_COST_SUM.dart';
import 'package:eam/helpers/application_helper.dart';
import 'package:eam/helpers/pa_helper.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/rounds_helper.dart';
import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/menu_action_dropdown.dart';
import 'package:eam/presentation/common_widgets/eam_tab.dart';
import 'package:eam/presentation/pages/rounds/create_round.dart';
import 'package:eam/provider/assistant/FabVisibilityProvider.dart';
import 'package:eam/provider/rounds_provider.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/widgets/eam_toolbar2.dart';
import 'package:eam/widgets/menu_action_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../utils/app_constants.dart';
import '../../../utils/app_dimension.dart';
import '../../../utils/app_notifier.dart';
import '../../widgets/atoms_layer/eam_icons.dart';
import '../../common_widgets/eam_icon_button.dart';
import 'widgets/rounds_grids/widgets/rounds_grid_new.dart';

enum RoundsTabStatus { All, Today, Upcoming }

class RoundsView extends StatefulWidget {
  static const routeName = '/rounds-view';

  final bool enableBack;
  final bool? isChartData;
  const RoundsView(
      {Key? key, required this.enableBack, this.isChartData = false})
      : super(key: key);

  @override
  State<RoundsView> createState() => _RoundsViewState();
}

class _RoundsViewState extends State<RoundsView> {
  late RoundProvider roundProvider;

  bool _loadRound = false;

  List _tabItems = [
    RoundsTabStatus.All.name,
    RoundsTabStatus.Today.name,
    RoundsTabStatus.Upcoming.name
  ];

  bool timerRunning = false;
  Timer? _timer;
  AppNotifier _appNotifier = AppNotifier();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateFabVisibility("Rounds");
  }

  void _updateFabVisibility(String? contextMessage) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FabVisibilityProvider>(context, listen: false)
          .updateContext(contextMessage);
    });
  }

  @override
  void initState() {
    _initProvider();

    _appNotifier.notifySyncStatus((data) {
      // if (mounted) {
      //   context.read<ProgressProvider>().setInboxCount();
      // }
      if (data[EventSyncStatusFieldType] ==
          EventSyncStatusTypeInboxProcessingComplete) {
        if (mounted) {
          _initProvider();
          // context.read<ProgressProvider>().setInboxCount();
        }
      }
      if (mounted) {
        if ((data[EventSyncStatusFieldType] == EventSyncStatusTypeInbox &&
            (data[EventSyncStatusFieldInboxCount] == 0 &&
                data[EventSyncStatusFieldSentItemsCount] == 0))) {
          _initProvider();
        } else if (data[EventSyncStatusFieldType] == EventSyncStatusTypeSent) {
          _initProvider();
        } else if (data[EventSyncStatusFieldType] == EventSyncStatusTypeInbox) {
          _initProvider();
        } else {}
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    if (mounted) {
      _appNotifier.unSubscribeNotifySyncStatus();
      // _appNotifier.unSubscribeInfoMessage();
    }
    // _updateFabVisibility("");
    super.dispose();
  }

  void _initProvider() async {
    setState(() {
      _loadRound = true;
    });
    if (!widget.isChartData!) {
      await context.read<RoundProvider>().addRoundsNos([]);
    }
    await context.read<RoundProvider>().getRoundList(context: context);
    context.read<RoundProvider>().switchRounds();

    setState(() {
      _loadRound = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    roundProvider = Provider.of<RoundProvider>(context);

    if (widget.enableBack) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
      ));
    }

    return Scaffold(
        backgroundColor: Colors.white, appBar: _getToolbar(), body: _getBody());
  }

  _getToolbar() {
    double width = TabPortraitOrMobile.isTabPortraitOrMobile(context) ? 0 : 16;
    return EamAppBar(
      context: context,
      enableBack: widget.enableBack,
      title:
          "${AppLocalizations.of(context)!.rounds}(${roundProvider.roundData.length})",
      actionButton: [
        EamIconButton(
            title: AppLocalizations.of(context)!.syncAll,
            onTap: () {
              Provider.of<RoundProvider>(context, listen: false)
                  .submitAllRounds(context: context);
            },
            icon: EamIcon(
                iconName: EamIcon.refresh,
                color: Theme.of(context).primaryColor,
                height: 22,
                width: 22)),
        SizedBox(width: width),
        _roundsActionButtons(_getActionButton())
      ],
      searchRequired: true,
      onScanned: _onScanned,
      onSearchValueChange: _onSearchValueChange,
    );
  }

  _onScanned(result) {
    context.read<RoundProvider>().filterRoundList(
          searchString: result.toLowerCase(),
        );
  }

  _onSearchValueChange(res) {
    context
        .read<RoundProvider>()
        .filterRoundList(searchString: res.toLowerCase());
  }

  _tabs() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: EamTabs(
        items: _tabItems,
        onChanged: (i, status) {
          context.read<RoundProvider>().setRoundStatus(status);
          context.read<RoundProvider>().switchRounds();
        },
      ),
    );
  }

  _getActionButton() => MenuActionButton(
        iconColor: kIsWeb ? Colors.black : Colors.black,
        options: [
          MenuActionItem(
            value: 'create_round',
            name: "Create Round",
          ),
          MenuActionItem(
            value: 'delete_all',
            name: AppLocalizations.of(context)!.deleteAllString,
          ),
          MenuActionItem(
            value: 'get_all_round',
            name: AppLocalizations.of(context)!.getAllRoundString,
          ),
          if (!kIsWeb) ...[
            MenuActionItem(
              value: 'refresh',
              name: AppLocalizations.of(context)!.refreshString,
            ),
          ]
        ],
        onOptionItemSelected: (item) async {
          switch (item.value) {
            case "create_round":
              Navigator.pushNamed(context, CreateRound.routeName);
              break;
            case 'delete_all':
              UIHelper.showEamDialog(context,
                  title: 'Alert',
                  description: AppLocalizations.of(context)!
                      .areYouSureToDeleteOrdersInspectionString,
                  positiveActionLabel:
                      AppLocalizations.of(context)!.deleteString,
                  negativeActionLabel: AppLocalizations.of(context)!.noString,
                  onPositiveClickListener: () {
                RoundsHelper.deleteAllRounds().then((value) {
                  //OrdersHelper.countAllTables();
                  Navigator.of(context, rootNavigator: true).pop();
                  roundProvider.getRoundList(
                      context: context, deleteAllOrder: true);
                });
              });
              break;
            case 'get_all_round':
              //String mode = await DbHelper.getExecutionMode();
              await _getRoundsFromUMP();
              break;
            case 'refresh':
              await SyncEngine().receive();
              break;
          }
        },
      );

  Widget _roundsActionButtons(Widget icon) {
    return Container(
      height: 45,
      width: 45,
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(6),
        border: TabPortraitOrMobile.isTabPortraitOrMobile(context)
            ? Border.all(color: Colors.transparent)
            : Border.all(color: HexColor("#D5DADD")),
      ),
      child: Center(child: icon),
    );
  }

  _getBody() {
    return Padding(
      padding: Dimensions.padding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _loadRound ? SizedBox() : _tabs(),
          SizedBox(height: 16),
          _getMobileBody(),
          SizedBox(height: 16),
        ],
      ),
    );
  }

  _getMobileBody() {
    return Expanded(
      child: SingleChildScrollView(
        child: RoundsGridNew(
          isMobile: PlatformDetails.isMobileScreen(context) ||
              PlatformDetails.isTabPortraitScreen(context),
        ),
      ),
    );
  }

  Future<void> getRoundsInSync() async {
    try {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!
              .pleaseWaitWhileDownloadingInspectionString);

      Result result = await PAHelper.getAllRounds();

      if (result.statusCode == Status.httpOk ||
          result.statusCode == Status.httpCreated) {
        UIHelper.showSnackBar(
          context,
          message: AppLocalizations.of(context)!
              .orderInspectionDownloadSuccessfullyString,
        );
        _initProvider();
      }
    } catch (e) {
      UIHelper.showSnackBar(context, message: e.toString());

      log(e.toString());
    }
    return;
  }

  Future<void> _getRoundsFromUMP() async {
    // String mode = !kIsWeb
    //     ? Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND
    //     : Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND;

    String mode = Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND;

    if (mode ==
        (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND)) {
      bool _isCheck = await ApplicationHelper()
          .isDeviceSetting(key: AppConstants.PA_GET_ROUNDS);
      if (_isCheck) {
        _requestCheck();
      } else {
        await ApplicationHelper.addDeviceSetting(
            key: AppConstants.PA_GET_ROUNDS, value: Constants.YES);
        await PAHelper.getAllRoundsInAsync();
        _initProvider();

        UIHelper.showSnackBar(context,
            message: AppLocalizations.of(context)!
                .pleaseWaitWhileDownloadingInspectionString);
      }
    } else if (mode ==
        (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND)) {
      bool _isCheck = await ApplicationHelper()
          .isDeviceSetting(key: AppConstants.PA_GET_ROUNDS);
      if (_isCheck) {
        _requestCheck();
      } else {
        await ApplicationHelper.addDeviceSetting(
            key: AppConstants.PA_GET_ROUNDS, value: Constants.YES);
        await getRoundsInSync();
      }
    } else {
      UIHelper.showEamDialogWithOption(context,
          title: 'Select the execution mode',
          optionList: ['Foreground', 'Background'],
          dismissible: true, onOptionSelected: (index) async {
        if (index == 0) {
          await getRoundsInSync();
        } else {
          await PAHelper.getAllRoundsInAsync();

          UIHelper.showSnackBar(context,
              message: AppLocalizations.of(context)!
                  .pleaseWaitWhileDownloadingInspectionString);
        }
      });
    }
  }

  Future<void> _requestCheck() async {
    UIHelper.showEamDialog2(
      context,
      positiveActionLabel: AppLocalizations.of(context)!.yesString,
      negativeActionLabel: AppLocalizations.of(context)!.cancel,
      title: AppLocalizations.of(context)!.alertString,
      description:
          "Request already queued. Do you want to submit another request?",
      onPositiveClickListener: () async {
        UIHelper.closeDialog(context);
        _roundObjectDownload();
      },
      onNegativeClickListener: () =>
          Navigator.of(context, rootNavigator: true).pop(),
    );
  }

  _roundObjectDownload() async {
    String mode = !kIsWeb
        ? Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND
        : Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND;
    if (mode ==
        (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND)) {
      await PAHelper.getAllRoundsInAsync();

      UIHelper.showSnackBar(context,
          message:
              AppLocalizations.of(context)!.pleaseWaitDownloadingOrderString);
    } else if (mode ==
        (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND)) {
      await getRoundsInSync();
    }
  }
}
