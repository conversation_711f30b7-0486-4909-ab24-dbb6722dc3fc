import 'package:eam/be/C_CODE_HEADER.dart';
import 'package:eam/be/C_PRIORITY_HEADER.dart';
import 'package:eam/be/C_ROUND_CHARACTERISTIC_HEADER.dart';
import 'package:eam/be/C_USER_RIG_HEADER.dart';
import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/EQUIP_MEAS_POINT.dart';
import 'package:eam/be/FUNCLOC_MEAS_POINT.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/MEAS_POINT_HEADER.dart';
import 'package:eam/be/MEAS_POINT_HIST.dart';
import 'package:eam/be/ORDER_ACTION.dart';
import 'package:eam/be/ORDER_FORM.dart';
import 'package:eam/be/ORDER_HEADER.dart';
import 'package:eam/be/ORDER_HEAD_ADD.dart';
import 'package:eam/be/ORDER_HEAD_CUST_ADD.dart';
import 'package:eam/be/ORDER_LONG_TEXT_ADD.dart';
import 'package:eam/be/ORDER_LONG_TEXT_VIEW.dart';
import 'package:eam/be/ORDER_MATERIAL.dart';
import 'package:eam/be/ORDER_MEAS_DOC.dart';
import 'package:eam/be/ORDER_OBJECT_LIST.dart';
import 'package:eam/be/ORDER_OPERATIONS.dart';
import 'package:eam/be/ORDER_PARTNER.dart';
import 'package:eam/be/ORDER_TIME_CONFIRM.dart';
import 'package:eam/be/ORDER_USER_STATUS.dart';
import 'package:eam/be/ROUND_HEADER.dart';
import 'package:eam/be/ROUND_HISTORY.dart';
import 'package:eam/be/ROUND_OBJECTS.dart';
import 'package:eam/helpers/application_helper.dart';
import 'package:eam/helpers/order_helper.dart';
import 'package:eam/models/generic_ron_model.dart';
import 'package:eam/models/home/<USER>';
import 'package:eam/models/rounds/round_history_item.dart';
import 'package:eam/models/rounds/round_hostory.dart';
import 'package:eam/models/rounds/rounds_list_model.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:logger/Logger.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

class RoundsHelper {
  static const className = 'RoundsHelper';

  static Future<ROUND_HEADER> getRoundHeader({required String roundId}) {
    try {
      final String whereClause =
          ROUND_HEADER.FIELD_ROUND_ID + " = '" + roundId + "'";
      return AppDatabaseManager()
          .select(DBInputEntity(ROUND_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause))
          .then((dataList) {
        if (dataList.isNotEmpty) {
          return ROUND_HEADER.fromJson(dataList.first);
        } else {
          throw Exception('Round not found for ID: $roundId');
        }
      });
    } catch (e) {
      Logger.logError(className, 'getRoundHeader', e.toString());
      throw e;
    }
  }

  static Future<int> getOrdersCount() async {
    String? roundType = (await ApplicationHelper.getRoundsOrderType(
        meaurementContext: Constants.MEAS_CTX_ROUND));
    String whereClause =
        ORDER_HEADER.FIELD_ORDER_TYP + " = '" + roundType! + "'";
    var query =
        "SELECT COUNT(*) AS count FROM ${ORDER_HEADER.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return int.parse(dataList[0]["count"]);
      }
    } catch (e) {
      throw e;
    }
    return 0;
  }

  static Future<void> deleteAllRounds() async {
    await OrdersHelper.deleteAllOrders(
        meausrementContext: Constants.MEAS_CTX_ROUND);
  }

  static Future<List<ROUND_OBJECTS>> getAllRoundObjectLists(
      {required String roundId}) async {
    List<ROUND_OBJECTS> roundObjectList = [];

    try {
      String query = "${ROUND_OBJECTS.FIELD_ROUND_ID} = '$roundId'";

      roundObjectList = (await AppDatabaseManager().select(
              DBInputEntity(ROUND_OBJECTS.TABLE_NAME, {})
                ..setWhereClause(query)))
          .map((e) => ROUND_OBJECTS.fromJson(e))
          .toList();
    } catch (e) {
      throw e;
    }

    return roundObjectList;
  }

  static Future<int> getMeasuringPointTotalCount(
      {required final ORDER_OBJECT_LIST orderObjectList}) async {
    String whereClause = "";
    if (orderObjectList.equip_no == null || orderObjectList.equip_no!.isEmpty) {
      whereClause = ORDER_MEAS_DOC.FIELD_MEAS_POINT +
          " in (SELECT " +
          FUNCLOC_MEAS_POINT.FIELD_MEASURE_POINT +
          " FROM " +
          FUNCLOC_MEAS_POINT.TABLE_NAME +
          " WHERE " +
          FUNCLOC_MEAS_POINT.FIELD_FUNC_LOC +
          " = '" +
          orderObjectList.func_loc! +
          "')" +
          " AND " +
          ORDER_MEAS_DOC.FIELD_ORDER_NO +
          " = '" +
          orderObjectList.order_no! +
          "'";
    } else {
      whereClause = ORDER_MEAS_DOC.FIELD_MEAS_POINT +
          " in (SELECT " +
          EQUIP_MEAS_POINT.FIELD_MEASURE_POINT +
          " FROM " +
          EQUIP_MEAS_POINT.TABLE_NAME +
          " WHERE " +
          EQUIP_MEAS_POINT.FIELD_EQUNR +
          " = '" +
          orderObjectList.equip_no! +
          "')" +
          " AND " +
          ORDER_MEAS_DOC.FIELD_ORDER_NO +
          " = '" +
          orderObjectList.order_no! +
          "'";
    }

    String query =
        "SELECT COUNT(*) AS count FROM ${ORDER_MEAS_DOC.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return (dataList[0]["count"]);
      }
    } catch (e) {
      throw e;
    }

    return 0;
  }

  static Future<double> getMeasuringPointReadCount(
      {required final ROUND_OBJECTS roundObject}) async {
    List<ROUND_HISTORY> historyList = await getRoundHistoryByRoundIdAndItemNo(
      roundId: roundObject.round_id.toString(),
      itemNo: roundObject.item_no.toString(),
    );

    if (historyList.isEmpty) return 0;

    // Sort by readDate DESC, then by readTime DESC
    historyList.sort((a, b) {
      DateTime aDateTime = Utils.parseDateTime(a.read_date, a.read_time);
      DateTime bDateTime = Utils.parseDateTime(b.read_date, b.read_time);
      return bDateTime.compareTo(aDateTime); // descending
    });

    return double.parse(historyList.first.reading_value.toString());
  }

  static Future<int> getAllRoundsCount() async {
    int count = 0;
    try {
      List<String> _orderTypes =
          await ApplicationHelper.getOrderTypesByMeasContexts(
              measurementContext: Constants.MEAS_CTX_ROUND);
      if (_orderTypes.isNotEmpty) {
        String whereClause = ORDER_HEADER.FIELD_ORDER_TYP +
            " IN (${_orderTypes.map((type) => "'$type'").join(',')})" +
            " AND (" +
            ORDER_HEADER.FIELD_HISTORY_FLAG +
            " IS NULL OR " +
            ORDER_HEADER.FIELD_HISTORY_FLAG +
            " = '')";

        count = (await AppDatabaseManager().execute(
                "SELECT COUNT(*) AS COUNT FROM ${ORDER_HEADER.TABLE_NAME} WHERE $whereClause"))[
            0]["COUNT"];
      }
    } catch (e) {
      Logger.logError('RoundsHelper', 'getAllRoundsCount', e.toString());
    }

    return count;
  }

  /// Implementation of getRoundsViewModels
  static Future<List<GenericRONModel>> getRoundsModelList(
      {required BuildContext context}) async {
    List<GenericRONModel> models = [];
    // List<String> _orderTypes =
    //     await ApplicationHelper.getOrderTypesByMeasContexts(
    //         measurementContext: Constants.MEAS_CTX_ROUND);
    // if (_orderTypes.isNotEmpty) {
    //   String _where = ORDER_HEADER.FIELD_ORDER_TYP +
    //       " IN (${_orderTypes.map((type) => "'$type'").join(',')})";

    //   try {
    //     List<dynamic> dataList = await AppDatabaseManager().select(
    //         DBInputEntity(ORDER_HEADER.TABLE_NAME, {})
    //           ..setWhereClause(_where +
    //               " ORDER BY SYNC_STATUS DESC,OBJECT_STATUS DESC," +
    //               ORDER_HEADER.FIELD_BASC_START_DAT +
    //               " ASC"));
    //     if (dataList.length > 0) {
    //       for (var data in dataList) {
    //         models.add(
    //           (await getRoundsModel(
    //               orderHeader: ORDER_HEADER.fromJson(data), context: context)),
    //         );
    //       }
    //     }
    //   } catch (e) {
    //     Logger.logError(
    //         'RoundsHelper', 'getRoundsModelList (getOrders)', "Error: ${e}");
    //   }
    // }

    List<dynamic> dataList = await AppDatabaseManager()
        .select(DBInputEntity(ROUND_HEADER.TABLE_NAME, {}));

    if (dataList.length > 0) {
      for (var data in dataList) {
        models.add(
          (await getRoundsModel(
              roundHeader: ROUND_HEADER.fromJson(data), context: context)),
        );
      }
    }

    return models;
  }

  static Future<List<GenericRONModel>> getRoundsModelListByOrderNos(
      {required BuildContext context, required List<String> orderNos}) async {
    List<GenericRONModel> models = [];

    // if (orderNos.isEmpty) {
    //   return models;
    // }

    // List<String> _orderTypes =
    //     await ApplicationHelper.getOrderTypesByMeasContexts(
    //         measurementContext: Constants.MEAS_CTX_ROUND);
    // if (_orderTypes.isNotEmpty) {
    //   String _where = ORDER_HEADER.FIELD_ORDER_TYP +
    //       " IN (${_orderTypes.map((type) => "'$type'").join(',')})" +
    //       " AND ${ORDER_HEADER.FIELD_ORDER_NO} IN (${orderNos.map((no) => "'$no'").join(',')})";

    //   try {
    //     List<dynamic> dataList = await AppDatabaseManager().select(
    //         DBInputEntity(ORDER_HEADER.TABLE_NAME, {})
    //           ..setWhereClause(_where +
    //               " ORDER BY SYNC_STATUS DESC, OBJECT_STATUS DESC, " +
    //               ORDER_HEADER.FIELD_BASC_START_DAT +
    //               " ASC"));

    //     if (dataList.isNotEmpty) {
    //       for (var data in dataList) {
    //         models.add(
    //           await getRoundsModel(
    //               roundHeader: ORDER_HEADER.fromJson(data), context: context),
    //         );
    //       }
    //     }
    //   } catch (e) {
    //     Logger.logError(
    //         'RoundsHelper', 'getRoundsModelListByOrderNos', "Error: ${e}");
    //   }
    // }
    return models;
  }

  static Future<int> getEquipmentRecordFieldCnt(
      {required String equipmentNo, required String orderNo}) async {
    try {
      String whereClause = ORDER_MEAS_DOC.FIELD_MEAS_POINT +
          " IN (SELECT " +
          EQUIP_MEAS_POINT.FIELD_MEASURE_POINT +
          " FROM " +
          EQUIP_MEAS_POINT.TABLE_NAME +
          " WHERE " +
          EQUIP_MEAS_POINT.FIELD_EQUNR +
          " = '" +
          equipmentNo +
          "') AND " +
          ORDER_HEADER.FIELD_ORDER_NO +
          " = '" +
          orderNo +
          "'";
      String query =
          "SELECT COUNT(*) AS count FROM ${ORDER_MEAS_DOC.TABLE_NAME} WHERE $whereClause";
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return (dataList[0]["count"]);
      }
    } catch (e) {
      throw e;
    }

    return 0;
  }

  static Future<int> getFuncLocRecordFieldCnt(
      {required String funcLoc, required String orderNo}) async {
    try {
      String whereClause = ORDER_MEAS_DOC.FIELD_MEAS_POINT +
          " IN (SELECT " +
          FUNCLOC_MEAS_POINT.FIELD_MEASURE_POINT +
          " FROM " +
          FUNCLOC_MEAS_POINT.TABLE_NAME +
          " WHERE " +
          FUNCLOC_MEAS_POINT.FIELD_FUNC_LOC +
          " = '" +
          funcLoc +
          "') AND " +
          ORDER_HEADER.FIELD_ORDER_NO +
          " = '" +
          orderNo +
          "'";
      String query =
          "SELECT COUNT(*) AS count FROM ${ORDER_MEAS_DOC.TABLE_NAME} WHERE $whereClause";
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return (dataList[0]["count"]);
      }
    } catch (e) {
      throw e;
    }

    return 0;
  }

  static Future<String?> getSuperFuncLoc({required String funcLoc}) async {
    try {
      final String whereClause =
          FUNC_LOC_HEADER.FIELD_FUNC_LOC + "= '" + funcLoc + "'";

      List dataList = await AppDatabaseManager().select(
          DBInputEntity(FUNC_LOC_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return FUNC_LOC_HEADER.fromJson(dataList[0]).super_func_loc_desc;
      }
    } catch (e) {
      throw e;
    }

    return "";
  }

  static Future<RoundsListModel> getRoundsObjectListModel(
      {required ROUND_OBJECTS roundObject}) async {
    String equipment = "";
    String funcLoc = "";

    if (!Utils.isNullOrEmpty(roundObject.equip_no)) {
      equipment = roundObject.equip_desc! + " (" + roundObject.equip_no! + ")";
    }
    if (!Utils.isNullOrEmpty(roundObject.func_loc)) {
      funcLoc = roundObject.func_loc! + " (" + roundObject.func_loc_desc! + ")";
    }

    double recorded =
        await getMeasuringPointReadCount(roundObject: roundObject);

    double lastOilChange = double.parse(roundObject.trip_counter.toString());

    int currentReading = 0;
    double hrsSinceLastOilChange = recorded + currentReading - lastOilChange;

    bool isToggled = roundObject.delta_flag == "X" ? true : false;

    RoundsListModel roundsListModel = RoundsListModel(
      roundObject: roundObject,
      roundId: roundObject.round_id.toString(),
      itemNO: roundObject.item_no.toString(),
      status: roundObject.objectStatus.name,
      measPointDesc: roundObject.meas_point_desc ?? "",
      equipment: equipment,
      funcLoc: funcLoc,
      currentReading: currentReading,
      recorded: recorded,
      lastOilChange: lastOilChange,
      isToggled: isToggled,
      key: roundObject.lid.toString(),
    );

    return roundsListModel;
  }

  /**fetch all the round hostory of a round */
  static Future<List<ROUND_HISTORY>> getRoundHistoryByRoundId(
      {required String roundId}) async {
    try {
      String whereClause =
          ROUND_OBJECTS.FIELD_ROUND_ID + " = '" + roundId + "'";
      List<ROUND_HISTORY> roundHistory = (await AppDatabaseManager().select(
              DBInputEntity(ROUND_HISTORY.TABLE_NAME, {})
                ..setWhereClause(whereClause)))
          .map((e) => ROUND_HISTORY.fromJson(e))
          .toList();
      return roundHistory;
    } catch (e) {
      Logger.logError(className, 'getRoundHistory', e.toString());
      throw e;
    }
  }

  /**
   * fetch all the round history of a round by roundId and item of the round
   */
  static Future<List<ROUND_HISTORY>> getRoundHistoryByRoundIdAndItemNo(
      {required String roundId, required String itemNo}) async {
    try {
      String whereClause = ROUND_OBJECTS.FIELD_ROUND_ID +
          " = '" +
          roundId +
          "' AND " +
          ROUND_HISTORY.FIELD_ITEM_NO +
          " = '" +
          itemNo +
          "'";
      List<ROUND_HISTORY> roundHistory = (await AppDatabaseManager().select(
              DBInputEntity(ROUND_HISTORY.TABLE_NAME, {})
                ..setWhereClause(whereClause)))
          .map((e) => ROUND_HISTORY.fromJson(e))
          .toList();
      return roundHistory;
    } catch (e) {
      Logger.logError(className, 'getRoundHistory', e.toString());
      throw e;
    }
  }

  static Future<List<RoundsListModel>> getRoundsObjectListModels(
      {required List<ROUND_OBJECTS> roundObjectList}) async {
    List<RoundsListModel> roundsListModel = [];
    for (ROUND_OBJECTS roundObject in roundObjectList) {
      roundsListModel
          .add(await getRoundsObjectListModel(roundObject: roundObject));
    }
    return roundsListModel;
  }

  static Future<ORDER_MEAS_DOC?> getOrderMeasuringDoc(
      {required final String measPoint, required final String orderNo}) async {
    try {
      final String whereClause = ORDER_MEAS_DOC.FIELD_MEAS_POINT +
          " = '" +
          measPoint +
          "' AND " +
          ORDER_MEAS_DOC.FIELD_ORDER_NO +
          " = '" +
          orderNo +
          "'";

      List dataList = await AppDatabaseManager().select(
          DBInputEntity(ORDER_MEAS_DOC.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return ORDER_MEAS_DOC.fromJson(dataList[0]);
      }
    } catch (e) {
      throw e;
    }
    return null;
  }

  // static Future<ROUND_OBJECTS> getOrderObjectList(
  //     {required final String roundId}) async {
  //   try {
  //     final String whereClause = FieldLid + " = '" + lid + "'";

  //     List dataList = await AppDatabaseManager().select(
  //         DBInputEntity(ORDER_OBJECT_LIST.TABLE_NAME, {})
  //           ..setWhereClause(whereClause));
  //     if (dataList.length > 0) {
  //       return ORDER_OBJECT_LIST.fromJson(dataList[0]);
  //     }
  //   } catch (e) {
  //     throw e;
  //   }
  //   return null;
  // }

  static Future<MEAS_POINT_HEADER?> getMeasuringPoint(
      {required final String mesPoint}) async {
    try {
      final String whereClause =
          MEAS_POINT_HEADER.FIELD_MEAS_POINT + " = '" + mesPoint + "'";

      List dataList = await AppDatabaseManager().select(
          DBInputEntity(MEAS_POINT_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return MEAS_POINT_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      throw e;
    }
    return null;
  }

  static Future<List<MEAS_POINT_HEADER>> getMeasuringPoints(
      {required final ORDER_OBJECT_LIST orderObjectList}) async {
    List<MEAS_POINT_HEADER> measPointHeaders = [];
    try {
      String whereClause = "";
      if (orderObjectList.equip_no == null ||
          orderObjectList.equip_no!.isEmpty) {
        whereClause = MEAS_POINT_HEADER.FIELD_MEAS_POINT +
            " in (SELECT " +
            FUNCLOC_MEAS_POINT.FIELD_MEASURE_POINT +
            " FROM " +
            FUNCLOC_MEAS_POINT.TABLE_NAME +
            " WHERE " +
            FUNCLOC_MEAS_POINT.FIELD_FUNC_LOC +
            " = '" +
            orderObjectList.func_loc! +
            "')";
      } else {
        whereClause = MEAS_POINT_HEADER.FIELD_MEAS_POINT +
            " in (SELECT " +
            EQUIP_MEAS_POINT.FIELD_MEASURE_POINT +
            " FROM " +
            EQUIP_MEAS_POINT.TABLE_NAME +
            " WHERE " +
            EQUIP_MEAS_POINT.FIELD_EQUNR +
            " = '" +
            orderObjectList.equip_no! +
            "')";
      }
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(MEAS_POINT_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          measPointHeaders.add(MEAS_POINT_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      throw e;
    }
    return measPointHeaders;
  }

  static Future<EQUIP_HEADER?> getEquipmentHeader(
      {required String equipNo}) async {
    try {
      final String whereClause =
          EQUIP_HEADER.FIELD_EQUNR + " = '" + equipNo + "'";

      List dataList = await AppDatabaseManager().select(
          DBInputEntity(EQUIP_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return EQUIP_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      throw e;
    }
    return null;
  }

  static Future<List<ORDER_HEADER>> getAllRounds() async {
    List<ORDER_HEADER> headers = [];
    try {
      String? roundsOrderType = (await ApplicationHelper.getRoundsOrderType(
          meaurementContext: Constants.MEAS_CTX_ROUND));
      final String whereClause = ORDER_HEADER.FIELD_ORDER_TYP +
          " = '" +
          roundsOrderType! +
          "' AND (" +
          ORDER_HEADER.FIELD_HISTORY_FLAG +
          " IS NULL OR " +
          ORDER_HEADER.FIELD_HISTORY_FLAG +
          " = '')";
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(ORDER_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          headers.add(ORDER_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError('OrderHelper', 'getAllOrders', e.toString());
    }

    return headers;
  }

  static Future<List<ORDER_HEADER>> getAllModifiedRounds() async {
    List<ORDER_HEADER> headers = [];
    try {
      String? roundsOrderType = await ApplicationHelper.getRoundsOrderType(
          meaurementContext: Constants.MEAS_CTX_ROUND);
      final String whereClause = "(" +
          ORDER_HEADER.FIELD_ORDER_TYP +
          " = '" +
          roundsOrderType! +
          "') AND (" +
          ORDER_HEADER.FIELD_HISTORY_FLAG +
          " IS NULL OR " +
          ORDER_HEADER.FIELD_HISTORY_FLAG +
          " = '') AND (" +
          FieldObjectStatus +
          " = '" +
          ObjectStatus.modify.index.toString() +
          "' OR " +
          FieldObjectStatus +
          " = '" +
          ObjectStatus.add.index.toString() +
          "') AND (" +
          FieldSyncStatus +
          " NOT IN (1,2))";
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(ORDER_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          headers.add(ORDER_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      throw e;
    }
    return headers;
  }

  ///Implementation of getRoundsViewModel
  static Future<GenericRONModel> getRoundsModel(
      {required ROUND_HEADER roundHeader,
      required BuildContext context}) async {
    GenericRONModel viewModel =
        GenericRONModel(key: roundHeader.round_id.toString());
    viewModel.syncStatus = roundHeader.syncStatus;
    viewModel.infoMsgCat = roundHeader.infoMsgCat ?? "";
    if (roundHeader.created_at != null) {
      viewModel.date = Utils.getDateInDeviceFormat(
          timestamp: Utils.getTimestampFromServerDate(roundHeader.created_on!));
    }

    String whereClause =
        C_USER_RIG_HEADER.FIELD_RIG_NO + "=" + "'${roundHeader.rig_no}'";

    List<C_USER_RIG_HEADER> rigDetails = (await AppDatabaseManager().select(
            DBInputEntity(C_USER_RIG_HEADER.TABLE_NAME, {})
              ..setWhereClause(whereClause)))
        .map((e) => C_USER_RIG_HEADER.fromJson(e))
        .toList();

    List<C_ROUND_CHARACTERISTIC_HEADER> roundCharacteristicHeader =
        await getroundCharacteristicHeadersByCharcNum(
            roundHeader.characteristic.toString());

    List<ROUND_OBJECTS> roundObjects =
        await getRundObjectsByRoundId(roundHeader.round_id.toString());

    //find completed count
    int pendingRoundObjectsCount =
        await getPendingRoundObjectsByRoundId(roundHeader.round_id.toString());

    int completeRoundObjectsCount = await getCompletedRoundObjectsByRoundId(
        roundHeader.round_id.toString());

    int totalRoundObjectsCount = roundObjects.length;

    viewModel.rigName =
        rigDetails.isNotEmpty ? rigDetails.first.rig_description : "NA";

    //set panding, completed and total count in viewModel subtitles
    viewModel.subTitle1 = pendingRoundObjectsCount.toString();
    viewModel.subTitle2 = completeRoundObjectsCount.toString();
    viewModel.subTitle3 = totalRoundObjectsCount.toString();
    //find

    if (roundCharacteristicHeader.isNotEmpty) {
      viewModel.title =
          "${roundCharacteristicHeader.first.charac_name} (${roundCharacteristicHeader.first.charac_desc})";
    }

    // if (roundHeader.basc_fin_dat != null) {
    //   viewModel.endDate = Utils.getDateInDeviceFormat(
    //       timestamp:
    //           Utils.getTimestampFromServerDate(roundHeader.basc_fin_dat!));
    // }

//     if (!Utils.isNullOrEmpty(roundHeader.priority_id)) {
//       C_PRIORITY_HEADER? header = await ApplicationHelper.getPriorityHeader(
//           priority: roundHeader.priority_id!);
//       if (header != null) {
//         viewModel.priority = header.priority!;
//         viewModel.priorityDesc = header.priority_desc!;
//       }
//     }

//     if (Utils.isNullOrEmpty(roundHeader.order_no)) {
//       viewModel.subTitle1 = roundHeader.work_cntr!;
//       viewModel.subTitleIcon1 = Icons.store;

//       viewModel.subTitle2 = '';
//     } else {
//       viewModel.subTitle1 = roundHeader.order_no!;
//       viewModel.subTitleIcon1 = FontAwesomeIcons.gauge;

//       viewModel.subTitle2 = roundHeader.work_cntr!;
//       viewModel.subTitleIcon2 = Icons.store;
//     }

// //		viewModel.getSubTitle3().setValue(orderHeader.getEQUIP_DESC());

//     viewModel.subTitle3 = roundHeader.func_loc_desc!;
//     viewModel.functionalLocation = roundHeader.func_loc_desc!;
//     viewModel.type = roundHeader.order_typ ?? '';
//     if (!Utils.isNullOrEmpty(roundHeader.equip_id)) {
//       viewModel.subTitle4 = roundHeader.equip_desc!;
//       viewModel.equipmentName = roundHeader.equip_desc!;
//       viewModel.equipmentNumber = roundHeader.equip_id ?? "";
//     } else {
//       viewModel.subTitle4 = '';
//     }
    switch (roundHeader.syncStatus) {
      case SyncStatus.error:
        viewModel.status = Colors.red;
        // await _deleteOrderAction(orderHeader: roundHeader);
        break;
      case SyncStatus.none:
        viewModel.status = Colors.transparent;

        if (roundHeader.objectStatus == ObjectStatus.add ||
            roundHeader.objectStatus == ObjectStatus.modify) {
          viewModel.status = Theme.of(context).primaryColor;
        }

        // viewModel.isCompleted = ((!Utils.isNullOrEmpty(roundHeader.syst_stat) &&
        //         roundHeader.syst_stat!.contains(Constants.TECO)) ||
        //     (await OrdersHelper.isOrderActionPresent(
        //         orderNo: roundHeader.order_no!)));

        break;
      case SyncStatus.sent:
      case SyncStatus.queued:
        viewModel.status = Colors.orange;
        // viewModel.isCompleted = (!Utils.isNullOrEmpty(roundHeader.syst_stat) &&
        //         roundHeader.syst_stat!.contains(Constants.TECO)) ||
        //     await OrdersHelper.isOrderActionPresent(
        //         orderNo: roundHeader.order_no!);
        break;
    }

    if (roundHeader.objectStatus == ObjectStatus.add ||
        roundHeader.objectStatus == ObjectStatus.modify) {
      try {
        viewModel.isSubmitRequired =
            !(await SettingsHelper().isInOutBoxQueue(roundHeader.lid)) &&
                !(await SettingsHelper().isInSentItems(roundHeader.lid));
      } catch (e) {
        //Logger.e(e.getMessage());
        viewModel.isSubmitRequired = false;
      }
    } else {
      viewModel.isSubmitRequired = false;
    }

    return viewModel;
  }

  static Future<int> getPendingRoundObjectsByRoundId(String roundId) async {
    try {
      var query =
          "SELECT COUNT(*) AS COUNT FROM ROUND_OBJECTS WHERE READING_VALUE IS NULL OR READING_VALUE = '' AND ROUND_ID=" +
              roundId;

      var dataList = await AppDatabaseManager().execute(query);
      var count = 0;
      if (dataList != null && dataList.isNotEmpty) {
        count = dataList[0]['COUNT'] ?? 0;
      }

      return count;
    } catch (e) {
      Logger.logError(
          'RoundsHelper', 'getPendingRoundObjectsByRoundId', e.toString());
      return 0;
    }
  }

  static Future<int> getCompletedRoundObjectsByRoundId(String roundId) async {
    try {
      var query =
          "SELECT COUNT(*) AS COUNT, ROUND_ID FROM ROUND_OBJECTS WHERE READING_VALUE IS NOT NULL AND READING_VALUE != '' AND ROUND_ID=" +
              roundId;

      var dataList = await AppDatabaseManager().execute(query);
      var count = 0;
      if (dataList != null && dataList.isNotEmpty) {
        count = dataList[0]['COUNT'] ?? 0;
      }
      return count;
    } catch (e) {
      Logger.logError(
          'RoundsHelper', 'getCompletedRoundObjectsByRoundId', e.toString());
      return 0;
    }
  }

  static Future<List<ROUND_OBJECTS>> getRundObjectsByRoundId(
      String roundId) async {
    String whereClause =
        ROUND_OBJECTS.FIELD_ROUND_ID + " = '" + roundId.toString() + "'";

    List<ROUND_OBJECTS> result = (await AppDatabaseManager().select(
            DBInputEntity(ROUND_OBJECTS.TABLE_NAME, {})
              ..setWhereClause(whereClause)))
        .map((e) => ROUND_OBJECTS.fromJson(e))
        .toList();

    return result;
  }

  static Future<List<C_ROUND_CHARACTERISTIC_HEADER>>
      getroundCharacteristicHeadersByCharcNum(String charcNum) async {
    String whereClause = C_ROUND_CHARACTERISTIC_HEADER.FIELD_CHARAC_NUM +
        " = '" +
        charcNum +
        "'";

    //fetch the characterstics Header
    List<dynamic> roundCharacteristicDataList = await AppDatabaseManager()
        .select(DBInputEntity(C_ROUND_CHARACTERISTIC_HEADER.TABLE_NAME, {})
          ..setWhereClause(whereClause));

    List<C_ROUND_CHARACTERISTIC_HEADER> roundCharacteristicHeader =
        roundCharacteristicDataList
            .map((e) => C_ROUND_CHARACTERISTIC_HEADER.fromJson(e))
            .toList();
    return roundCharacteristicHeader;
  }

  static Future<PieChartSyncStatus> getSyncStatusCount() async {
    PieChartSyncStatus pieChartSyncStatus = PieChartSyncStatus();

    try {
      String? roundsOrderType = (await ApplicationHelper.getRoundsOrderType(
          meaurementContext: Constants.MEAS_CTX_ROUND));

      String syncStatusQuery =
          "SELECT $FieldSyncStatus, COUNT(*) AS `count` FROM ${ORDER_HEADER.TABLE_NAME} WHERE " +
              ORDER_HEADER.FIELD_ORDER_TYP +
              " = '" +
              roundsOrderType! +
              "'" +
              " GROUP BY $FieldSyncStatus";
      String objectStatusQuery =
          "SELECT $FieldObjectStatus, COUNT(*) AS `count` FROM ${ORDER_HEADER.TABLE_NAME} WHERE " +
              ORDER_HEADER.FIELD_ORDER_TYP +
              " = '" +
              roundsOrderType +
              "'" +
              " GROUP BY $FieldObjectStatus";

      List<dynamic> syncStatusDataList =
          await AppDatabaseManager().execute(syncStatusQuery);
      List<dynamic> objectStatusDataList =
          await AppDatabaseManager().execute(objectStatusQuery);
      pieChartSyncStatus.totalCount = await getSyncStatusTotalCount();
      if (syncStatusDataList.isNotEmpty) {
        for (var value in syncStatusDataList) {
          var syncStatus = SyncStatus.values[value["SYNC_STATUS"]];
          if (syncStatus == SyncStatus.none) {
            pieChartSyncStatus.unchangedCount =
                double.parse(value["count"].toString());
          } else if (syncStatus == SyncStatus.sent ||
              syncStatus == SyncStatus.queued) {
            pieChartSyncStatus.queuedOrSendCount +=
                double.parse(value["count"].toString());
            double.parse(value["count"].toString());
          } else if (syncStatus == SyncStatus.error) {
            pieChartSyncStatus.errorCount =
                double.parse(value["count"].toString());
          }
        }
      }
      if (objectStatusDataList.isNotEmpty) {
        for (var value in objectStatusDataList) {
          var objectStatus = ObjectStatus.values[value["OBJECT_STATUS"]];
          if (objectStatus == ObjectStatus.add ||
              objectStatus == ObjectStatus.modify) {
            pieChartSyncStatus.addModifiedCount +=
                double.parse(value["count"].toString());
          }
        }
      }
    } catch (e) {
      Logger.logError(className, 'getSyncStatusPercentage', e.toString());
    }
    return pieChartSyncStatus;
  }

  static Future<int> getSyncStatusTotalCount() async {
    String? roundsOrderType = (await ApplicationHelper.getRoundsOrderType(
        meaurementContext: Constants.MEAS_CTX_ROUND));
    String countQuery =
        "select count(*) as total from ${ORDER_HEADER.TABLE_NAME} WHERE " +
            ORDER_HEADER.FIELD_ORDER_TYP +
            " = '" +
            roundsOrderType! +
            "'";
    List<dynamic> totalCountData =
        await AppDatabaseManager().execute(countQuery);
    if (totalCountData.isNotEmpty) {
      return totalCountData[0]['total'];
    }
    return 0;
  }

  static Future<void> _deleteOrderAction(
      {required ORDER_HEADER orderHeader}) async {
    List<dynamic> data = await AppDatabaseManager().select(
        DBInputEntity(ORDER_ACTION.TABLE_NAME, {})
          ..setWhereClause(
              '${ORDER_ACTION.FIELD_ORDER_NO} = "${orderHeader.order_no}"'));
    if (data.isNotEmpty) {
      await AppDatabaseManager().delete(DBInputEntity(
          ORDER_ACTION.TABLE_NAME, ORDER_ACTION.fromJson(data[0]).toJson()));
    }
  }

  static Future<bool> isEditable(
      {required final ROUND_HEADER roundHeader}) async {
    if (roundHeader.round_status == "OPN") {
      return true;
    } else {
      return false;
    }
  }

  static Future<bool> updateRoundObject(ROUND_OBJECTS round_objects) async {
    var result = await AppDatabaseManager().update(
        DBInputEntity(ROUND_OBJECTS.TABLE_NAME, round_objects.toJson()));
    return result;
  }

  static Future<bool> updateRondHeaderObjectStatus(
      String roundId, ObjectStatus objectStatus) async {
    ROUND_HEADER round_header = await getRoundHeader(roundId: roundId);
    round_header.objectStatus = objectStatus;
    var result = await AppDatabaseManager()
        .update(DBInputEntity(ROUND_HEADER.TABLE_NAME, round_header.toJson()));
    return result;
  }

  static Future<bool> isRoundCompletedPrtially(
      {required String roundId}) async {
    try {
      var count = await getPendingRoundObjectsByRoundId(roundId);
      if (count != 0) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      throw e;
    }
  }

  static Future<RoundHistoryViewModel> getRoundHistoryModel(
      {required ROUND_OBJECTS roundObject}) async {
    try {
      List<ROUND_HISTORY> history = await getRoundHistoryByRoundIdAndItemNo(
          roundId: roundObject.round_id.toString(),
          itemNo: roundObject.item_no.toString());

      // Sort by readDate DESC, then by readTime DESC
      history.sort((a, b) {
        DateTime aDateTime = Utils.parseDateTime(a.read_date, a.read_time);
        DateTime bDateTime = Utils.parseDateTime(b.read_date, b.read_time);
        return bDateTime.compareTo(aDateTime); // descending
      });

      List<RoundHistoryItem> items = history
          .map((e) => RoundHistoryItem(
              user: e.read_by ?? "",
              reading: double.parse(e.reading_value ?? '0'),
              readIngUnit: e.reading_unit.toString(),
              date: Utils.parseDateTime(e.read_date, e.read_time)))
          .toList();

      return RoundHistoryViewModel(
          title: roundObject.meas_point_desc ?? "",
          roundId: roundObject.round_id.toString(),
          history: items);
    } catch (e) {
      throw e;
    }
  }
}
