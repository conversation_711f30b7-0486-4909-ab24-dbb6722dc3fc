/*!
 * # Fomantic-UI - Reveal
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "reveal";

@import (multiple) "../../theme.config";

/*******************************
            Reveal
*******************************/

.ui.reveal {
    display: inherit;
    position: relative !important;
    font-size: 0;
}

.ui.reveal > .visible.content {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: @topZIndex !important;
    transition: @transition;
}
.ui.reveal > .hidden.content {
    position: relative !important;
    z-index: @bottomZIndex !important;
}

/* Make sure hovered element is on top of other reveal */
.ui.active.reveal .visible.content,
.ui.reveal:hover .visible.content {
    z-index: @activeZIndex !important;
}

/*******************************
              Types
*******************************/

& when (@variationRevealSlide) {
    /* --------------
          Slide
    --------------- */

    .ui.slide.reveal {
        position: relative !important;
        overflow: hidden !important;
        white-space: nowrap;
    }

    .ui.slide.reveal > .content {
        display: block;
        width: 100%;
        white-space: normal;
        float: left;
        margin: 0;
        transition: @slideTransition;
    }

    .ui.slide.reveal > .visible.content {
        position: relative !important;
    }
    .ui.slide.reveal > .hidden.content {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        transform: translateX(100%) !important;
    }
    .ui.slide.active.reveal > .visible.content,
    .ui.slide.reveal:hover > .visible.content {
        transform: translateX(-100%) !important;
    }
    .ui.slide.active.reveal > .hidden.content,
    .ui.slide.reveal:hover > .hidden.content {
        transform: translateX(0) !important;
    }

    .ui.slide.right.reveal > .visible.content {
        transform: translateX(0) !important;
    }
    .ui.slide.right.reveal > .hidden.content {
        transform: translateX(-100%) !important;
    }
    .ui.slide.right.active.reveal > .visible.content,
    .ui.slide.right.reveal:hover > .visible.content {
        transform: translateX(100%) !important;
    }
    .ui.slide.right.active.reveal > .hidden.content,
    .ui.slide.right.reveal:hover > .hidden.content {
        transform: translateX(0) !important;
    }

    .ui.slide.up.reveal > .hidden.content {
        transform: translateY(100%) !important;
    }
    .ui.slide.up.active.reveal > .visible.content,
    .ui.slide.up.reveal:hover > .visible.content {
        transform: translateY(-100%) !important;
    }
    .ui.slide.up.active.reveal > .hidden.content,
    .ui.slide.up.reveal:hover > .hidden.content {
        transform: translateY(0) !important;
    }

    .ui.slide.down.reveal > .hidden.content {
        transform: translateY(-100%) !important;
    }
    .ui.slide.down.active.reveal > .visible.content,
    .ui.slide.down.reveal:hover > .visible.content {
        transform: translateY(100%) !important;
    }
    .ui.slide.down.active.reveal > .hidden.content,
    .ui.slide.down.reveal:hover > .hidden.content {
        transform: translateY(0) !important;
    }
}

& when (@variationRevealFade) {
    /* --------------
          Fade
    --------------- */

    .ui.fade.reveal > .visible.content {
        opacity: 1;
    }
    .ui.fade.active.reveal > .visible.content,
    .ui.fade.reveal:hover > .visible.content {
        opacity: 0;
    }
}

& when (@variationRevealMove) {
    /* --------------
          Move
    --------------- */

    .ui.move.reveal {
        position: relative !important;
        overflow: hidden !important;
        white-space: nowrap;
    }

    .ui.move.reveal > .content {
        display: block;
        float: left;
        white-space: normal;
        margin: 0;
        transition: @moveTransition;
    }

    .ui.move.reveal > .visible.content {
        position: relative !important;
    }
    .ui.move.reveal > .hidden.content {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
    }
    .ui.move.active.reveal > .visible.content,
    .ui.move.reveal:hover > .visible.content {
        transform: translateX(-100%) !important;
    }
    .ui.move.right.active.reveal > .visible.content,
    .ui.move.right.reveal:hover > .visible.content {
        transform: translateX(100%) !important;
    }
    .ui.move.up.active.reveal > .visible.content,
    .ui.move.up.reveal:hover > .visible.content {
        transform: translateY(-100%) !important;
    }
    .ui.move.down.active.reveal > .visible.content,
    .ui.move.down.reveal:hover > .visible.content {
        transform: translateY(100%) !important;
    }
}

& when (@variationRevealRotate) {
    /* --------------
         Rotate
    --------------- */

    .ui.rotate.reveal > .visible.content {
        transition-duration: @transitionDuration;
        transform: rotate(0);
    }

    .ui.rotate.reveal > .visible.content,
    .ui.rotate.right.reveal > .visible.content {
        transform-origin: bottom right;
    }
    .ui.rotate.active.reveal > .visible.content,
    .ui.rotate.reveal:hover > .visible.content,
    .ui.rotate.right.active.reveal > .visible.content,
    .ui.rotate.right.reveal:hover > .visible.content {
        transform: rotate(@rotateDegrees);
    }

    .ui.rotate.left.reveal > .visible.content {
        transform-origin: bottom left;
    }
    .ui.rotate.left.active.reveal > .visible.content,
    .ui.rotate.left.reveal:hover > .visible.content {
        transform: rotate(-@rotateDegrees);
    }
}

/*******************************
              States
*******************************/

& when (@variationRevealDisabled) {
    .ui.disabled.reveal:hover > .visible.visible.content {
        position: static !important;
        display: block !important;
        opacity: 1 !important;
        top: 0 !important;
        left: 0 !important;
        right: auto !important;
        bottom: auto !important;
        transform: none !important;
    }
    .ui.disabled.reveal:hover > .hidden.hidden.content {
        display: none !important;
    }
}

/*******************************
           Coupling
*******************************/

.ui.reveal > .ui.ribbon.label {
    z-index: @overlayZIndex;
}

/*******************************
           Variations
*******************************/

/* --------------
     Visible
--------------- */

.ui.visible.reveal {
    overflow: visible;
}

/* --------------
     Instant
--------------- */

.ui.instant.reveal > .content {
    transition-delay: 0s !important;
}

/* --------------
     Sizing
--------------- */

.ui.reveal > .content {
    font-size: @medium;
}
& when not (@variationRevealSizes = false) {
    each(@variationRevealSizes, {
        @s: @@value;
        .ui.@{value}.reveal > .content {
            font-size: @s;
        }
    });
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
