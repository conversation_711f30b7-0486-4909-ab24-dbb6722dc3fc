/*******************************
         Site Settings
*******************************/

@import "variation.variables";
@supportIE: true;

/* -------------------
       Fonts
-------------------- */

@fontName: "Lato";
@fontFileNameLatin: "LatoLatin";
@fontFileNameSupplement: "Lato";

// based on https://github.com/twbs/bootstrap/blob/v5.1.3/scss/_variables.scss#L577 list
@fallbackFonts:
    system-ui,
    -apple-system,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    "Liberation Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";

@headerFont: @fontName, @fallbackFonts;
@pageFont: @fontName, @fallbackFonts;

@fontDisplay: swap;
@textRendering: optimizeLegibility;
@unicodeRangeLatin: U+0000, U+000D, U+0020-007E, U+00A0-017F, U+0192, U+0218-021B, U+0237, U+02C6-02C7, U+02C9, U+02D8-02DD, U+0394, U+03A9, U+03BC, U+03C0, U+1E80-1E85, U+2010, U+2013-2014, U+2018-201A, U+201C-201E, U+2020-2022, U+2026, U+2030, U+2039-203A, U+2044, U+20A3-20A4, U+20A7, U+20AC, U+2113, U+2122, U+2126, U+212E, U+2202, U+2206, U+220F, U+2211-2212, U+2215, U+2219-221A, U+221E, U+222B, U+2248, U+2260, U+2264-2265, U+25CA, U+F8FF, U+FB00-FB04;
@unicodeRangeSupplement: U+0180-0191, U+0193-0217, U+021C-0236, U+0238-02C5, U+02C8, U+02CA-02D7, U+02DE-036F, U+0374-0375, U+037A-037E, U+0384-038A, U+038C, U+038E-0393, U+0395-03A1, U+03A3-03A8, U+03AA-03BB, U+03BD-03BF, U+03C1-03CE, U+03D0-0486, U+0488-0513, U+1D00-1DCA, U+1DFE-1E7F, U+1E86-1E9B, U+1E9E, U+1EA0-1EF9, U+1F00-1F15, U+1F18-1F1D, U+1F20-1F45, U+1F48-1F4D, U+1F50-1F57, U+1F59, U+1F5B, U+1F5D, U+1F5F-1F7D, U+1F80-1FB4, U+1FB6-1FC4, U+1FC6-1FD3, U+1FD6-1FDB, U+1FDD-1FEF, U+1FF2-1FF4, U+1FF6-1FFE, U+2000-200F, U+2012, U+2015-2017, U+201B, U+201F, U+202F, U+2034, U+203C-203E, U+205E-205F, U+2070-2071, U+2074-2094, U+20A0-20A2, U+20A5-20A6, U+20A8-20AB, U+20AD-20B5, U+20B8-20BA, U+20DD, U+2105, U+2116-2117, U+2120, U+2132, U+214D-214E, U+2153-215F, U+2183-2184, U+2190-2199, U+21A8, U+221F, U+2229, U+2261, U+2302, U+2310, U+2320-2321, U+2460-2473, U+24EA-24F4, U+24FF-2500, U+2502, U+250C, U+2510, U+2514, U+2518, U+2C60-2C6C, U+2C74-2C77;

@importFonts: true;
@fonts: {
    @regularLatin: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameLatin}-Regular.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameLatin}-Regular.woff") format("woff"));
        font-style: normal;
        font-weight: @normal;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeLatin;
    };
    @boldLatin: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameLatin}-Bold.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameLatin}-Bold.woff") format("woff"));
        font-style: normal;
        font-weight: @bold;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeLatin;
    };
    @italicLatin: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameLatin}-Italic.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameLatin}-Italic.woff") format("woff"));
        font-style: italic;
        font-weight: @normal;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeLatin;
    };
    @boldItalicLatin: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameLatin}-BoldItalic.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameLatin}-BoldItalic.woff") format("woff"));
        font-style: italic;
        font-weight: @bold;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeLatin;
    };
    @regularSupplement: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameSupplement}-Regular.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameSupplement}-Regular.woff") format("woff"));
        font-style: normal;
        font-weight: @normal;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeSupplement;
    };
    @boldSupplement: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameSupplement}-Bold.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameSupplement}-Bold.woff") format("woff"));
        font-style: normal;
        font-weight: @bold;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeSupplement;
    };
    @italicSupplement: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameSupplement}-Italic.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameSupplement}-Italic.woff") format("woff"));
        font-style: italic;
        font-weight: @normal;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeSupplement;
    };
    @boldItalicSupplement: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontFileNameSupplement}-BoldItalic.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontFileNameSupplement}-BoldItalic.woff") format("woff"));
        font-style: italic;
        font-weight: @bold;
        font-display: @fontDisplay;
        text-rendering: @textRendering;
        unicode-range: @unicodeRangeSupplement;
    };
};

@googleFontName: @fontName;
@importGoogleFonts: false;
@googleFontSizes: "ital,wght@0,400%3B0,700%3B1,400%3B1,700";
@googleSubset: "latin";
@googleFontDisplay: "swap";

@googleProtocol: "https://";
@googleFonts: "@{googleFontName}:@{googleFontSizes}";
@googleFontRequest: "@{googleFonts}&subset=@{googleSubset}&display=@{googleFontDisplay}";
@googleFontUrl: "@{googleProtocol}fonts.googleapis.com/css2?family=@{googleFontRequest}";

@bold: bold;
@normal: normal;

/* -------------------
      Base Sizes
-------------------- */

/* This is the single variable that controls them all */
@emSize: 14px;

/* The size of page text */
@fontSize: 14px;

/* -------------------
    Border Radius
-------------------- */

/* See Power-user section below
   for explanation of @px variables
*/
@relativeBorderRadius: @relative4px;
@absoluteBorderRadius: @4px;

@defaultBorderRadius: @absoluteBorderRadius;

/* -------------------
    Brand Colors
-------------------- */

@primaryColor: @blue;
@secondaryColor: @black;

@lightPrimaryColor: @lightBlue;
@lightSecondaryColor: @lightBlack;

/* Whenever a color needs to get calculated (screen()/multiply()) out of a base color */
@blendingBaseColor: #ccc;

/* --------------
  Page Heading
--------------- */

@headerFontWeight: @bold;
@headerLineHeight: unit((18 / 14), em);

@h1: unit((28 / 14), rem);
@h2: unit((24 / 14), rem);
@h3: unit((18 / 14), rem);
@h4: unit((15 / 14), rem);
@h5: unit((14 / 14), rem);
@h6: unit((12 / 14), rem);

/* --------------
   Form Input
--------------- */

/* This adjusts the default form input across all elements */
@inputBackground: @white;
@inputVerticalPadding: @relative11px;
@inputHorizontalPadding: @relative14px;
@inputPadding: @inputVerticalPadding @inputHorizontalPadding;

/* Input Text Color */
@inputColor: @textColor;
@inputPlaceholderColor: if(iscolor(@inputColor), lighten(@inputColor, 75), @inputColor);
@inputPlaceholderFocusColor: if(iscolor(@inputColor), lighten(@inputColor, 45), @inputColor);

/* Line Height Default For Inputs in Browser (Descenders are 17px at 14px base em) */
@inputLineHeight: unit((17 / 14), em);

/* -------------------
    Focused Input
-------------------- */

/* Used on inputs, textarea etc */
@focusedFormBorderColor: #85b7d9;

/* Used on dropdowns, other larger blocks */
@focusedFormMutedBorderColor: #96c8da;

/* -------------------
        Sizes
-------------------- */

/*
  Sizes are all expressed in terms of 14px/em (default em)
  This ensures these "ratios" remain constant despite changes in EM
*/

@miniSize: (11 / 14);
@tinySize: (12 / 14);
@smallSize: (13 / 14);
@mediumSize: (14 / 14);
@largeSize: (16 / 14);
@bigSize: (18 / 14);
@hugeSize: (20 / 14);
@massiveSize: (24 / 14);

/* -------------------
        Page
-------------------- */

@pageBackground: #fff;
@pageOverflowX: hidden;

@lineHeight: 1.4285em;
@textColor: rgba(0, 0, 0, 0.87);

/* -------------------
      Paragraph
-------------------- */

@paragraphMargin: 0 0 1em;
@paragraphLineHeight: @lineHeight;

/* -------------------
       Links
-------------------- */

@linkColor: #4183c4;
@linkUnderline: none;
@linkHoverColor: if(iscolor(@linkColor), darken(saturate(@linkColor, 20), 15, relative), @linkColor);
@linkHoverUnderline: @linkUnderline;

/* -------------------
    Scroll Bars
-------------------- */

@useCustomScrollbars: true;

@customScrollbarWidth: 10px;
@customScrollbarHeight: 10px;

@trackBackground: rgba(0, 0, 0, 0.1);
@trackBorderRadius: 0;

@thumbBorderRadius: 5px;
@thumbBackground: rgba(0, 0, 0, 0.25);
@thumbTransition: color 0.2s ease;

@thumbInactiveBackground: rgba(0, 0, 0, 0.15);
@thumbHoverBackground: rgba(128, 135, 139, 0.8);

/* Inverted */
@trackInvertedBackground: rgba(255, 255, 255, 0.1);
@thumbInvertedBackground: rgba(255, 255, 255, 0.25);
@thumbInvertedInactiveBackground: rgba(255, 255, 255, 0.15);
@thumbInvertedHoverBackground: rgba(255, 255, 255, 0.35);

/* IE needs hex values */
@trackBackgroundHex: #e6e6e6;
@thumbBackgroundHex: #bfbfbf;
@trackInvertedBackgroundHex: #323232;
@thumbInvertedBackgroundHex: #656565;

/* Scrolling */
@scrollingMobileMaxHeight: 15em;
@scrollingTabletMaxHeight: 18em;
@scrollingComputerMaxHeight: 24em;
@scrollingWidescreenMaxHeight: 30em;
@overscrollBehavior: none;

/* -------------------
  Highlighted Text
-------------------- */

@highlightBackground: #cce2ff;
@highlightColor: @textColor;

@inputHighlightBackground: rgba(100, 100, 100, 0.4);
@inputHighlightColor: @textColor;

/* -------------------
       Loader
-------------------- */

@loaderSize: @relativeBig;
@loaderSpeedFast: 0.3s;
@loaderSpeed: 0.6s;
@loaderSpeedSlow: 0.9s;
@loaderLineWidth: 0.2em;
@loaderFillColor: rgba(0, 0, 0, 0.1);
@loaderLineColor: @grey;

@invertedLoaderFillColor: rgba(255, 255, 255, 0.15);
@invertedLoaderLineColor: @white;

/* -------------------
        Grid
-------------------- */

@columnCount: 16;

/* -------------------
     Transitions
-------------------- */

@defaultDuration: 0.1s;
@defaultEasing: ease;

/* -------------------
     Breakpoints
-------------------- */

@mobileBreakpoint: 320px;
@tabletBreakpoint: 768px;
@computerBreakpoint: 992px;
@largeMonitorBreakpoint: 1200px;
@widescreenMonitorBreakpoint: 1920px;

/* -------------------
      Site Colors
-------------------- */

/* ---  Colors  --- */
@red: #db2828;
@orange: #f2711c;
@yellow: #fbbd08;
@olive: #b5cc18;
@green: #21ba45;
@teal: #00b5ad;
@blue: #2185d0;
@violet: #6435c9;
@purple: #a333c8;
@pink: #e03997;
@brown: #a5673f;
@grey: #767676;
@black: #1b1c1d;

/* ---  Light Colors  --- */
@lightRed: #ff695e;
@lightOrange: #ff851b;
@lightYellow: #ffe21f;
@lightOlive: #d9e778;
@lightGreen: #2ecc40;
@lightTeal: #6dffff;
@lightBlue: #54c8ff;
@lightViolet: #a291fb;
@lightPurple: #dc73ff;
@lightPink: #ff8edf;
@lightBrown: #d67c1c;
@lightGrey: #dcddde;
@lightBlack: #545454;

/* ---   Neutrals  --- */
@fullBlack: #000;
@offWhite: #f9fafb;
@darkWhite: #f3f4f5;
@midWhite: #dcddde;
@white: #fff;

/* --- Colored Backgrounds --- */
@primaryBackground: #dff0ff;
@secondaryBackground: #f4f4f4;
@redBackground: #ffe8e6;
@orangeBackground: #ffedde;
@yellowBackground: #fff8db;
@oliveBackground: #fbfdef;
@greenBackground: #e5f9e7;
@tealBackground: #e1f7f7;
@blueBackground: #dff0ff;
@violetBackground: #eae7ff;
@purpleBackground: #f6e7ff;
@pinkBackground: #ffe3fb;
@brownBackground: #f1e2d3;
@greyBackground: #f4f4f4;
@blackBackground: #f4f4f4;

/* --- Colored Headers --- */
@primaryHeaderColor: if(iscolor(@primaryTextColor), darken(@primaryTextColor, 5), @primaryTextColor);
@secondaryHeaderColor: if(iscolor(@secondaryTextColor), darken(@secondaryTextColor, 5), @secondaryTextColor);
@redHeaderColor: if(iscolor(@redTextColor), darken(@redTextColor, 5), @redTextColor);
@oliveHeaderColor: if(iscolor(@oliveTextColor), darken(@oliveTextColor, 5), @oliveTextColor);
@greenHeaderColor: if(iscolor(@greenTextColor), darken(@greenTextColor, 5), @greenTextColor);
@yellowHeaderColor: if(iscolor(@yellowTextColor), darken(@yellowTextColor, 5), @yellowTextColor);
@blueHeaderColor: if(iscolor(@blueTextColor), darken(@blueTextColor, 5), @blueTextColor);
@tealHeaderColor: if(iscolor(@tealTextColor), darken(@tealTextColor, 5), @tealTextColor);
@pinkHeaderColor: if(iscolor(@pinkTextColor), darken(@pinkTextColor, 5), @pinkTextColor);
@violetHeaderColor: if(iscolor(@violetTextColor), darken(@violetTextColor, 5), @violetTextColor);
@purpleHeaderColor: if(iscolor(@purpleTextColor), darken(@purpleTextColor, 5), @purpleTextColor);
@orangeHeaderColor: if(iscolor(@orangeTextColor), darken(@orangeTextColor, 5), @orangeTextColor);
@brownHeaderColor: if(iscolor(@brownTextColor), darken(@brownTextColor, 5), @brownTextColor);
@greyHeaderColor: if(iscolor(@greyTextColor), darken(@greyTextColor, 5), @greyTextColor);
@blackHeaderColor: if(iscolor(@blackTextColor), darken(@blackTextColor, 5), @blackTextColor);

/* --- Colored Text --- */
@primaryTextColor: @invertedTextColor;
@secondaryTextColor: @invertedTextColor;
@redTextColor: @red;
@orangeTextColor: @orange;
@yellowTextColor: #b58105; // Yellow text is difficult to read
@oliveTextColor: #8abc1e; // Olive is difficult to read
@greenTextColor: #1ebc30; // Green is difficult to read
@tealTextColor: #10a3a3; // Teal text is difficult to read
@blueTextColor: @blue;
@violetTextColor: @violet;
@purpleTextColor: @purple;
@pinkTextColor: @pink;
@brownTextColor: @brown;
@greyTextColor: @grey;
@blackTextColor: @black;

/* --- Light Colored Text --- */
@lightPrimaryTextColor: @invertedTextColor;
@lightSecondaryTextColor: @invertedTextColor;
@lightRedTextColor: @lightRed;
@lightOrangeTextColor: @lightOrange;
@lightYellowTextColor: #b58105; // Yellow text is difficult to read
@lightOliveTextColor: #8abc1e; // Olive is difficult to read
@lightGreenTextColor: #1ebc30; // Green is difficult to read
@lightTealTextColor: #10a3a3; // Teal text is difficult to read
@lightBlueTextColor: @lightBlue;
@lightVioletTextColor: @lightViolet;
@lightPurpleTextColor: @lightPurple;
@lightPinkTextColor: @lightPink;
@lightBrownTextColor: @lightBrown;
@lightGreyTextColor: @lightGrey;
@lightBlackTextColor: @lightBlack;

/* --- Hovered Colored Text --- */
@primaryHoverTextColor: @primaryTextColor;
@secondaryHoverTextColor: @secondaryTextColor;
@redHoverTextColor: @redTextColor;
@orangeHoverTextColor: @orangeTextColor;
@yellowHoverTextColor: @yellowTextColor;
@oliveHoverTextColor: @oliveTextColor;
@greenHoverTextColor: @greenTextColor;
@tealHoverTextColor: @tealTextColor;
@blueHoverTextColor: @blueTextColor;
@violetHoverTextColor: @violetTextColor;
@purpleHoverTextColor: @purpleTextColor;
@pinkHoverTextColor: @pinkTextColor;
@brownHoverTextColor: @brownTextColor;
@greyHoverTextColor: @greyTextColor;
@blackHoverTextColor: @blackTextColor;

/* --- Colored Border --- */
@primaryBorderColor: @primaryColor;
@secondaryBorderColor: @secondaryColor;
@redBorderColor: @redTextColor;
@orangeBorderColor: @orangeTextColor;
@yellowBorderColor: @yellowTextColor;
@oliveBorderColor: @oliveTextColor;
@greenBorderColor: @greenTextColor;
@tealBorderColor: @tealTextColor;
@blueBorderColor: @blueTextColor;
@violetBorderColor: @violetTextColor;
@purpleBorderColor: @purpleTextColor;
@pinkBorderColor: @pinkTextColor;
@brownBorderColor: @brownTextColor;
@greyBorderColor: @greyTextColor;
@blackBorderColor: @blackTextColor;

/* --- Shadows --- */
@primaryRibbonShadow: if(iscolor(@primaryColor), darken(@primaryColor, 10), @primaryColor);
@secondaryRibbonShadow: if(iscolor(@secondaryColor), darken(@secondaryColor, 10), @secondaryColor);
@redRibbonShadow: if(iscolor(@red), darken(@red, 10), @red);
@orangeRibbonShadow: if(iscolor(@orange), darken(@orange, 10), @orange);
@yellowRibbonShadow: if(iscolor(@yellow), darken(@yellow, 10), @yellow);
@oliveRibbonShadow: if(iscolor(@olive), darken(@olive, 10), @olive);
@greenRibbonShadow: if(iscolor(@green), darken(@green, 10), @green);
@tealRibbonShadow: if(iscolor(@teal), darken(@teal, 10), @teal);
@blueRibbonShadow: if(iscolor(@blue), darken(@blue, 10), @blue);
@violetRibbonShadow: if(iscolor(@violet), darken(@violet, 10), @violet);
@purpleRibbonShadow: if(iscolor(@purple), darken(@purple, 10), @purple);
@pinkRibbonShadow: if(iscolor(@pink), darken(@pink, 10), @pink);
@brownRibbonShadow: if(iscolor(@brown), darken(@brown, 10), @brown);
@greyRibbonShadow: if(iscolor(@grey), darken(@grey, 10), @grey);
@blackRibbonShadow: if(iscolor(@black), darken(@black, 10), @black);

@primaryInvertedRibbonShadow: if(iscolor(@lightPrimaryColor), darken(@lightPrimaryColor, 10), @lightPrimaryColor);
@secondaryInvertedRibbonShadow: if(iscolor(@lightSecondaryColor), darken(@lightSecondaryColor, 10), @lightSecondaryColor);
@redInvertedRibbonShadow: if(iscolor(@lightRed), darken(@lightRed, 10), @lightRed);
@orangeInvertedRibbonShadow: if(iscolor(@lightOrange), darken(@lightOrange, 10), @lightOrange);
@yellowInvertedRibbonShadow: if(iscolor(@lightYellow), darken(@lightYellow, 10), @lightYellow);
@oliveInvertedRibbonShadow: if(iscolor(@lightOlive), darken(@lightOlive, 10), @lightOlive);
@greenInvertedRibbonShadow: if(iscolor(@lightGreen), darken(@lightGreen, 10), @lightGreen);
@tealInvertedRibbonShadow: if(iscolor(@lightTeal), darken(@lightTeal, 10), @lightTeal);
@blueInvertedRibbonShadow: if(iscolor(@lightBlue), darken(@lightBlue, 10), @lightBlue);
@violetInvertedRibbonShadow: if(iscolor(@lightViolet), darken(@lightViolet, 10), @lightViolet);
@purpleInvertedRibbonShadow: if(iscolor(@lightPurple), darken(@lightPurple, 10), @lightPurple);
@pinkInvertedRibbonShadow: if(iscolor(@lightPink), darken(@lightPink, 10), @lightPink);
@brownInvertedRibbonShadow: if(iscolor(@lightBrown), darken(@lightBrown, 10), @lightBrown);
@greyInvertedRibbonShadow: if(iscolor(@lightGrey), lighten(@lightGrey, 5), @lightGrey);
@blackInvertedRibbonShadow: if(iscolor(@lightBlack), lighten(@lightBlack, 5), @lightBlack);

@textShadow: none;
@invertedTextShadow: @textShadow;

@primaryTextShadow: @invertedTextShadow;
@secondaryTextShadow: @invertedTextShadow;
@redTextShadow: @invertedTextShadow;
@orangeTextShadow: @invertedTextShadow;
@yellowTextShadow: @invertedTextShadow;
@oliveTextShadow: @invertedTextShadow;
@greenTextShadow: @invertedTextShadow;
@tealTextShadow: @invertedTextShadow;
@blueTextShadow: @invertedTextShadow;
@violetTextShadow: @invertedTextShadow;
@purpleTextShadow: @invertedTextShadow;
@pinkTextShadow: @invertedTextShadow;
@brownTextShadow: @invertedTextShadow;
@greyTextShadow: @invertedTextShadow;
@blackTextShadow: @invertedTextShadow;

/* Inverted */
@lightPrimaryTextShadow: @invertedTextShadow;
@lightSecondaryTextShadow: @invertedTextShadow;
@lightRedTextShadow: @invertedTextShadow;
@lightOrangeTextShadow: @invertedTextShadow;
@lightYellowTextShadow: @textShadow;
@lightOliveTextShadow: @textShadow;
@lightGreenTextShadow: @invertedTextShadow;
@lightTealTextShadow: @textShadow;
@lightBlueTextShadow: @invertedTextShadow;
@lightVioletTextShadow: @invertedTextShadow;
@lightPurpleTextShadow: @invertedTextShadow;
@lightPinkTextShadow: @invertedTextShadow;
@lightBrownTextShadow: @invertedTextShadow;
@lightGreyTextShadow: @textShadow;
@lightBlackTextShadow: @invertedTextShadow;

/* Box Shadows */

@shadowShadow: 0 0 0 0 rgba(0, 0, 0, 0);
@borderWidth: 1px;

@primaryBoxShadow:
    0 0 0 @borderWidth @primaryBorderColor inset,
    @shadowShadow;
@primaryBoxFloatingShadow:
    0 0 0 @borderWidth @primaryBorderColor inset,
    @floatingShadow;
@secondaryBoxShadow:
    0 0 0 @borderWidth @secondaryBorderColor inset,
    @shadowShadow;
@secondaryBoxFloatingShadow:
    0 0 0 @borderWidth @secondaryBorderColor inset,
    @floatingShadow;
@redBoxShadow:
    0 0 0 @borderWidth @redBorderColor inset,
    @shadowShadow;
@redBoxFloatingShadow:
    0 0 0 @borderWidth @redBorderColor inset,
    @floatingShadow;
@orangeBoxShadow:
    0 0 0 @borderWidth @orangeBorderColor inset,
    @shadowShadow;
@orangeBoxFloatingShadow:
    0 0 0 @borderWidth @orangeBorderColor inset,
    @floatingShadow;
@yellowBoxShadow:
    0 0 0 @borderWidth @yellowBorderColor inset,
    @shadowShadow;
@yellowBoxFloatingShadow:
    0 0 0 @borderWidth @yellowBorderColor inset,
    @floatingShadow;
@oliveBoxShadow:
    0 0 0 @borderWidth @oliveBorderColor inset,
    @shadowShadow;
@oliveBoxFloatingShadow:
    0 0 0 @borderWidth @oliveBorderColor inset,
    @floatingShadow;
@greenBoxShadow:
    0 0 0 @borderWidth @greenBorderColor inset,
    @shadowShadow;
@greenBoxFloatingShadow:
    0 0 0 @borderWidth @greenBorderColor inset,
    @floatingShadow;
@tealBoxShadow:
    0 0 0 @borderWidth @tealBorderColor inset,
    @shadowShadow;
@tealBoxFloatingShadow:
    0 0 0 @borderWidth @tealBorderColor inset,
    @floatingShadow;
@blueBoxShadow:
    0 0 0 @borderWidth @blueBorderColor inset,
    @shadowShadow;
@blueBoxFloatingShadow:
    0 0 0 @borderWidth @blueBorderColor inset,
    @floatingShadow;
@violetBoxShadow:
    0 0 0 @borderWidth @violetBorderColor inset,
    @shadowShadow;
@violetBoxFloatingShadow:
    0 0 0 @borderWidth @violetBorderColor inset,
    @floatingShadow;
@purpleBoxShadow:
    0 0 0 @borderWidth @purpleBorderColor inset,
    @shadowShadow;
@purpleBoxFloatingShadow:
    0 0 0 @borderWidth @purpleBorderColor inset,
    @floatingShadow;
@pinkBoxShadow:
    0 0 0 @borderWidth @pinkBorderColor inset,
    @shadowShadow;
@pinkBoxFloatingShadow:
    0 0 0 @borderWidth @pinkBorderColor inset,
    @floatingShadow;
@brownBoxShadow:
    0 0 0 @borderWidth @brownBorderColor inset,
    @shadowShadow;
@brownBoxFloatingShadow:
    0 0 0 @borderWidth @brownBorderColor inset,
    @floatingShadow;
@greyBoxShadow:
    0 0 0 @borderWidth @greyBorderColor inset,
    @shadowShadow;
@greyBoxFloatingShadow:
    0 0 0 @borderWidth @greyBorderColor inset,
    @floatingShadow;
@blackBoxShadow:
    0 0 0 @borderWidth @blackBorderColor inset,
    @shadowShadow;
@blackBoxFloatingShadow:
    0 0 0 @borderWidth @blackBorderColor inset,
    @floatingShadow;

/* -------------------
     Alpha Colors
-------------------- */

@subtleTransparentBlack: rgba(0, 0, 0, 0.03);
@transparentBlack: rgba(0, 0, 0, 0.05);
@strongTransparentBlack: rgba(0, 0, 0, 0.1);
@veryStrongTransparentBlack: rgba(0, 0, 0, 0.15);

@subtleTransparentWhite: rgba(255, 255, 255, 0.02);
@transparentWhite: rgba(255, 255, 255, 0.08);
@strongTransparentWhite: rgba(255, 255, 255, 0.15);

/* -------------------
       Accents
-------------------- */

/* Differentiating Neutrals */
@subtleGradient: linear-gradient(transparent, @transparentBlack);

/* Differentiating Layers */
@subtleShadow: 0 1px 2px 0 @borderColor;
@bottomShadow: 0 2px 1px -1px @borderColor;
@floatingShadow:
    0 2px 4px 0 rgba(34, 36, 38, 0.12),
    0 2px 10px 0 rgba(34, 36, 38, 0.15);
@invertedFloatingShadow:
    0 2px 4px 0 rgba(225, 225, 225, 0.1),
    0 2px 10px 0 rgba(225, 225, 225, 0.5);

/*******************************
           Power-User
*******************************/

/* -------------------
    Emotive Colors
-------------------- */

/* Positive */
@positiveColor: @green;
@positiveBackgroundColor: #fcfff5;
@positiveBorderColor: #a3c293;
@positiveHeaderColor: #1a531b;
@positiveTextColor: #2c662d;

/* Negative */
@negativeColor: @red;
@negativeBackgroundColor: #fff6f6;
@negativeBorderColor: #e0b4b4;
@negativeHeaderColor: #912d2b;
@negativeTextColor: #9f3a38;

/* Info */
@infoColor: #31ccec;
@infoBackgroundColor: #f8ffff;
@infoBorderColor: #a9d5de;
@infoHeaderColor: #0e566c;
@infoTextColor: #276f86;

/* Warning */
@warningColor: #f2c037;
@warningBorderColor: #c9ba9b;
@warningBackgroundColor: #fffaf3;
@warningHeaderColor: #794b02;
@warningTextColor: #573a08;

/* -------------------
        Paths
-------------------- */

/* For source only. Modified in gulp for dist */
@imagePath: "../../themes/default/assets/images";
@fontPath: "../../themes/default/assets/fonts";

/* -------------------
       Em Sizes
-------------------- */

/*
  This rounds @size values to the closest pixel then expresses that value in (r)em.
  This ensures all size values round to exact pixels
*/
@miniRaw: unit((round(@miniSize * @emSize) / @emSize));
@tinyRaw: unit((round(@tinySize * @emSize) / @emSize));
@smallRaw: unit((round(@smallSize * @emSize) / @emSize));
@mediumRaw: unit((round(@mediumSize * @emSize) / @emSize));
@largeRaw: unit((round(@largeSize * @emSize) / @emSize));
@bigRaw: unit((round(@bigSize * @emSize) / @emSize));
@hugeRaw: unit((round(@hugeSize * @emSize) / @emSize));
@massiveRaw: unit((round(@massiveSize * @emSize) / @emSize));

@mini: unit(@miniRaw, rem);
@tiny: unit(@tinyRaw, rem);
@small: unit(@smallRaw, rem);
@medium: unit(@mediumRaw, rem);
@large: unit(@largeRaw, rem);
@big: unit(@bigRaw, rem);
@huge: unit(@hugeRaw, rem);
@massive: unit(@massiveRaw, rem);

/* em */
@relativeMini: unit(@miniRaw, em);
@relativeTiny: unit(@tinyRaw, em);
@relativeSmall: unit(@smallRaw, em);
@relativeMedium: unit(@mediumRaw, em);
@relativeLarge: unit(@largeRaw, em);
@relativeBig: unit(@bigRaw, em);
@relativeHuge: unit(@hugeRaw, em);
@relativeMassive: unit(@massiveRaw, em);

/* rem */
@absoluteMini: unit(@miniRaw, rem);
@absoluteTiny: unit(@tinyRaw, rem);
@absoluteSmall: unit(@smallRaw, rem);
@absoluteMedium: unit(@mediumRaw, rem);
@absoluteLarge: unit(@largeRaw, rem);
@absoluteBig: unit(@bigRaw, rem);
@absoluteHuge: unit(@hugeRaw, rem);
@absoluteMassive: unit(@massiveRaw, rem);

/* -------------------
       Icons
-------------------- */

/* Maximum Glyph Width of Icon */
@iconWidth: 1.18em;

/* -------------------
     Neutral Text
-------------------- */

@darkTextColor: rgba(0, 0, 0, 0.85);
@mutedTextColor: rgba(0, 0, 0, 0.6);
@lightTextColor: rgba(0, 0, 0, 0.4);

@unselectedTextColor: rgba(0, 0, 0, 0.4);
@hoveredTextColor: rgba(0, 0, 0, 0.8);
@pressedTextColor: rgba(0, 0, 0, 0.9);
@selectedTextColor: rgba(0, 0, 0, 0.95);

@invertedTextColor: rgba(255, 255, 255, 0.9);
@invertedMutedTextColor: rgba(255, 255, 255, 0.8);
@invertedLightTextColor: rgba(255, 255, 255, 0.7);
@invertedUnselectedTextColor: rgba(255, 255, 255, 0.5);
@invertedHoveredTextColor: rgba(255, 255, 255, 1);
@invertedPressedTextColor: rgba(255, 255, 255, 1);
@invertedSelectedTextColor: rgba(255, 255, 255, 1);

/* -------------------
     Brand Colors
-------------------- */

@facebookColor: #3b5998;
@twitterColor: #1da1f2;
@googlePlusColor: #dd4b39;
@linkedInColor: #0077b5;
@youtubeColor: #f00;
@pinterestColor: #bd081c;
@vkColor: #45668e;
@instagramColor: #49769c;
@telegramColor: #08c;
@whatsAppColor: #25d366;

/* -------------------
      Borders
-------------------- */

@circularRadius: 500rem;

@borderColor: rgba(34, 36, 38, 0.15);
@strongBorderColor: rgba(34, 36, 38, 0.22);
@internalBorderColor: rgba(34, 36, 38, 0.1);
@selectedBorderColor: rgba(34, 36, 38, 0.35);
@strongSelectedBorderColor: rgba(34, 36, 38, 0.5);
@disabledBorderColor: rgba(34, 36, 38, 0.5);

@solidInternalBorderColor: #fafafa;
@solidBorderColor: #d4d4d5;
@solidSelectedBorderColor: #bcbdbd;

@whiteBorderColor: rgba(255, 255, 255, 0.1);
@selectedWhiteBorderColor: rgba(255, 255, 255, 0.8);

@solidWhiteBorderColor: #555;
@selectedSolidWhiteBorderColor: #999;

/* -------------------
    Derived Values
-------------------- */

/* Loaders Position Offset */
@loaderOffset: -(@loaderSize / 2);
@loaderMargin: @loaderOffset 0 0 @loaderOffset;

/* Rendered Scrollbar Width */
@scrollbarWidth: 17px;

/* Maximum Single Character Glyph Width, aka Capital "W" */
@glyphWidth: 1.1em;

/* Used to match floats with text */
@lineHeightOffset: ((@lineHeight - 1em) / 2);
@headerLineHeightOffset: ((@headerLineHeight - 1em) / 2);

/* Header Spacing */
@headerTopMargin: e(%("calc(2rem - %d)", @headerLineHeightOffset));
@headerBottomMargin: 1rem;
@headerMargin: @headerTopMargin 0 @headerBottomMargin;

/* Minimum Mobile Width */
@pageMinWidth: 320px;

/* Positive / Negative Dupes */
@successBackgroundColor: @positiveBackgroundColor;
@successColor: @positiveColor;
@successBorderColor: @positiveBorderColor;
@successHeaderColor: @positiveHeaderColor;
@successTextColor: @positiveTextColor;

@errorBackgroundColor: @negativeBackgroundColor;
@errorColor: @negativeColor;
@errorBorderColor: @negativeBorderColor;
@errorHeaderColor: @negativeHeaderColor;
@errorTextColor: @negativeTextColor;

/* Responsive */
@largestMobileScreen: (@tabletBreakpoint - 0.02px);
@largestTabletScreen: (@computerBreakpoint - 0.02px);
@largestSmallMonitor: (@largeMonitorBreakpoint - 0.02px);
@largestLargeMonitor: (@widescreenMonitorBreakpoint - 0.02px);

/* -------------------
  Exact Pixel Values
-------------------- */

/*
  These are used to specify exact pixel values in em
  for things like borders that remain constantly
  sized as emSize adjusts

  Since there are many more sizes than names for sizes,
  these are named by their original pixel values.

*/

@1px: unit((1 / @emSize), rem);
@2px: unit((2 / @emSize), rem);
@3px: unit((3 / @emSize), rem);
@4px: unit((4 / @emSize), rem);
@5px: unit((5 / @emSize), rem);
@6px: unit((6 / @emSize), rem);
@7px: unit((7 / @emSize), rem);
@8px: unit((8 / @emSize), rem);
@9px: unit((9 / @emSize), rem);
@10px: unit((10 / @emSize), rem);
@11px: unit((11 / @emSize), rem);
@12px: unit((12 / @emSize), rem);
@13px: unit((13 / @emSize), rem);
@14px: unit((14 / @emSize), rem);
@15px: unit((15 / @emSize), rem);
@16px: unit((16 / @emSize), rem);
@17px: unit((17 / @emSize), rem);
@18px: unit((18 / @emSize), rem);
@19px: unit((19 / @emSize), rem);
@20px: unit((20 / @emSize), rem);
@21px: unit((21 / @emSize), rem);
@22px: unit((22 / @emSize), rem);
@23px: unit((23 / @emSize), rem);
@24px: unit((24 / @emSize), rem);
@25px: unit((25 / @emSize), rem);
@26px: unit((26 / @emSize), rem);
@27px: unit((27 / @emSize), rem);
@28px: unit((28 / @emSize), rem);
@29px: unit((29 / @emSize), rem);
@30px: unit((30 / @emSize), rem);
@31px: unit((31 / @emSize), rem);
@32px: unit((32 / @emSize), rem);
@33px: unit((33 / @emSize), rem);
@34px: unit((34 / @emSize), rem);
@35px: unit((35 / @emSize), rem);
@36px: unit((36 / @emSize), rem);
@37px: unit((37 / @emSize), rem);
@38px: unit((38 / @emSize), rem);
@39px: unit((39 / @emSize), rem);
@40px: unit((40 / @emSize), rem);
@41px: unit((41 / @emSize), rem);
@42px: unit((42 / @emSize), rem);
@43px: unit((43 / @emSize), rem);
@44px: unit((44 / @emSize), rem);
@45px: unit((45 / @emSize), rem);
@46px: unit((46 / @emSize), rem);
@47px: unit((47 / @emSize), rem);
@48px: unit((48 / @emSize), rem);
@49px: unit((49 / @emSize), rem);
@50px: unit((50 / @emSize), rem);
@51px: unit((51 / @emSize), rem);
@52px: unit((52 / @emSize), rem);
@53px: unit((53 / @emSize), rem);
@54px: unit((54 / @emSize), rem);
@55px: unit((55 / @emSize), rem);
@56px: unit((56 / @emSize), rem);
@57px: unit((57 / @emSize), rem);
@58px: unit((58 / @emSize), rem);
@59px: unit((59 / @emSize), rem);
@60px: unit((60 / @emSize), rem);
@61px: unit((61 / @emSize), rem);
@62px: unit((62 / @emSize), rem);
@63px: unit((63 / @emSize), rem);
@64px: unit((64 / @emSize), rem);

@relative1px: unit((1 / @emSize), em);
@relative2px: unit((2 / @emSize), em);
@relative3px: unit((3 / @emSize), em);
@relative4px: unit((4 / @emSize), em);
@relative5px: unit((5 / @emSize), em);
@relative6px: unit((6 / @emSize), em);
@relative7px: unit((7 / @emSize), em);
@relative8px: unit((8 / @emSize), em);
@relative9px: unit((9 / @emSize), em);
@relative10px: unit((10 / @emSize), em);
@relative11px: unit((11 / @emSize), em);
@relative12px: unit((12 / @emSize), em);
@relative13px: unit((13 / @emSize), em);
@relative14px: unit((14 / @emSize), em);
@relative15px: unit((15 / @emSize), em);
@relative16px: unit((16 / @emSize), em);
@relative17px: unit((17 / @emSize), em);
@relative18px: unit((18 / @emSize), em);
@relative19px: unit((19 / @emSize), em);
@relative20px: unit((20 / @emSize), em);
@relative21px: unit((21 / @emSize), em);
@relative22px: unit((22 / @emSize), em);
@relative23px: unit((23 / @emSize), em);
@relative24px: unit((24 / @emSize), em);
@relative25px: unit((25 / @emSize), em);
@relative26px: unit((26 / @emSize), em);
@relative27px: unit((27 / @emSize), em);
@relative28px: unit((28 / @emSize), em);
@relative29px: unit((29 / @emSize), em);
@relative30px: unit((30 / @emSize), em);
@relative31px: unit((31 / @emSize), em);
@relative32px: unit((32 / @emSize), em);
@relative33px: unit((33 / @emSize), em);
@relative34px: unit((34 / @emSize), em);
@relative35px: unit((35 / @emSize), em);
@relative36px: unit((36 / @emSize), em);
@relative37px: unit((37 / @emSize), em);
@relative38px: unit((38 / @emSize), em);
@relative39px: unit((39 / @emSize), em);
@relative40px: unit((40 / @emSize), em);
@relative41px: unit((41 / @emSize), em);
@relative42px: unit((42 / @emSize), em);
@relative43px: unit((43 / @emSize), em);
@relative44px: unit((44 / @emSize), em);
@relative45px: unit((45 / @emSize), em);
@relative46px: unit((46 / @emSize), em);
@relative47px: unit((47 / @emSize), em);
@relative48px: unit((48 / @emSize), em);
@relative49px: unit((49 / @emSize), em);
@relative50px: unit((50 / @emSize), em);
@relative51px: unit((51 / @emSize), em);
@relative52px: unit((52 / @emSize), em);
@relative53px: unit((53 / @emSize), em);
@relative54px: unit((54 / @emSize), em);
@relative55px: unit((55 / @emSize), em);
@relative56px: unit((56 / @emSize), em);
@relative57px: unit((57 / @emSize), em);
@relative58px: unit((58 / @emSize), em);
@relative59px: unit((59 / @emSize), em);
@relative60px: unit((60 / @emSize), em);
@relative61px: unit((61 / @emSize), em);
@relative62px: unit((62 / @emSize), em);
@relative63px: unit((63 / @emSize), em);
@relative64px: unit((64 / @emSize), em);

/* Columns */
@oneWide: (1 / @columnCount * 100%);
@twoWide: (2 / @columnCount * 100%);
@threeWide: (3 / @columnCount * 100%);
@fourWide: (4 / @columnCount * 100%);
@fiveWide: (5 / @columnCount * 100%);
@sixWide: (6 / @columnCount * 100%);
@sevenWide: (7 / @columnCount * 100%);
@eightWide: (8 / @columnCount * 100%);
@nineWide: (9 / @columnCount * 100%);
@tenWide: (10 / @columnCount * 100%);
@elevenWide: (11 / @columnCount * 100%);
@twelveWide: (12 / @columnCount * 100%);
@thirteenWide: (13 / @columnCount * 100%);
@fourteenWide: (14 / @columnCount * 100%);
@fifteenWide: (15 / @columnCount * 100%);
@sixteenWide: (16 / @columnCount * 100%);

@oneColumn: (1 / 1 * 100%);
@twoColumn: (1 / 2 * 100%);
@threeColumn: (1 / 3 * 100%);
@fourColumn: (1 / 4 * 100%);
@fiveColumn: (1 / 5 * 100%);
@sixColumn: (1 / 6 * 100%);
@sevenColumn: (1 / 7 * 100%);
@eightColumn: (1 / 8 * 100%);
@nineColumn: (1 / 9 * 100%);
@tenColumn: (1 / 10 * 100%);
@elevenColumn: (1 / 11 * 100%);
@twelveColumn: (1 / 12 * 100%);
@thirteenColumn: (1 / 13 * 100%);
@fourteenColumn: (1 / 14 * 100%);
@fifteenColumn: (1 / 15 * 100%);
@sixteenColumn: (1 / 16 * 100%);

/*******************************
             States
*******************************/

/* -------------------
      Disabled
-------------------- */

@disabledOpacity: 0.45;
@disabledPointerEvents: none;
@disabledTextColor: rgba(40, 40, 40, 0.3);
@invertedDisabledTextColor: rgba(225, 225, 225, 0.3);

/* -------------------
        Hover
-------------------- */

/* ---  Shadows  --- */
@floatingShadowHover:
    0 2px 4px 0 rgba(34, 36, 38, 0.15),
    0 2px 10px 0 rgba(34, 36, 38, 0.25);

/* ---  Colors  --- */
@primaryColorHover: if(iscolor(@primaryColor), saturate(darken(@primaryColor, 5), 10, relative), @primaryColor);
@secondaryColorHover: if(iscolor(@secondaryColor), saturate(lighten(@secondaryColor, 5), 10, relative), @secondaryColor);
@lightPrimaryColorHover: if(iscolor(@lightPrimaryColor), saturate(darken(@lightPrimaryColor, 10), 10, relative), @lightPrimaryColor);
@lightSecondaryColorHover: if(iscolor(@lightSecondaryColor), saturate(lighten(@lightSecondaryColor, 10), 10, relative), @lightSecondaryColor);

@redHover: if(iscolor(@red), saturate(darken(@red, 5), 10, relative), @red);
@orangeHover: if(iscolor(@orange), saturate(darken(@orange, 5), 10, relative), @orange);
@yellowHover: if(iscolor(@yellow), saturate(darken(@yellow, 5), 10, relative), @yellow);
@oliveHover: if(iscolor(@olive), saturate(darken(@olive, 5), 10, relative), @olive);
@greenHover: if(iscolor(@green), saturate(darken(@green, 5), 10, relative), @green);
@tealHover: if(iscolor(@teal), saturate(darken(@teal, 5), 10, relative), @teal);
@blueHover: if(iscolor(@blue), saturate(darken(@blue, 5), 10, relative), @blue);
@violetHover: if(iscolor(@violet), saturate(darken(@violet, 5), 10, relative), @violet);
@purpleHover: if(iscolor(@purple), saturate(darken(@purple, 5), 10, relative), @purple);
@pinkHover: if(iscolor(@pink), saturate(darken(@pink, 5), 10, relative), @pink);
@brownHover: if(iscolor(@brown), saturate(darken(@brown, 5), 10, relative), @brown);

@lightRedHover: if(iscolor(@lightRed), saturate(darken(@lightRed, 10), 10, relative), @lightRed);
@lightOrangeHover: if(iscolor(@lightOrange), saturate(darken(@lightOrange, 10), 10, relative), @lightOrange);
@lightYellowHover: if(iscolor(@lightYellow), saturate(darken(@lightYellow, 10), 10, relative), @lightYellow);
@lightOliveHover: if(iscolor(@lightOlive), saturate(darken(@lightOlive, 10), 10, relative), @lightOlive);
@lightGreenHover: if(iscolor(@lightGreen), saturate(darken(@lightGreen, 10), 10, relative), @lightGreen);
@lightTealHover: if(iscolor(@lightTeal), saturate(darken(@lightTeal, 10), 10, relative), @lightTeal);
@lightBlueHover: if(iscolor(@lightBlue), saturate(darken(@lightBlue, 10), 10, relative), @lightBlue);
@lightVioletHover: if(iscolor(@lightViolet), saturate(darken(@lightViolet, 10), 10, relative), @lightViolet);
@lightPurpleHover: if(iscolor(@lightPurple), saturate(darken(@lightPurple, 10), 10, relative), @lightPurple);
@lightPinkHover: if(iscolor(@lightPink), saturate(darken(@lightPink, 10), 10, relative), @lightPink);
@lightBrownHover: if(iscolor(@lightBrown), saturate(darken(@lightBrown, 10), 10, relative), @lightBrown);
@lightGreyHover: if(iscolor(@lightGrey), saturate(darken(@lightGrey, 10), 10, relative), @lightGrey);
@lightBlackHover: if(iscolor(@fullBlack), saturate(darken(@fullBlack, 10), 10, relative), @fullBlack);

/* ---  Emotive  --- */
@positiveColorHover: if(iscolor(@positiveColor), saturate(darken(@positiveColor, 5), 10, relative), @positiveColor);
@negativeColorHover: if(iscolor(@negativeColor), saturate(darken(@negativeColor, 5), 10, relative), @negativeColor);

/* ---  Brand   --- */
@facebookHoverColor: if(iscolor(@facebookColor), saturate(darken(@facebookColor, 5), 10, relative), @facebookColor);
@twitterHoverColor: if(iscolor(@twitterColor), saturate(darken(@twitterColor, 5), 10, relative), @twitterColor);
@googlePlusHoverColor: if(iscolor(@googlePlusColor), saturate(darken(@googlePlusColor, 5), 10, relative), @googlePlusColor);
@linkedInHoverColor: if(iscolor(@linkedInColor), saturate(darken(@linkedInColor, 5), 10, relative), @linkedInColor);
@youtubeHoverColor: if(iscolor(@youtubeColor), saturate(darken(@youtubeColor, 5), 10, relative), @youtubeColor);
@instagramHoverColor: if(iscolor(@instagramColor), saturate(darken(@instagramColor, 5), 10, relative), @instagramColor);
@pinterestHoverColor: if(iscolor(@pinterestColor), saturate(darken(@pinterestColor, 5), 10, relative), @pinterestColor);
@vkHoverColor: if(iscolor(@vkColor), saturate(darken(@vkColor, 5), 10, relative), @vkColor);
@telegramHoverColor: if(iscolor(@telegramColor), saturate(darken(@telegramColor, 5), 10, relative), @telegramColor);
@whatsAppHoverColor: if(iscolor(@whatsAppColor), saturate(darken(@whatsAppColor, 5), 10, relative), @whatsAppColor);

/* ---  Dark Tones  --- */
@fullBlackHover: if(iscolor(@fullBlack), lighten(@fullBlack, 5), @fullBlack);
@blackHover: if(iscolor(@black), lighten(@black, 5), @black);
@greyHover: if(iscolor(@grey), lighten(@grey, 5), @grey);

/* ---  Light Tones  --- */
@whiteHover: if(iscolor(@white), darken(@white, 5), @white);
@offWhiteHover: if(iscolor(@offWhite), darken(@offWhite, 5), @offWhite);
@darkWhiteHover: if(iscolor(@darkWhite), darken(@darkWhite, 5), @darkWhite);

/* -------------------
        Focus
-------------------- */

/* ---  Colors  --- */
@primaryColorFocus: if(iscolor(@primaryColor), saturate(darken(@primaryColor, 8), 20, relative), @primaryColor);
@secondaryColorFocus: if(iscolor(@secondaryColor), saturate(lighten(@secondaryColor, 8), 20, relative), @secondaryColor);
@lightPrimaryColorFocus: if(iscolor(@lightPrimaryColor), saturate(darken(@lightPrimaryColor, 8), 20, relative), @lightPrimaryColor);
@lightSecondaryColorFocus: if(iscolor(@lightSecondaryColor), saturate(lighten(@lightSecondaryColor, 8), 20, relative), @lightSecondaryColor);

@redFocus: if(iscolor(@red), saturate(darken(@red, 8), 20, relative), @red);
@orangeFocus: if(iscolor(@orange), saturate(darken(@orange, 8), 20, relative), @orange);
@yellowFocus: if(iscolor(@yellow), saturate(darken(@yellow, 8), 20, relative), @yellow);
@oliveFocus: if(iscolor(@olive), saturate(darken(@olive, 8), 20, relative), @olive);
@greenFocus: if(iscolor(@green), saturate(darken(@green, 8), 20, relative), @green);
@tealFocus: if(iscolor(@teal), saturate(darken(@teal, 8), 20, relative), @teal);
@blueFocus: if(iscolor(@blue), saturate(darken(@blue, 8), 20, relative), @blue);
@violetFocus: if(iscolor(@violet), saturate(darken(@violet, 8), 20, relative), @violet);
@purpleFocus: if(iscolor(@purple), saturate(darken(@purple, 8), 20, relative), @purple);
@pinkFocus: if(iscolor(@pink), saturate(darken(@pink, 8), 20, relative), @pink);
@brownFocus: if(iscolor(@brown), saturate(darken(@brown, 8), 20, relative), @brown);

@lightRedFocus: if(iscolor(@lightRed), saturate(darken(@lightRed, 8), 20, relative), @lightRed);
@lightOrangeFocus: if(iscolor(@lightOrange), saturate(darken(@lightOrange, 8), 20, relative), @lightOrange);
@lightYellowFocus: if(iscolor(@lightYellow), saturate(darken(@lightYellow, 8), 20, relative), @lightYellow);
@lightOliveFocus: if(iscolor(@lightOlive), saturate(darken(@lightOlive, 8), 20, relative), @lightOlive);
@lightGreenFocus: if(iscolor(@lightGreen), saturate(darken(@lightGreen, 8), 20, relative), @lightGreen);
@lightTealFocus: if(iscolor(@lightTeal), saturate(darken(@lightTeal, 8), 20, relative), @lightTeal);
@lightBlueFocus: if(iscolor(@lightBlue), saturate(darken(@lightBlue, 8), 20, relative), @lightBlue);
@lightVioletFocus: if(iscolor(@lightViolet), saturate(darken(@lightViolet, 8), 20, relative), @lightViolet);
@lightPurpleFocus: if(iscolor(@lightPurple), saturate(darken(@lightPurple, 8), 20, relative), @lightPurple);
@lightPinkFocus: if(iscolor(@lightPink), saturate(darken(@lightPink, 8), 20, relative), @lightPink);
@lightBrownFocus: if(iscolor(@lightBrown), saturate(darken(@lightBrown, 8), 20, relative), @lightBrown);
@lightGreyFocus: if(iscolor(@lightGrey), saturate(darken(@lightGrey, 8), 20, relative), @lightGrey);
@lightBlackFocus: if(iscolor(@fullBlack), saturate(darken(@fullBlack, 8), 20, relative), @fullBlack);

/* ---  Emotive  --- */
@positiveColorFocus: if(iscolor(@positiveColor), saturate(darken(@positiveColor, 8), 20, relative), @positiveColor);
@negativeColorFocus: if(iscolor(@negativeColor), saturate(darken(@negativeColor, 8), 20, relative), @negativeColor);

/* ---  Brand   --- */
@facebookFocusColor: if(iscolor(@facebookColor), saturate(darken(@facebookColor, 8), 20, relative), @facebookColor);
@twitterFocusColor: if(iscolor(@twitterColor), saturate(darken(@twitterColor, 8), 20, relative), @twitterColor);
@googlePlusFocusColor: if(iscolor(@googlePlusColor), saturate(darken(@googlePlusColor, 8), 20, relative), @googlePlusColor);
@linkedInFocusColor: if(iscolor(@linkedInColor), saturate(darken(@linkedInColor, 8), 20, relative), @linkedInColor);
@youtubeFocusColor: if(iscolor(@youtubeColor), saturate(darken(@youtubeColor, 8), 20, relative), @youtubeColor);
@instagramFocusColor: if(iscolor(@instagramColor), saturate(darken(@instagramColor, 8), 20, relative), @instagramColor);
@pinterestFocusColor: if(iscolor(@pinterestColor), saturate(darken(@pinterestColor, 8), 20, relative), @pinterestColor);
@vkFocusColor: if(iscolor(@vkColor), saturate(darken(@vkColor, 8), 20, relative), @vkColor);
@telegramFocusColor: if(iscolor(@telegramColor), saturate(darken(@telegramColor, 8), 20, relative), @telegramColor);
@whatsAppFocusColor: if(iscolor(@whatsAppColor), saturate(darken(@whatsAppColor, 8), 20, relative), @whatsAppColor);

/* ---  Dark Tones  --- */
@fullBlackFocus: if(iscolor(@fullBlack), lighten(@fullBlack, 8), @fullBlack);
@blackFocus: if(iscolor(@black), lighten(@black, 8), @black);
@greyFocus: if(iscolor(@grey), lighten(@grey, 8), @grey);

/* ---  Light Tones  --- */
@whiteFocus: if(iscolor(@white), darken(@white, 8), @white);
@offWhiteFocus: if(iscolor(@offWhite), darken(@offWhite, 8), @offWhite);
@darkWhiteFocus: if(iscolor(@darkWhite), darken(@darkWhite, 8), @darkWhite);

/* -------------------
    Down (:active)
-------------------- */

/* ---  Colors  --- */
@primaryColorDown: if(iscolor(@primaryColor), darken(@primaryColor, 10), @primaryColor);
@secondaryColorDown: if(iscolor(@secondaryColor), lighten(@secondaryColor, 10), @secondaryColor);
@lightPrimaryColorDown: if(iscolor(@lightPrimaryColor), darken(@lightPrimaryColor, 10), @lightPrimaryColor);
@lightSecondaryColorDown: if(iscolor(@lightSecondaryColor), lighten(@lightSecondaryColor, 10), @lightSecondaryColor);

@redDown: if(iscolor(@red), darken(@red, 10), @red);
@orangeDown: if(iscolor(@orange), darken(@orange, 10), @orange);
@yellowDown: if(iscolor(@yellow), darken(@yellow, 10), @yellow);
@oliveDown: if(iscolor(@olive), darken(@olive, 10), @olive);
@greenDown: if(iscolor(@green), darken(@green, 10), @green);
@tealDown: if(iscolor(@teal), darken(@teal, 10), @teal);
@blueDown: if(iscolor(@blue), darken(@blue, 10), @blue);
@violetDown: if(iscolor(@violet), darken(@violet, 10), @violet);
@purpleDown: if(iscolor(@purple), darken(@purple, 10), @purple);
@pinkDown: if(iscolor(@pink), darken(@pink, 10), @pink);
@brownDown: if(iscolor(@brown), darken(@brown, 10), @brown);

@lightRedDown: if(iscolor(@lightRed), darken(@lightRed, 10), @lightRed);
@lightOrangeDown: if(iscolor(@lightOrange), darken(@lightOrange, 10), @lightOrange);
@lightYellowDown: if(iscolor(@lightYellow), darken(@lightYellow, 10), @lightYellow);
@lightOliveDown: if(iscolor(@lightOlive), darken(@lightOlive, 10), @lightOlive);
@lightGreenDown: if(iscolor(@lightGreen), darken(@lightGreen, 10), @lightGreen);
@lightTealDown: if(iscolor(@lightTeal), darken(@lightTeal, 10), @lightTeal);
@lightBlueDown: if(iscolor(@lightBlue), darken(@lightBlue, 10), @lightBlue);
@lightVioletDown: if(iscolor(@lightViolet), darken(@lightViolet, 10), @lightViolet);
@lightPurpleDown: if(iscolor(@lightPurple), darken(@lightPurple, 10), @lightPurple);
@lightPinkDown: if(iscolor(@lightPink), darken(@lightPink, 10), @lightPink);
@lightBrownDown: if(iscolor(@lightBrown), darken(@lightBrown, 10), @lightBrown);
@lightGreyDown: if(iscolor(@lightGrey), darken(@lightGrey, 10), @lightGrey);
@lightBlackDown: if(iscolor(@fullBlack), darken(@fullBlack, 10), @fullBlack);

/* ---  Emotive  --- */
@positiveColorDown: if(iscolor(@positiveColor), darken(@positiveColor, 10), @positiveColor);
@negativeColorDown: if(iscolor(@negativeColor), darken(@negativeColor, 10), @negativeColor);

/* ---  Brand   --- */
@facebookDownColor: if(iscolor(@facebookColor), darken(@facebookColor, 10), @facebookColor);
@twitterDownColor: if(iscolor(@twitterColor), darken(@twitterColor, 10), @twitterColor);
@googlePlusDownColor: if(iscolor(@googlePlusColor), darken(@googlePlusColor, 10), @googlePlusColor);
@linkedInDownColor: if(iscolor(@linkedInColor), darken(@linkedInColor, 10), @linkedInColor);
@youtubeDownColor: if(iscolor(@youtubeColor), darken(@youtubeColor, 10), @youtubeColor);
@instagramDownColor: if(iscolor(@instagramColor), darken(@instagramColor, 10), @instagramColor);
@pinterestDownColor: if(iscolor(@pinterestColor), darken(@pinterestColor, 10), @pinterestColor);
@vkDownColor: if(iscolor(@vkColor), darken(@vkColor, 10), @vkColor);
@telegramDownColor: if(iscolor(@telegramColor), darken(@telegramColor, 10), @telegramColor);
@whatsAppDownColor: if(iscolor(@whatsAppColor), darken(@whatsAppColor, 10), @whatsAppColor);

/* ---  Dark Tones  --- */
@fullBlackDown: if(iscolor(@fullBlack), lighten(@fullBlack, 10), @fullBlack);
@blackDown: if(iscolor(@black), lighten(@black, 10), @black);
@greyDown: if(iscolor(@grey), lighten(@grey, 10), @grey);

/* ---  Light Tones  --- */
@whiteDown: if(iscolor(@white), darken(@white, 10), @white);
@offWhiteDown: if(iscolor(@offWhite), darken(@offWhite, 10), @offWhite);
@darkWhiteDown: if(iscolor(@darkWhite), darken(@darkWhite, 10), @darkWhite);

/* -------------------
        Active
-------------------- */

/* ---  Colors  --- */
@primaryColorActive: if(iscolor(@primaryColor), saturate(darken(@primaryColor, 5), 15, relative), @primaryColor);
@secondaryColorActive: if(iscolor(@secondaryColor), saturate(lighten(@secondaryColor, 5), 15, relative), @secondaryColor);
@lightPrimaryColorActive: if(iscolor(@lightPrimaryColor), saturate(darken(@lightPrimaryColor, 5), 15, relative), @lightPrimaryColor);
@lightSecondaryColorActive: if(iscolor(@lightSecondaryColor), saturate(lighten(@lightSecondaryColor, 5), 15, relative), @lightSecondaryColor);

@redActive: if(iscolor(@red), saturate(darken(@red, 5), 15, relative), @red);
@orangeActive: if(iscolor(@orange), saturate(darken(@orange, 5), 15, relative), @orange);
@yellowActive: if(iscolor(@yellow), saturate(darken(@yellow, 5), 15, relative), @yellow);
@oliveActive: if(iscolor(@olive), saturate(darken(@olive, 5), 15, relative), @olive);
@greenActive: if(iscolor(@green), saturate(darken(@green, 5), 15, relative), @green);
@tealActive: if(iscolor(@teal), saturate(darken(@teal, 5), 15, relative), @teal);
@blueActive: if(iscolor(@blue), saturate(darken(@blue, 5), 15, relative), @blue);
@violetActive: if(iscolor(@violet), saturate(darken(@violet, 5), 15, relative), @violet);
@purpleActive: if(iscolor(@purple), saturate(darken(@purple, 5), 15, relative), @purple);
@pinkActive: if(iscolor(@pink), saturate(darken(@pink, 5), 15, relative), @pink);
@brownActive: if(iscolor(@brown), saturate(darken(@brown, 5), 15, relative), @brown);

@lightRedActive: if(iscolor(@lightRed), saturate(darken(@lightRed, 5), 15, relative), @lightRed);
@lightOrangeActive: if(iscolor(@lightOrange), saturate(darken(@lightOrange, 5), 15, relative), @lightOrange);
@lightYellowActive: if(iscolor(@lightYellow), saturate(darken(@lightYellow, 5), 15, relative), @lightYellow);
@lightOliveActive: if(iscolor(@lightOlive), saturate(darken(@lightOlive, 5), 15, relative), @lightOlive);
@lightGreenActive: if(iscolor(@lightGreen), saturate(darken(@lightGreen, 5), 15, relative), @lightGreen);
@lightTealActive: if(iscolor(@lightTeal), saturate(darken(@lightTeal, 5), 15, relative), @lightTeal);
@lightBlueActive: if(iscolor(@lightBlue), saturate(darken(@lightBlue, 5), 15, relative), @lightBlue);
@lightVioletActive: if(iscolor(@lightViolet), saturate(darken(@lightViolet, 5), 15, relative), @lightViolet);
@lightPurpleActive: if(iscolor(@lightPurple), saturate(darken(@lightPurple, 5), 15, relative), @lightPurple);
@lightPinkActive: if(iscolor(@lightPink), saturate(darken(@lightPink, 5), 15, relative), @lightPink);
@lightBrownActive: if(iscolor(@lightBrown), saturate(darken(@lightBrown, 5), 15, relative), @lightBrown);
@lightGreyActive: if(iscolor(@lightGrey), saturate(darken(@lightGrey, 5), 15, relative), @lightGrey);
@lightBlackActive: if(iscolor(@fullBlack), saturate(darken(@fullBlack, 5), 15, relative), @fullBlack);

/* ---  Emotive  --- */
@positiveColorActive: if(iscolor(@positiveColor), saturate(darken(@positiveColor, 5), 15, relative), @positiveColor);
@negativeColorActive: if(iscolor(@negativeColor), saturate(darken(@negativeColor, 5), 15, relative), @negativeColor);

/* ---  Brand   --- */
@facebookActiveColor: if(iscolor(@facebookColor), saturate(darken(@facebookColor, 5), 15, relative), @facebookColor);
@twitterActiveColor: if(iscolor(@twitterColor), saturate(darken(@twitterColor, 5), 15, relative), @twitterColor);
@googlePlusActiveColor: if(iscolor(@googlePlusColor), saturate(darken(@googlePlusColor, 5), 15, relative), @googlePlusColor);
@linkedInActiveColor: if(iscolor(@linkedInColor), saturate(darken(@linkedInColor, 5), 15, relative), @linkedInColor);
@youtubeActiveColor: if(iscolor(@youtubeColor), saturate(darken(@youtubeColor, 5), 15, relative), @youtubeColor);
@instagramActiveColor: if(iscolor(@instagramColor), saturate(darken(@instagramColor, 5), 15, relative), @instagramColor);
@pinterestActiveColor: if(iscolor(@pinterestColor), saturate(darken(@pinterestColor, 5), 15, relative), @pinterestColor);
@vkActiveColor: if(iscolor(@vkColor), saturate(darken(@vkColor, 5), 15, relative), @vkColor);
@telegramActiveColor: if(iscolor(@telegramColor), saturate(darken(@telegramColor, 5), 15, relative), @telegramColor);
@whatsAppActiveColor: if(iscolor(@whatsAppColor), saturate(darken(@whatsAppColor, 5), 15, relative), @whatsAppColor);

/* ---  Dark Tones  --- */
@fullBlackActive: if(iscolor(@fullBlack), darken(@fullBlack, 5), @fullBlack);
@blackActive: if(iscolor(@black), darken(@black, 5), @black);
@greyActive: if(iscolor(@grey), darken(@grey, 5), @grey);

/* ---  Light Tones  --- */
@whiteActive: if(iscolor(@white), darken(@white, 5), @white);
@offWhiteActive: if(iscolor(@offWhite), darken(@offWhite, 5), @offWhite);
@darkWhiteActive: if(iscolor(@darkWhite), darken(@darkWhite, 5), @darkWhite);

/* ---  Tertiary  --- */
@primaryTertiaryColor: if(iscolor(@primaryColor), saturate(@primaryColor, 20%), @primaryColor);
@primaryTertiaryColorHover: if(iscolor(@primaryColorHover), desaturate(@primaryColorHover, 20%), @primaryColorHover);
@primaryTertiaryColorFocus: if(iscolor(@primaryColorFocus), desaturate(@primaryColorFocus, 20%), @primaryColorFocus);
@primaryTertiaryColorActive: if(iscolor(@primaryColorActive), saturate(@primaryColorActive, 20%), @primaryColorActive);
@secondaryTertiaryColor: if(iscolor(@secondaryColor), saturate(@secondaryColor, 20%), @secondaryColor);
@secondaryTertiaryColorHover: if(iscolor(@secondaryColorHover), desaturate(@secondaryColorHover, 20%), @secondaryColorHover);
@secondaryTertiaryColorFocus: if(iscolor(@secondaryColorFocus), desaturate(@secondaryColorFocus, 20%), @secondaryColorFocus);
@secondaryTertiaryColorActive: if(iscolor(@secondaryColorActive), saturate(@secondaryColorActive, 20%), @secondaryColorActive);
@redTertiaryColor: if(iscolor(@red), saturate(@red, 20%), @red);
@redTertiaryColorHover: if(iscolor(@redHover), desaturate(@redHover, 20%), @redHover);
@redTertiaryColorFocus: if(iscolor(@redFocus), desaturate(@redFocus, 20%), @redFocus);
@redTertiaryColorActive: if(iscolor(@redActive), saturate(@redActive, 20%), @redActive);
@orangeTertiaryColor: if(iscolor(@orange), saturate(@orange, 20%), @orange);
@orangeTertiaryColorHover: if(iscolor(@orangeHover), desaturate(@orangeHover, 20%), @orangeHover);
@orangeTertiaryColorFocus: if(iscolor(@orangeFocus), desaturate(@orangeFocus, 20%), @orangeFocus);
@orangeTertiaryColorActive: if(iscolor(@orangeActive), saturate(@orangeActive, 20%), @orangeActive);
@yellowTertiaryColor: if(iscolor(@yellow), saturate(@yellow, 20%), @yellow);
@yellowTertiaryColorHover: if(iscolor(@yellowHover), desaturate(@yellowHover, 20%), @yellowHover);
@yellowTertiaryColorFocus: if(iscolor(@yellowFocus), desaturate(@yellowFocus, 20%), @yellowFocus);
@yellowTertiaryColorActive: if(iscolor(@yellowActive), saturate(@yellowActive, 20%), @yellowActive);
@oliveTertiaryColor: if(iscolor(@olive), saturate(@olive, 20%), @olive);
@oliveTertiaryColorHover: if(iscolor(@oliveHover), desaturate(@oliveHover, 20%), @oliveHover);
@oliveTertiaryColorFocus: if(iscolor(@oliveFocus), desaturate(@oliveFocus, 20%), @oliveFocus);
@oliveTertiaryColorActive: if(iscolor(@oliveActive), saturate(@oliveActive, 20%), @oliveActive);
@greenTertiaryColor: if(iscolor(@green), saturate(@green, 20%), @green);
@greenTertiaryColorHover: if(iscolor(@greenHover), desaturate(@greenHover, 20%), @greenHover);
@greenTertiaryColorFocus: if(iscolor(@greenFocus), desaturate(@greenFocus, 20%), @greenFocus);
@greenTertiaryColorActive: if(iscolor(@greenActive), saturate(@greenActive, 20%), @greenActive);
@tealTertiaryColor: if(iscolor(@teal), saturate(@teal, 20%), @teal);
@tealTertiaryColorHover: if(iscolor(@tealHover), desaturate(@tealHover, 20%), @tealHover);
@tealTertiaryColorFocus: if(iscolor(@tealFocus), desaturate(@tealFocus, 20%), @tealFocus);
@tealTertiaryColorActive: if(iscolor(@tealActive), saturate(@tealActive, 20%), @tealActive);
@blueTertiaryColor: if(iscolor(@blue), saturate(@blue, 20%), @blue);
@blueTertiaryColorHover: if(iscolor(@blueHover), desaturate(@blueHover, 20%), @blueHover);
@blueTertiaryColorFocus: if(iscolor(@blueFocus), desaturate(@blueFocus, 20%), @blueFocus);
@blueTertiaryColorActive: if(iscolor(@blueActive), saturate(@blueActive, 20%), @blueActive);
@violetTertiaryColor: if(iscolor(@violet), saturate(@violet, 20%), @violet);
@violetTertiaryColorHover: if(iscolor(@violetHover), desaturate(@violetHover, 20%), @violetHover);
@violetTertiaryColorFocus: if(iscolor(@violetFocus), desaturate(@violetFocus, 20%), @violetFocus);
@violetTertiaryColorActive: if(iscolor(@violetActive), saturate(@violetActive, 20%), @violetActive);
@purpleTertiaryColor: if(iscolor(@purple), saturate(@purple, 20%), @purple);
@purpleTertiaryColorHover: if(iscolor(@purpleHover), desaturate(@purpleHover, 20%), @purpleHover);
@purpleTertiaryColorFocus: if(iscolor(@purpleFocus), desaturate(@purpleFocus, 20%), @purpleFocus);
@purpleTertiaryColorActive: if(iscolor(@purpleActive), saturate(@purpleActive, 20%), @purpleActive);
@pinkTertiaryColor: if(iscolor(@pink), saturate(@pink, 20%), @pink);
@pinkTertiaryColorHover: if(iscolor(@pinkHover), desaturate(@pinkHover, 20%), @pinkHover);
@pinkTertiaryColorFocus: if(iscolor(@pinkFocus), desaturate(@pinkFocus, 20%), @pinkFocus);
@pinkTertiaryColorActive: if(iscolor(@pinkActive), saturate(@pinkActive, 20%), @pinkActive);
@brownTertiaryColor: if(iscolor(@brown), saturate(@brown, 20%), @brown);
@brownTertiaryColorHover: if(iscolor(@brownHover), desaturate(@brownHover, 20%), @brownHover);
@brownTertiaryColorFocus: if(iscolor(@brownFocus), desaturate(@brownFocus, 20%), @brownFocus);
@brownTertiaryColorActive: if(iscolor(@brownActive), saturate(@brownActive, 20%), @brownActive);
@greyTertiaryColor: if(iscolor(@grey), darken(@grey, 5%), @grey);
@greyTertiaryColorHover: if(iscolor(@greyHover), lighten(@greyHover, 5%), @greyHover);
@greyTertiaryColorFocus: if(iscolor(@greyFocus), lighten(@greyFocus, 8%), @greyFocus);
@greyTertiaryColorActive: if(iscolor(@greyActive), darken(@greyActive, 5%), @greyActive);
@blackTertiaryColor: if(iscolor(@black), lighten(@black, 20%), @black);
@blackTertiaryColorHover: if(iscolor(@blackHover), lighten(@blackHover, 40%), @blackHover);
@blackTertiaryColorFocus: if(iscolor(@blackFocus), lighten(@blackFocus, 40%), @blackFocus);
@blackTertiaryColorActive: if(iscolor(@blackActive), lighten(@blackActive, 20%), @blackActive);

/* ---  Bright  --- */
@primaryBright: if(iscolor(@lightPrimaryColor), screen(@lightPrimaryColor, @blendingBaseColor), @lightPrimaryColor);
@secondaryBright: if(iscolor(@lightSecondaryColor), screen(@lightSecondaryColor, @blendingBaseColor), @lightSecondaryColor);
@redBright: if(iscolor(@lightRed), screen(@lightRed, @blendingBaseColor), @lightRed);
@orangeBright: if(iscolor(@lightOrange), screen(@lightOrange, @blendingBaseColor), @lightOrange);
@yellowBright: if(iscolor(@lightYellow), screen(@lightYellow, @blendingBaseColor), @lightYellow);
@oliveBright: if(iscolor(@lightOlive), screen(@lightOlive, @blendingBaseColor), @lightOlive);
@greenBright: if(iscolor(@lightGreen), screen(@lightGreen, @blendingBaseColor), @lightGreen);
@tealBright: if(iscolor(@lightTeal), screen(@lightTeal, @blendingBaseColor), @lightTeal);
@blueBright: if(iscolor(@lightBlue), screen(@lightBlue, @blendingBaseColor), @lightBlue);
@violetBright: if(iscolor(@lightViolet), screen(@lightViolet, @blendingBaseColor), @lightViolet);
@purpleBright: if(iscolor(@lightPurple), screen(@lightPurple, @blendingBaseColor), @lightPurple);
@pinkBright: if(iscolor(@lightPink), screen(@lightPink, @blendingBaseColor), @lightPink);
@brownBright: if(iscolor(@lightBrown), screen(@lightBrown, @blendingBaseColor), @lightBrown);
@greyBright: @lightGrey;
@blackBright: @lightBlack;

@primaryBrightHover: if(iscolor(@lightPrimaryColorHover), screen(@lightPrimaryColorHover, @blendingBaseColor), @lightPrimaryColorHover);
@secondaryBrightHover: if(iscolor(@lightSecondaryColorHover), screen(@lightSecondaryColorHover, @blendingBaseColor), @lightSecondaryColorHover);
@redBrightHover: if(iscolor(@lightRedHover), screen(@lightRedHover, @blendingBaseColor), @lightRedHover);
@orangeBrightHover: if(iscolor(@lightOrangeHover), screen(@lightOrangeHover, @blendingBaseColor), @lightOrangeHover);
@yellowBrightHover: if(iscolor(@lightYellowHover), screen(@lightYellowHover, @blendingBaseColor), @lightYellowHover);
@oliveBrightHover: if(iscolor(@lightOliveHover), screen(@lightOliveHover, @blendingBaseColor), @lightOliveHover);
@greenBrightHover: if(iscolor(@lightGreenHover), screen(@lightGreenHover, @blendingBaseColor), @lightGreenHover);
@tealBrightHover: if(iscolor(@lightTealHover), screen(@lightTealHover, @blendingBaseColor), @lightTealHover);
@blueBrightHover: if(iscolor(@lightBlueHover), screen(@lightBlueHover, @blendingBaseColor), @lightBlueHover);
@violetBrightHover: if(iscolor(@lightVioletHover), screen(@lightVioletHover, @blendingBaseColor), @lightVioletHover);
@purpleBrightHover: if(iscolor(@lightPurpleHover), screen(@lightPurpleHover, @blendingBaseColor), @lightPurpleHover);
@pinkBrightHover: if(iscolor(@lightPinkHover), screen(@lightPinkHover, @blendingBaseColor), @lightPinkHover);
@brownBrightHover: if(iscolor(@lightBrownHover), screen(@lightBrownHover, @blendingBaseColor), @lightBrownHover);
@greyBrightHover: @lightGreyHover;
@blackBrightHover: @lightBlackHover;

/*******************************
 States shared in Form-related components
 *******************************/

/* Form state */
@formErrorColor: @negativeTextColor;
@formErrorBorder: @negativeBorderColor;
@formErrorBackground: @negativeBackgroundColor;
@formErrorLabelBackground: if(iscolor(@formErrorBorder), darken(@formErrorBorder, -8), @formErrorBorder);
@transparentFormErrorColor: @formErrorColor;
@transparentFormErrorBackground: @formErrorBackground;

@formInfoColor: @infoTextColor;
@formInfoBorder: @infoBorderColor;
@formInfoBackground: @infoBackgroundColor;
@formInfoLabelBackground: if(iscolor(@formInfoBorder), darken(@formInfoBorder, -8), @formInfoBorder);
@transparentFormInfoColor: @formInfoColor;
@transparentFormInfoBackground: @formInfoBackground;

@formSuccessColor: @positiveTextColor;
@formSuccessBorder: @positiveBorderColor;
@formSuccessBackground: @positiveBackgroundColor;
@formSuccessLabelBackground: if(iscolor(@formSuccessBorder), darken(@formSuccessBorder, -8), @formSuccessBorder);
@transparentFormSuccessColor: @formSuccessColor;
@transparentFormSuccessBackground: @formSuccessBackground;

@formWarningColor: @warningTextColor;
@formWarningBorder: @warningBorderColor;
@formWarningBackground: @warningBackgroundColor;
@formWarningLabelBackground: if(iscolor(@formWarningBorder), darken(@formWarningBorder, -8), @formWarningBorder);
@transparentFormWarningColor: @formWarningColor;
@transparentFormWarningBackground: @formWarningBackground;

/* Input state */
@inputErrorBorderRadius: "";
@inputErrorBoxShadow: none;

@inputInfoBorderRadius: "";
@inputInfoBoxShadow: none;

@inputSuccessBorderRadius: "";
@inputSuccessBoxShadow: none;

@inputWarningBorderRadius: "";
@inputWarningBoxShadow: none;

/* AutoFill */
@inputAutoFillBackground: #fffff0;
@inputAutoFillBorder: #e5dfa1;
@inputAutoFillFocusBackground: @inputAutoFillBackground;
@inputAutoFillFocusBorder: #d5c315;

@inputAutoFillErrorBackground: #fffaf0;
@inputAutoFillErrorBorder: #e0b4b4;

@inputAutoFillInfoBackground: #f0faff;
@inputAutoFillInfoBorder: #b3e0e0;

@inputAutoFillSuccessBackground: #f0fff0;
@inputAutoFillSuccessBorder: #bee0b3;

@inputAutoFillWarningBackground: #ffffe0;
@inputAutoFillWarningBorder: #e0e0b3;

/* Dropdown state */
@dropdownErrorHoverBackground: #fbe7e7;
@dropdownErrorSelectedBackground: @dropdownErrorHoverBackground;
@dropdownErrorActiveBackground: #fdcfcf;
@dropdownErrorLabelBackground: #eacbcb;
@dropdownErrorLabelColor: @errorTextColor;

@dropdownInfoHoverBackground: #e9f2fb;
@dropdownInfoSelectedBackground: @dropdownInfoHoverBackground;
@dropdownInfoActiveBackground: #cef1fd;
@dropdownInfoLabelBackground: #cce3ea;
@dropdownInfoLabelColor: @infoTextColor;

@dropdownSuccessHoverBackground: #e9fbe9;
@dropdownSuccessSelectedBackground: @dropdownSuccessHoverBackground;
@dropdownSuccessActiveBackground: #dafdce;
@dropdownSuccessLabelBackground: #cceacc;
@dropdownSuccessLabelColor: @successTextColor;

@dropdownWarningHoverBackground: #fbfbe9;
@dropdownWarningSelectedBackground: @dropdownWarningHoverBackground;
@dropdownWarningActiveBackground: #fdfdce;
@dropdownWarningLabelBackground: #eaeacc;
@dropdownWarningLabelColor: @warningTextColor;

/* Focused state */
@inputErrorFocusBackground: @negativeBackgroundColor;
@inputErrorFocusColor: @negativeTextColor;
@inputErrorFocusBorder: @negativeBorderColor;
@inputErrorFocusBoxShadow: none;

@inputInfoFocusBackground: @infoBackgroundColor;
@inputInfoFocusColor: @infoTextColor;
@inputInfoFocusBorder: @infoBorderColor;
@inputInfoFocusBoxShadow: none;

@inputSuccessFocusBackground: @positiveBackgroundColor;
@inputSuccessFocusColor: @positiveTextColor;
@inputSuccessFocusBorder: @positiveBorderColor;
@inputSuccessFocusBoxShadow: none;

@inputWarningFocusBackground: @warningBackgroundColor;
@inputWarningFocusColor: @warningTextColor;
@inputWarningFocusBorder: @warningBorderColor;
@inputWarningFocusBoxShadow: none;

/* Placeholder state */
@inputErrorPlaceholderColor: if(iscolor(@formErrorColor), lighten(@formErrorColor, 40), @formErrorColor);
@inputErrorPlaceholderFocusColor: if(iscolor(@formErrorColor), lighten(@formErrorColor, 30), @formErrorColor);

@inputInfoPlaceholderColor: if(iscolor(@formInfoColor), lighten(@formInfoColor, 40), @formInfoColor);
@inputInfoPlaceholderFocusColor: if(iscolor(@formInfoColor), lighten(@formInfoColor, 30), @formInfoColor);

@inputSuccessPlaceholderColor: if(iscolor(@formSuccessColor), lighten(@formSuccessColor, 40), @formSuccessColor);
@inputSuccessPlaceholderFocusColor: if(iscolor(@formSuccessColor), lighten(@formSuccessColor, 30), @formSuccessColor);

@inputWarningPlaceholderColor: if(iscolor(@formWarningColor), lighten(@formWarningColor, 40), @formWarningColor);
@inputWarningPlaceholderFocusColor: if(iscolor(@formWarningColor), lighten(@formWarningColor, 30), @formWarningColor);

@defaultHighlightMatchesBackground: revert;
@defaultHighlightMatchesColor: revert;
