/*******************************
        Slider Variables
*******************************/

/* -------------------
       Element
-------------------- */

@height: 1.5em;
@hoverPointer: auto;
@padding: 1em 0.5em;

/* Track */
@trackHeight: 0.4em;
@trackPositionTop: (@height / 2) - (@trackHeight / 2);
@background: #ccc;
@trackBorderRadius: 4px;
@trackColor: @transparentBlack;

/* Track Fill */
@trackFillHeight: @trackHeight;
@trackFillColor: @black;
@trackFillColorFocus: @blackHover;
@invertedTrackFillColor: @lightBlack;
@invertedTrackFillColorFocus: @lightBlackHover;
@trackFillBorderRadius: @trackBorderRadius;

/* Thumb */
@thumbHeight: @height;
@thumbBorderRadius: 100%;
@thumbBackground: @white @subtleGradient;
@thumbShadow:
    @subtleShadow,
    0 0 0 1px @borderColor inset;
@thumbTransitionDuration: 0.3s;
@thumbTransition: background @thumbTransitionDuration @defaultEasing;
@thumbVerticalSliderOffset: 0.03em;

/* Thumb Hover */
@thumbHoverPointer: pointer;
@thumbHoverBackground: @whiteHover @subtleGradient;

/* -------------------
        States
-------------------- */

/* Disabled */
@disabledOpactiy: 0.5;
@disabledTrackFillColor: @background;

/* -------------------
      Variations
-------------------- */

/* Vertical */
@verticalPadding: 0.5em 1em;

/* Labeled */
@labelHeight: @height;
@labelWidth: 1px;
@labelColor: @background;
@labelPadding: 0.2em 0;

/* Hover */
@hoverVarOpacity: 0;
@hoverVarHoverOpacity: 1;
@hoverOpacityTransitionDuration: 0.2s;
@hoverOpacityTransition: opacity @hoverOpacityTransitionDuration linear;

/* Sizing */
@smallHeight: 1em;
@smallLabelHeight: @smallHeight;
@smallTrackHeight: 0.3em;
@smallTrackPositionTop: (@smallHeight / 2) - (@smallTrackHeight / 2);

@largeHeight: 2em;
@largeLabelHeight: @largeHeight;
@largeTrackHeight: 0.5em;
@largeTrackPositionTop: (@largeHeight / 2) - (@largeTrackHeight / 2);

@bigHeight: 2.5em;
@bigLabelHeight: @bigHeight;
@bigTrackHeight: 0.6em;
@bigTrackPositionTop: (@bigHeight / 2) - (@bigTrackHeight / 2);
