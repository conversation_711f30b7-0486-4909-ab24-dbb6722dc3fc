/*!
 * # Fomantic-UI - Search
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "search";

@import (multiple) "../../theme.config";

/*******************************
             Search
*******************************/

.ui.search {
    position: relative;
}

.ui.search > .prompt {
    margin: 0;
    outline: none;
    -webkit-appearance: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    text-shadow: none;
    font-style: normal;
    font-weight: @normal;
    line-height: @promptLineHeight;
    padding: @promptPadding;
    font-size: @promptFontSize;
    background: @promptBackground;
    border: @promptBorder;
    color: @promptColor;
    box-shadow: @promptBoxShadow;
    transition: @promptTransition;
}

.ui.search .prompt {
    border-radius: @promptBorderRadius;
}

/* --------------
     Icon
--------------- */

.ui.search .prompt ~ .search.icon {
    cursor: pointer;
}

/* --------------
    Results
--------------- */

.ui.search > .results {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    transform-origin: center top;
    white-space: normal;
    text-align: left;
    text-transform: none;
    background: @resultsBackground;
    margin-top: @resultsDistance;
    width: @resultsWidth;
    border-radius: @resultsBorderRadius;
    box-shadow: @resultsBoxShadow;
    border: @resultsBorder;
    z-index: @resultsZIndex;
}
.ui.search > .results > :first-child {
    border-radius: @resultsBorderRadius @resultsBorderRadius 0 0;
}
.ui.search > .results > :last-child {
    border-radius: 0 0 @resultsBorderRadius @resultsBorderRadius;
}

/* --------------
    Result
--------------- */

.ui.search > .results .result {
    cursor: pointer;
    display: block;
    overflow: hidden;
    font-size: @resultFontSize;
    padding: @resultPadding;
    color: @resultTextColor;
    line-height: @resultLineHeight;
    border-bottom: @resultDivider;
}
.ui.search > .results .result:last-child {
    border-bottom: @resultLastDivider !important;
}

/* Image */
.ui.search > .results .result .image {
    float: @resultImageFloat;
    overflow: hidden;
    background: @resultImageBackground;
    width: @resultImageWidth;
    height: @resultImageHeight;
    border-radius: @resultImageBorderRadius;
}
.ui.search > .results .result .image img {
    display: block;
    width: auto;
    height: 100%;
}

/* --------------
      Info
--------------- */

.ui.search > .results .result .image + .content {
    margin: @resultImageMargin;
}

.ui.search > .results .result .title {
    margin: @resultTitleMargin;
    font-family: @resultTitleFont;
    font-weight: @resultTitleFontWeight;
    font-size: @resultTitleFontSize;
    color: @resultTitleColor;
}
.ui.search > .results .result .description {
    margin-top: @resultDescriptionDistance;
    font-size: @resultDescriptionFontSize;
    color: @resultDescriptionColor;
}
.ui.search > .results .result .price {
    float: @resultPriceFloat;
    color: @resultPriceColor;
}

/* --------------
    Message
--------------- */

.ui.search > .results > .message {
    padding: @messageVerticalPadding @messageHorizontalPadding;
}
.ui.search > .results > .message .header {
    font-family: @headerFont;
    font-size: @messageHeaderFontSize;
    font-weight: @messageHeaderFontWeight;
    color: @messageHeaderColor;
}
.ui.search > .results > .message .description {
    margin-top: @messageDescriptionDistance;
    font-size: @messageDescriptionFontSize;
    color: @messageDescriptionColor;
}

/* View All Results */
.ui.search > .results > .action {
    display: block;
    border-top: @actionBorder;
    background: @actionBackground;
    padding: @actionPadding;
    color: @actionColor;
    font-weight: @actionFontWeight;
    text-align: @actionAlign;
}

/*******************************
            States
*******************************/

/* --------------------
       Focus
--------------------- */

.ui.search > .prompt:focus {
    border-color: @promptFocusBorderColor;
    background: @promptFocusBackground;
    color: @promptFocusColor;
}

& when (@variationSearchLoading) {
    /* --------------------
           Loading
    --------------------- */

    .ui.loading.search .input > i.icon::before {
        position: absolute;
        content: "";
        top: 50%;
        left: 50%;
        margin: @loaderMargin;
        width: @loaderSize;
        height: @loaderSize;
        border-radius: @circularRadius;
        border: @loaderLineWidth solid @loaderFillColor;
    }
    .ui.loading.search .input > i.icon::after {
        position: absolute;
        content: "";
        top: 50%;
        left: 50%;
        margin: @loaderMargin;
        width: @loaderSize;
        height: @loaderSize;
        animation: loader @loaderSpeed infinite linear;
        border: @loaderLineWidth solid @loaderLineColor;
        border-radius: @circularRadius;
        box-shadow: 0 0 0 1px transparent;
    }
}

/* --------------
      Hover
--------------- */

.ui.search > .results .result:hover,
.ui.category.search > .results .category .result:hover {
    background: @resultHoverBackground;
}
.ui.search .action:hover:not(div) {
    background: @actionHoverBackground;
}

/* --------------
      Active
--------------- */
& when (@variationSearchCategory) {
    .ui.category.search > .results .category.active {
        background: @categoryActiveBackground;
    }
    .ui.category.search > .results .category.active > .name {
        color: @categoryNameActiveColor;
    }
}

.ui.search > .results .result.active,
.ui.category.search > .results .category .result.active {
    position: relative;
    border-left-color: @resultActiveBorderLeft;
    background: @resultActiveBackground;
    box-shadow: @resultActiveBoxShadow;
}
.ui.search > .results .result.active .title {
    color: @resultActiveTitleColor;
}
.ui.search > .results .result.active .description {
    color: @resultActiveDescriptionColor;
}

& when (@variationSearchDisabled) {
    /* --------------------
            Disabled
    ---------------------- */

    /* Disabled */
    .ui.disabled.search {
        cursor: default;
        pointer-events: none;
        opacity: @disabledOpacity;
    }
}

/*******************************
           Types
*******************************/

& when (@variationSearchSelection) {
    /* --------------
        Selection
    --------------- */

    .ui.search.selection .prompt {
        border-radius: @selectionPromptBorderRadius;
    }

    /* Remove input */
    .ui.search.selection > .icon.input > .remove.icon {
        pointer-events: none;
        position: absolute;
        left: auto;
        opacity: 0;
        color: @selectionCloseIconColor;
        top: @selectionCloseTop;
        right: @selectionCloseRight;
        transition: @selectionCloseTransition;
    }
    .ui.search.selection > .icon.input > .active.remove.icon {
        cursor: pointer;
        opacity: @selectionCloseIconOpacity;
        pointer-events: auto;
    }
    .ui.search.selection > .icon.input:not([class*="left icon"]) > .icon ~ .remove.icon {
        right: @selectionCloseIconInputRight;
    }
    .ui.search.selection > .icon.input > .remove.icon:hover {
        opacity: @selectionCloseIconHoverOpacity;
        color: @selectionCloseIconHoverColor;
    }
}

& when (@variationSearchCategory) {
    /* --------------
        Category
    --------------- */

    .ui.category.search .results {
        width: @categoryResultsWidth;
    }

    .ui.category.search .results.animating,
    .ui.category.search .results.visible {
        display: table;
    }

    /* Category */
    .ui.category.search > .results .category {
        display: table-row;
        background: @categoryBackground;
        box-shadow: @categoryBoxShadow;
        transition: @categoryTransition;
    }

    /* Last Category */
    .ui.category.search > .results .category:last-child {
        border-bottom: none;
    }

    /* First / Last */
    .ui.category.search > .results .category:first-child .name + .result {
        border-radius: 0 @resultsBorderRadius 0 0;
    }
    .ui.category.search > .results .category:last-child .result:last-child {
        border-radius: 0 0 @resultsBorderRadius 0;
    }

    /* Category Result Name */
    .ui.category.search > .results .category > .name {
        display: table-cell;
        text-overflow: ellipsis;
        width: @categoryNameWidth;
        white-space: @categoryNameWhitespace;
        background: @categoryNameBackground;
        font-family: @categoryNameFont;
        font-size: @categoryNameFontSize;
        padding: @categoryNamePadding;
        font-weight: @categoryNameFontWeight;
        color: @categoryNameColor;
        border-bottom: @categoryDivider;
    }

    /* Category Result */
    .ui.category.search > .results .category .results {
        display: table-cell;
        background: @categoryResultBackground;
        border-left: @categoryResultLeftBorder;
        border-bottom: @categoryDivider;
    }
    .ui.category.search > .results .category .result {
        border-bottom: @categoryResultDivider;
        transition: @categoryResultTransition;
        padding: @categoryResultPadding;
    }

    & when (@variationSearchHorizontalCategory) {
        .ui.horizontal.category.search > .results .category,
        .ui.horizontal.category.search > .results .category > .name,
        .ui.horizontal.category.search > .results .category > .results {
            display: block;
        }

        .ui.horizontal.category.search > .results .category > .results {
            width: 100%;
            border-left: 0;
        }
    }
}

/*******************************
           Variations
*******************************/

& when (@variationSearchScrolling),
(@variationSearchShort),
(@variationSearchLong) {
    /* -------------------
         Scrolling
    -------------------- */

    .ui.scrolling.search > .results,
    .ui.search.long > .results,
    .ui.search.short > .results {
        overflow-x: hidden;
        overflow-y: auto;
        overscroll-behavior: @overscrollBehavior;
        backface-visibility: hidden;
    }
}

& when (@variationSearchScrolling) {
    @media only screen and (max-width: @largestMobileScreen) {
        .ui.scrolling.search > .results {
            max-height: @scrollingMobileMaxResultsHeight;
            .resizable& when (@variationSearchResizable) {
                height: @scrollingMobileMaxResultsHeight;
            }
        }
    }
    @media only screen and (min-width: @tabletBreakpoint) {
        .ui.scrolling.search > .results {
            max-height: @scrollingTabletMaxResultsHeight;
            .resizable& when (@variationSearchResizable) {
                height: @scrollingTabletMaxResultsHeight;
            }
        }
    }
    @media only screen and (min-width: @computerBreakpoint) {
        .ui.scrolling.search > .results {
            max-height: @scrollingComputerMaxResultsHeight;
            .resizable& when (@variationSearchResizable) {
                height: @scrollingComputerMaxResultsHeight;
            }
        }
    }
    @media only screen and (min-width: @widescreenMonitorBreakpoint) {
        .ui.scrolling.search > .results {
            max-height: @scrollingWidescreenMaxResultsHeight;
            .resizable& when (@variationSearchResizable) {
                height: @scrollingWidescreenMaxResultsHeight;
            }
        }
    }
    & when (@variationSearchResizable) {
        .ui.scrolling.resizable.search > .results {
            resize: @resizableDirection;
            max-height: none;
        }
    }
}

@media only screen and (max-width: @largestMobileScreen) {
    & when (@variationSearchShort) {
        .ui.search.short > .results {
            max-height: @scrollingMobileMaxResultsHeight;
        }
        & when (@variationSearchVeryShort) {
            .ui.search[class*="very short"] > .results {
                max-height: @scrollingMobileMaxResultsHeight * 0.75;
            }
        }
    }
    & when (@variationSearchLong) {
        .ui.search.long > .results {
            max-height: @scrollingMobileMaxResultsHeight * 2;
        }
        & when (@variationSearchVeryLong) {
            .ui.search[class*="very long"] > .results {
                max-height: @scrollingMobileMaxResultsHeight * 3;
            }
        }
    }
}
@media only screen and (min-width: @tabletBreakpoint) {
    & when (@variationSearchShort) {
        .ui.search.short > .results {
            max-height: @scrollingTabletMaxResultsHeight;
        }
        & when (@variationSearchVeryShort) {
            .ui.search[class*="very short"] > .results {
                max-height: @scrollingTabletMaxResultsHeight * 0.75;
            }
        }
    }
    & when (@variationSearchLong) {
        .ui.search.long > .results {
            max-height: @scrollingTabletMaxResultsHeight * 2;
        }
        & when (@variationSearchVeryLong) {
            .ui.search[class*="very long"] > .results {
                max-height: @scrollingTabletMaxResultsHeight * 3;
            }
        }
    }
}
@media only screen and (min-width: @computerBreakpoint) {
    & when (@variationSearchShort) {
        .ui.search.short > .results {
            max-height: @scrollingComputerMaxResultsHeight;
        }
        & when (@variationSearchVeryShort) {
            .ui.search[class*="very short"] > .results {
                max-height: @scrollingComputerMaxResultsHeight * 0.75;
            }
        }
    }
    & when (@variationSearchLong) {
        .ui.search.long > .results {
            max-height: @scrollingComputerMaxResultsHeight * 2;
        }
        & when (@variationSearchVeryLong) {
            .ui.search[class*="very long"] > .results {
                max-height: @scrollingComputerMaxResultsHeight * 3;
            }
        }
    }
}
@media only screen and (min-width: @widescreenMonitorBreakpoint) {
    & when (@variationSearchShort) {
        .ui.search.short > .results {
            max-height: @scrollingWidescreenMaxResultsHeight;
        }
        & when (@variationSearchVeryShort) {
            .ui.search[class*="very short"] > .results {
                max-height: @scrollingWidescreenMaxResultsHeight * 0.75;
            }
        }
    }
    & when (@variationSearchLong) {
        .ui.search.long > .results {
            max-height: @scrollingWidescreenMaxResultsHeight * 2;
        }
        & when (@variationSearchVeryLong) {
            .ui.search[class*="very long"] > .results {
                max-height: @scrollingWidescreenMaxResultsHeight * 3;
            }
        }
    }
}

& when (@variationSearchAligned) {
    /* -------------------
         Left / Right
    -------------------- */

    .ui[class*="left aligned"].search > .results {
        right: auto;
        left: 0;
    }
    .ui[class*="right aligned"].search > .results {
        right: 0;
        left: auto;
    }
}

/* --------------
    Fluid
--------------- */

& when (@variationSearchFluid) {
    .ui.fluid.search .results {
        width: 100%;
    }
}

/* --------------
      Sizes
--------------- */

.ui.search {
    font-size: @relativeMedium;
}
& when not (@variationSearchSizes = false) {
    each(@variationSearchSizes, {
        @s: @{value}SearchSize;
        .ui.@{value}.search {
            font-size: @@s;
        }
    });
}

& when (@variationSearchHighlightMatches) {
    .ui.search > .results mark {
        background: @highlightMatchesBackground;
        color: @highlightMatchesColor;
    }
}

/* --------------
      Mobile
--------------- */

@media only screen and (max-width: @largestMobileScreen) {
    .ui.search .results {
        max-width: @mobileMaxWidth;
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
