/*******************************
    User Variable Overrides
*******************************/

/* -------------------
    Custom variables
-------------------- */

@stepHeaderContentDisplay: flex;
@wizardHeaderTitleColor: @white;
@wizardHeaderDescriptionColor: rgba(@wizardHeaderTitleColor, 0.7);
@stepActiveColor: @lightBlue;
@stepColor: @stepActiveColor;
@stepTextColor: @white;
@stepsBorderColor: @white;

@stepGap: 20px;
@stepContentVerticalPaddingTop: unit((40 / 14), rem);
@stepContentHorizontalPadding: unit((16 / 14), rem);
@stepContentVerticalPaddingBottom: @stepContentHorizontalPadding;
@stepContentVerticalPaddingTopResponsive: unit((32 / 14), rem);

/* -------------------
       Group
-------------------- */

@stepMargin: 0;
@stepsBorderRadius: 0;
@stepsBackground: #152039;
@stepsBoxShadow: none;
@stepsBorder: 0;

/* -------------------
      Element
-------------------- */

@textColor: @stepTextColor;
@verticalMargin: 0;
@horizontalMargin: 0;

@arrowSize: 0;
@verticalPadding: 0;
@horizontalPadding: 0;
@divider: 0;
@background: '';

@titleFontSize: @relativeMedium;
@titleFontWeight: 500;

/* Arrow */
@arrowDisplay: none;
@activeArrowDisplay: none;

/* Hover */
@hoverBackground: transparent;
@hoverColor: @stepColor;

/* Active */
@activeBackground: transparent;
@activeColor: @stepColor;

/* Active + Hover */
@activeHoverBackground: transparent;
@activeHoverColor: @stepColor;

/* Down */
@downBackground: transparent;
@downColor: @stepColor;

/* Completed */
@completedColor: @white;
