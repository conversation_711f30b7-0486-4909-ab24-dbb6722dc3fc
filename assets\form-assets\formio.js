/**
* Primary page to control over form
* Applied custom events and using hooks from formio to load form with static master data
*
**/


// global variables
var resp;
var tempData = '';
var onSuccessfulSubmission;
var onErrorSubmission;
var formInitialSubData = '';
var formLatestSubData = '';
var formReadOnly;
var formJsonSchema;
var partiallySaved = false;
var mandatoryFieldsIsEmpty = true;
var form_io_form;
var barcodeComponent;
var barcodeInputID;
var languageTranslation = {};
var selectedlanguage;
var originalDataStr;

window.form = {};
// load the form
function loadForm(rendererContainer, formDesignSchema, formLoadOption, formSubData) {
    console.log("formSubData " + JSON.stringify(formSubData))
    //let ua = navigator.userAgent.toLowerCase();

    // if(ua && ua.indexOf("android") > -1){
    //     window.form.isAndroid = true;
    // }  
    // else {
    //     window.form.isAndroid = false
    // }

    // if(ua && ua.indexOf("ios") > -1){
    //     window.form.iosPlatform = true
    // }
    // else{
    //     window.form.iosPlatform = false
    // } 
    // console.log("Platform", window.form.isAndroid, window.form.iosPlatform)
    window.platform = {};
    window.platform.submissionData = formSubData;
    Formio.use(window.semantic.default);

    // create form and apply hooks to load form before rendering it in order to put data into form
    // Formio.createForm(rendererContainer, formDesignSchema, formLoadOption)
    //     .then(form => beforeRender(form, formSubData, formLoadOption))
    //     .catch(err => console.log('Error while fetching form: ', err));

};

// hook the form before loading it and put all custom control over form
function beforeRender(formIOForm, subData, formLoadOption) {
    languageTranslation["en"] = {
        Confirm: "Confirm!",
        Error: "Error",
        UnsavedChanges: "You have unsaved changes",
        CannotCompleteForm: "Please fill All Mandatory fields to complete the form.",
        DataSaved: "Data Saved",
        SaveWithoutMandatoryField: "Do you want to save without mandatory field!",
        Discard: "Discard",
        Save: "Save",
        Yes: "Yes",
        No: "No"
    };

    languageTranslation["fr"] = {
        Confirm: "Confirmer!",
        Error: "Erreur",
        UnsavedChanges: "Vous avez des changements non enregistrés",
        CannotCompleteForm: "Veuillez remplir tous les champs obligatoires pour compléter le formulaire",
        DataSaved: "Données enregistrées",
        SaveWithoutMandatoryField: "Voulez-vous enregistrer sans champ obligatoire!",
        Discard: "Jeter",
        Save: "Sauvegarder",
        Yes: "Oui",
        No: "Non"
    };

    selectedlanguage = formLoadOption.language;
    window.platform.submissionData = subData;
    formIOForm.submission = {
        data: subData
    }
    formIOForm.nosubmit = true;
    // set response for form
    resp = {
        submission: '',
        error: '',
        tempData: ''
    };

    // hide save and draft button
    $('[name="data[saveDraft]"]').hide();
    $('[name="data[submit]"]').hide();

    // triggers when submit button clicks.
    formIOForm.on('submit', function (submission) {
        resp = {
            submission: submission,
            error: '',
            tempData: ''
        };
        $('[name="data[unvSubmit]"]').hide();
        onSuccessfulSubmission(resp);
    });

    // triggers when form rendered successfully.
    formIOForm.on('render', function (submission) {
        console.log('Form rendered successfully with subData: ', submission);
        if ($('.btn-wizard-nav-next').length > 0) { } else {
            $('.btn-wizard-nav-submit').hide();
            $('[name="data[unvSubmit]"]').hide();
        }
    });

    // triggers when next button clicks in wizard.
    formIOForm.on('nextPage', function (submission) {
        resp = {
            submission: submission.submission,
            error: '',
            tempData: ''
        };
        $('.btn-wizard-nav-submit').hide();
        $('[name="data[unvSubmit]"]').hide();
        onSuccessfulSubmission(resp);
    });

    // triggers when error occurs while filling form.
    formIOForm.on('error', function (error) {
        if (error && error[0].component && error[0].component.validate && error[0].component.validate.required == true) {
            resp = {
                submission: '',
                error: error[0].message ? error[0].message : 'validation error',
                tempData: tempData
            };
        } else {
            resp = {
                submission: '',
                error: error,
                tempData: ''
            };
        }

        $('.btn-wizard-nav-next').prop('disabled', false);
        $('.btn-wizard-nav-submit').hide();
        $('[name="data[unvSubmit]"]').hide();
        $('.button-icon-right').css("display", "none");
        onErrorSubmission(resp);
    });

    // triggers when change occurs while filling form.
    formIOForm.on('change', function (submission) {
        if (submission && submission.changed && submission.changed.instance && submission.changed.instance.parent && submission.changed.instance.parent.parent && submission.changed.instance.parent.parent.type) {
            if (submission.changed.instance.parent.parent.type === 'form') {
                if (originalDataStr && submission.changed.instance.parent.parent.component && submission.changed.instance.parent.parent.component.nestedFormComponenttype && submission.changed.instance.parent.parent.component.nestedFormComponenttype === 'smartNestedForm') {
                    console.log("updating originalDataStr.form.data")
                    originalDataStr[submission.changed.instance.parent.parent.component.key].data = submission.data;
                } else {
                    originalDataStr = submission.data;
                }
            }
        } else {
            if (submission && submission.changed) {
                originalDataStr = submission.data
            } else {
                if (submission && submission.changed === undefined && submission.data) {
                    console.log("change event for first time")
                    originalDataStr = submission.data;
                    localStorage.setItem("initialFormData", JSON.stringify(submission.data));
                    console.log("first time change data " + JSON.stringify(submission.data));
                }
            }
        }
        tempData = {
            data: originalDataStr
        };

        resp = {
            "submission": '',
            "error": '',
            "tempData": tempData
        };
        partiallySaved = false;
    });

    // F:\new eam 2\eam\assets\form-assets\responsive-grid.min.css

    form_io_form = formIOForm;
    console.log('### FORM_IO_FORM ### ', form_io_form);

    formIOForm.ready.then(() => {
        console.log("formReady called")
        //  let eventCustom = new CustomEvent('FormRenderingComplete', {});
        // document.dispatchEvent(eventCustom);

        // if ((($('.table-bordered').find("th").length > 0)) || ($(".list-group-header").length > 0)) {
        //     console.log("before alignTable")
        //     alignTableContents();
        //     $('head').append('<link rel="stylesheet" href="assets/form-assets/responsive-grid.min.css" type="text/css" />');
        // }
    });
}

function alignTableContents() {
    let activeMode = false;
    // if (this.platform.is('mobile') && ($(window).width() < 760)) {
    //   activeMode = true;
    // }
    insertContents();
    flexTable();
    function flexTable() {
        // if (($(window).width() < 760) || activeMode)
        console.log("--------------------------------------")
        console.log($(window).width())
        if (($(window).width() < 760 || activeMode)) { // window is less than 768px
            showTableContents();
        } else {
            console.log("above 760px")
            $(".list-group-header").show();
        }
    }

    function showTableContents() {
        $(".list-group-header").hide();
        $(".table-bordered").each(function (val) {
            if ($(this).find('table').length == 0) {
                $(this).find(".table-bordered-thead").show();
                $(this).find('thead').hide();
                if ($(window).width() < 760) {
                    $(this).find('tr').addClass('datagrid-table-row')
                    $(this).find('tr').find('td').addClass('datagrid-table-data');
                    $(".table").css("table-layout", "fixed");
                } else {
                    $(".table-bordered").each(function (tabBorderedVal) {
                        if ($(this).find('table').length == 0) {
                            $(this).find('tr').removeClass('datagrid-table-row')
                            $(this).find('tr').find('td').removeClass('datagrid-table-data');
                        }
                    })

                    $(".datagridcard").each(function (datagridcardVal) {
                        $(".datagrid-table").each(function (tabBorderedVal) {
                            if ($(this).find('table').length == 0) {
                                $(this).find('tr').addClass('datagrid-table-row')
                                $(this).find('tr').addClass('col-md-4')
                                $(this).find('tr').addClass('col-lg-3')

                                $(this).find('tr').find('td').addClass('datagrid-table-data');
                                $(".datagrid-table-row").css("border", "none");
                                $(".datagrid-table-row").css("margin-top", "10px");
                            }
                        })
                    });
                }
            }
        });

        $(".table-bordered").each(function (value) {
            if ($(this).find('table').length == 0) {
                var tabledata = $(this).find('td');
                $(tabledata).each(function (val) {
                    if ($(this)[0].children && $(this)[0].children[1] && $(this)[0].children[1].nodeName != undefined && $(this)[0].children[1].nodeName == "BUTTON") {
                        if ($(this)[0].innerText.includes("Add")) {
                            $(this).find(".table-bordered-thead").hide();
                        }
                    }
                })
            }
            $(this).find('tfoot').each(function (val) {
                $(this).find('.table-bordered-thead').hide();
            });
        })
    }

    $(document).ready(function () {
        $(document).on("DOMNodeRemoved", function (e) {
            if (e && e.currentTarget) {
                insertContents();
                flexTable();
            }
        });
    });

    $(document).off().on('click', '.btn', (e) => {
        if (e && e.currentTarget) {
            var val = e.currentTarget.getAttribute("ref");
            if (val != null) {
                if (val.includes("editgrid")) {
                    $(".list-group-header").hide();
                } else if ((val.includes("addRow")) || (val.includes("removeRow"))) {
                    $(".list-group-header").hide();
                    $("table-bordered").find('thead').hide();
                    insertContents();
                    flexTable();
                }
            } else if (e.currentTarget.nodeName === 'BUTTON' && ((e.currentTarget.outerHTML.includes('fa-edit') || (e.currentTarget.outerHTML.includes('fa-trash'))))) {
                if ($(".list-group-header").length > 0) {
                    $(".list-group-header").hide();
                }
            }
        }
    });

    function insertContents() {
        $('.table-bordered').each(function (obj) {
            if ($(this).find('table').length == 0) {
                let $pathis = $(this);
                // fetch the table heading and map with table row entry
                let tHeadings = $pathis.find("th");
                $pathis.find("tr").each(function (trObj) {
                    $(this).addClass('table-row');
                    let $thisObj = $(this);
                    $thisObj.find("td").each(function (tdObj) {
                        let classList;
                        let $this = $(this);
                        if ($(this)[0] && $(this)[0].firstElementChild && $(this)[0].firstElementChild.classList) {
                            classList = $(this)[0].firstElementChild.classList;
                        }
                        if (classList && (classList.contains('formio-button-remove-row') || classList.contains('formio-button-add-row'))) {

                        } else {
                            $this.addClass('table-td');
                            if ($(this).find(".table-bordered-thead").length == 0 && tHeadings[tdObj] != undefined) {
                                let text = tHeadings[tdObj].innerText;
                                $this.append('<div class="table-bordered-thead">' + text + '</div> ');
                                $(".formio-component-label-hidden").css("order", "2");
                                $('.table-bordered-thead').hide();
                            }
                        }
                    });
                });
            }
        })

        $(".table-bordered").each(function (value) {
            if ($(this).find('table').length == 0) {
                var tabledata = $(this).find('td');
                $(tabledata).each(function (val) {
                    if ($(this)[0].children && $(this)[0].children[1] && $(this)[0].children[1].nodeName != undefined && $(this)[0].children[1].nodeName == "BUTTON") {
                        if ($(this)[0].innerText.includes("Add")) {
                            $(this).find(".table-bordered-thead").hide();
                        }
                    }
                })
            }
        })

        $(this).find('tfoot').each(function (val) {
            $(this).find('.table-bordered-thead').hide()
        })
    }
}
/*NEW CODE WINDOW(GET DATA FROM DART TO WEBVIEW) START HERE*/
if (typeof (window.chrome) != "undefined" && window.chrome.webview) {

    window.chrome.webview.addEventListener('message', function (e) {
        var formData = e.data;
        displayForm(formData);
    });
}

/*NEW CODE WINDOW END HERE*/

// prepare data and load form on load of web page
function displayForm(data) {
    WEB_VIEW_NATIVE.setFormData(data);
    // get all parameters for form to load
    var formDataFromAndroid = getFormRequiredDataToLoadForm();
    var formRequiredData = formDataFromAndroid;

    // preparing data to load Form
    var formIOContainer = document.getElementById('formIOContainer');

    // form schema should be in json object
    var decodedStr = decodeUnicode(formRequiredData.webViewFormTemplate);
    // console.log('FORM SCHEMA: ', decodedStr);
    formJsonSchema = JSON.parse(decodedStr);

    // prepare form sub data
    if (formRequiredData.webViewFormData !== '') {

        // When form already have some data [form not loading first time]
        formInitialSubData = JSON.parse(formRequiredData.webViewFormData);
    }

    // set form initial data as last data
    formLatestSubData = JSON.stringify(formInitialSubData);

    // check form mode read/edit
    formReadOnly = getFormModeToRender(formRequiredData.webViewReadMode, formRequiredData.webViewFormPartialFlag, formRequiredData.webViewCompletedFlag);

    var formOptions = {
        readOnly: formReadOnly,
        noAlerts: true,
        language: formRequiredData.language,
        i18n: JSON.parse(formRequiredData.translation)
    }

    // set form title
    setTimeout(() => {
        document.querySelector('#formLabel').innerHTML = formRequiredData.webViewFormTitle;
    }, 200);


    // load form
    // loadForm(formIOContainer, formJsonSchema, formOptions, formInitialSubData);

    // Function to load the form once SDK is ready
    function loadFormWithSDK() {
        console.log("window.loadUnviredForms", typeof window.loadUnviredForms);
        if (typeof window.loadUnviredForms === 'function') {
            window.loadUnviredForms({
                formsData: formJsonSchema,
                submissionData: formInitialSubData || {},
                eventCallback: (data) => {
                    console.log('submit called', data);
                    if (data.type == 'FORM_SUBMIT') {
                        resp = {
                            submission: data?.data?.formData,
                            error: '',
                            tempData: ''
                        };
                        onSuccessfulSubmission(resp);
                    }
                },
                container: formIOContainer,
                options: {
                    mode: 'render',
                    platform: "android",
                    language: 'en',
                    nestedFormData: [],
                    masterData: [],
                    // userData: userData,
                    showMoreButton: false,
                },
            });
        } else {
            console.error('loadUnviredForms is not available. SDK may not be loaded yet.');
        }
    }

    // Check if SDK is ready, if not wait for it
    if (window.unviredSDKReady && typeof window.loadUnviredForms === 'function') {
        loadFormWithSDK();
    } else {
        console.log('Waiting for Unvired SDK to load...');
        window.addEventListener('unviredSDKLoaded', loadFormWithSDK, { once: true });

        // Fallback timeout in case the event doesn't fire
        setTimeout(() => {
            if (typeof window.loadUnviredForms === 'function') {
                loadFormWithSDK();
            } else {
                console.error('Unvired SDK failed to load within timeout period');
            }
        }, 2000);
    }


    // save option for form
    checkAndPrepareSaveOptionsForForm(formReadOnly);

    setTimeout(function () {

        // custom event to fire android scanner and get barcode data
        // document.addEventListener('openBarcode', function (ev) {
        //     WEB_VIEW_NATIVE.navigateToScan()
        //     if (ev.detail) {
        //         barcodeComponent = ev.detail.componentObj;
        //         barcodeInputID = ev.detail.barcodeInputID;
        //         barcodeInputID = barcodeInputID.split('-');
        //     }
        // }, false);

        // custom event to read barcode scan result and bind to input field
        // document.addEventListener('readScannerResult', function (ev) {
        //     let scannedResult = ev.detail;
        //     if (barcodeComponent && barcodeInputID) {
        //         if (form_io_form.getComponentById(barcodeInputID[0]).type == "barcodeselect") {
        //             if (form_io_form.getComponentById(barcodeInputID[0]) != null) {
        //                 setTimeout(() => {
        //                     let selectOptions = form_io_form.getComponentById(barcodeInputID[0]).selectOptions;
        //                     let obj = { value: scannedResult, label: scannedResult, id: String(selectOptions.length) }
        //                     let temp = selectOptions.filter((data) => {
        //                         return data.value == obj.value
        //                     })
        //                     if (temp && temp.length > 0) {
        //                         if (obj) {
        //                             form_io_form.getComponentById(barcodeInputID[0]).setValue(temp[0].value);
        //                             $(".barcode-select-error").remove();
        //                         }
        //                     } else {
        //                         $(document).ready(function () {
        //                             form_io_form.getComponentById(barcodeInputID[0]).resetValue();
        //                             $(".barcode-select-error").remove();
        //                             let id = "#" + form_io_form.getComponentById(barcodeInputID[0]).element.id
        //                             $(id).append("<p class ='text-danger barcode-select-error'><span>&nbsp;" + scannedResult + "&nbsp;is not found</span></p>");
        //                         });
        //                     }
        //                     $(document).ready(function () {
        //                         $('.formio-errors').each(function (obj) {
        //                             if ($(this).find('.error').length == 0) {
        //                                 $(this).css('display', 'none');
        //                             }
        //                         });
        //                     });
        //                     $(".choices__list--dropdown").removeClass("is-active");
        //                 }, 100);
        //             }
        //         } else {
        //             if (form_io_form.getComponentById(barcodeInputID[0]) != null) {
        //                 form_io_form.getComponentById(barcodeInputID[0]).setValue(scannedResult);
        //             }
        //         }



        //     }
        // }, false);

        // disable focus on other elements when signature-pad is active
        let signaturePad = document.querySelector('.signature-pad-canvas');
        if (signaturePad !== undefined && signaturePad !== null) {
            signaturePad.addEventListener('touchstart', () => {
                document.activeElement.blur();
            }, false);
        }
    }, 200);
}

window.addEventListener('load', (ev) => {
});

// get form and order_form data
function getFormRequiredDataToLoadForm() {
    return WEB_VIEW_NATIVE.getFormData();
}
function barcodeChangeEvent() {
    console.log("barcodeChangeEvent called")
    form.triggerChange();
}

// get form mode to render form
function getFormModeToRender(readMode, partiallyFilled, formCompleted) {
    if (readMode == 'X') {
        return true;
    } else {
        if (formCompleted == 'X') {
            return true;
        } else {
            return false;
        }
    }
}

// creating dynamic UI to save form
function checkAndPrepareSaveOptionsForForm(readOnly) {
    if (!readOnly) {
        var toolbar = document.querySelector('.toolbarWrapper');
        var saveBtn = document.createElement("div");
        var saveWithComplete = document.createElement("i");
        var saveWithPartial = document.createElement("i");
        saveWithComplete.id = "saveWithComplete";
        saveWithPartial.id = "saveWithPartial";
        saveWithComplete.className = "fa fa-flag saveForm";
        saveWithPartial.className = "fa fa-floppy-o saveForm";
        saveWithComplete.addEventListener('click', saveAndCompleteForm);
        saveWithPartial.addEventListener('click', savePartialSubData);
        saveBtn.className = "saveBtn"
        saveBtn.appendChild(saveWithComplete);
        saveBtn.appendChild(saveWithPartial);
        toolbar.appendChild(saveBtn);
    }
}

// save form partially irrespective of error like |mandatory fields, form validation, form_io error|
function savePartialSubData() {

    var saveBtn = document.getElementById('saveWithPartial');
    saveBtn.classList.add('onHoverOrClick');
    setTimeout(() => {
        saveBtn.classList.remove('onHoverOrClick');
    }, 200);

    if (partiallySaved == true) {
        WEB_VIEW_NATIVE.showToastMessage(languageTranslation[selectedlanguage].DataSaved);
        return;
    } else {
        return new Promise((resolve, reject) => {
            onSuccessfulSubmission = resolve;
            onErrorSubmission = reject;
            $('[name="data[submit]"]').click();
        })
            .then(formIOResp => {
                formLatestSubData = JSON.stringify(formIOResp.submission.data);
                var sendPartialSuccessSub = formIOResp.submission;
                sendPartialSuccessSub.partialFlag = true;
                sendPartialSuccessSub.completedFlag = false;
                console.log(`S SUCCESS DATA ### ${JSON.stringify(sendPartialSuccessSub)} `);
                WEB_VIEW_NATIVE.saveAndUpdateData(JSON.stringify(sendPartialSuccessSub));
                WEB_VIEW_NATIVE.showToastMessage(languageTranslation[selectedlanguage].DataSaved);
                formInitialSubData = formLatestSubData;
                // destroyWebView();
                mandatoryFieldsIsEmpty = false;
                partiallySaved = true;
            })
            .catch(err => {
                if (JSON.stringify(err) === '{}') {
                    console.log("######### APPARENTLY NO ERROR, ITS JUST EMPTY RESPONSE ERROR ###########");
                    mandatoryFieldsIsEmpty = false;
                    partiallySaved = true;
                } else {
                    if (!partiallySaved) {
                        formLatestSubData = JSON.stringify(err.tempData.data);
                        var sendPartialFailureSub = err.tempData;
                        sendPartialFailureSub.partialFlag = true;
                        sendPartialFailureSub.completedFlag = false;
                        console.log(`S FAILURE DATA ### ${JSON.stringify(sendPartialFailureSub)} `);
                        WEB_VIEW_NATIVE.saveAndUpdateData(JSON.stringify(sendPartialFailureSub));
                        WEB_VIEW_NATIVE.showToastMessage(languageTranslation[selectedlanguage].DataSaved);
                        formInitialSubData = formLatestSubData;
                        // destroyWebView();
                        mandatoryFieldsIsEmpty = true;
                        partiallySaved = true;
                    } else {
                        errorDialog(err.error)
                            .then(result => {
                                if (result == 0) {
                                    console.log(" ########## CONTINUE EDITING ######### ");
                                    mandatoryFieldsIsEmpty = true;
                                    partiallySaved = false;
                                } else {
                                    formLatestSubData = JSON.stringify(err.tempData.data);
                                    var sendPartialFailureSub = err.tempData;
                                    sendPartialFailureSub.partialFlag = true;
                                    sendPartialFailureSub.completedFlag = false;
                                    console.log(`S FAILURE DATA ### ${JSON.stringify(sendPartialFailureSub)} `);
                                    WEB_VIEW_NATIVE.saveAndUpdateData(JSON.stringify(sendPartialFailureSub));
                                    WEB_VIEW_NATIVE.showToastMessage(languageTranslation[selectedlanguage].DataSaved);
                                    formInitialSubData = formLatestSubData;
                                    // destroyWebView();
                                    mandatoryFieldsIsEmpty = true;
                                    partiallySaved = true;
                                }
                            });
                    }
                }
            });
    }

}

// save form and make it complete
function saveAndCompleteForm() {

    var compBtn = document.getElementById('saveWithComplete');
    compBtn.classList.add('onHoverOrClick');
    setTimeout(() => {
        compBtn.classList.remove('onHoverOrClick');
    }, 200);

    if (partiallySaved == true) {
        if (!mandatoryFieldsIsEmpty) {
            var completedFormSub = tempData;
            completedFormSub.partialFlag = false;
            completedFormSub.completedFlag = true;
            WEB_VIEW_NATIVE.saveAndUpdateData(JSON.stringify(completedFormSub));
            //destroyWebView();
            return;
        } else {
            failedAlert()
                .then(result => {
                    console.log(" ########## CONTINUE EDITING AND FILL MANDATORY FIELDS ######### ");
                    // destroyWebView();
                });
        }
    } else {
        return new Promise((resolve, reject) => {
            onSuccessfulSubmission = resolve;
            onErrorSubmission = reject;
            $('[name="data[submit]"]').click();
        })
            .then(formIOResp => {
                formLatestSubData = JSON.stringify(formIOResp.submission.data);
                var sendFormSuccessSub = formIOResp.submission;
                sendFormSuccessSub.partialFlag = false;
                sendFormSuccessSub.completedFlag = true;
                console.log(`C SUCCESS DATA ### ${JSON.stringify(sendFormSuccessSub)} `);
                WEB_VIEW_NATIVE.saveAndUpdateData(JSON.stringify(sendFormSuccessSub));
                WEB_VIEW_NATIVE.showToastMessage(languageTranslation[selectedlanguage].DataSaved);
                formInitialSubData = formLatestSubData;
                //destroyWebView();
            })
            .catch(err => {
                if (JSON.stringify(err) == '{}') {
                    console.log("######### APPARENTLY NO ERROR ON COMPLETION, ITS JUST EMPTY RESPONSE ERROR ###########");
                    destroyWebView();
                } else {
                    failedAlert()
                        .then(result => {
                            console.log(" ########## CONTINUE EDITING AND FILL MANDATORY FIELD ######### ");
                            // destroyWebView();
                        });
                }
            });
    }

}

// check unsaved changes and navigate back
async function checkDataAndGoBack() {

    var backBtn = document.getElementById('backBtnIcon');
    backBtn.classList.add('onHoverOrClick');
    setTimeout(() => {
        backBtn.classList.remove('onHoverOrClick');
    }, 200);

    if (!formReadOnly) {
        if (partiallySaved == true) {
            // Form already saved
            destroyWebView();
        } else {
            var isFormDataChanged = false;
            let lastFormData = await getLastFormData();
            var tempData = "";
            if (lastFormData && lastFormData.tempData && lastFormData.tempData.data) {
                tempData = lastFormData.tempData.data;
            }


            let initialFormData = localStorage.getItem("initialFormData");
            initialFormData = initialFormData ? JSON.parse(initialFormData) : ""

            isFormDataChanged = _.isEqual(tempData, initialFormData);

            // if (formInitialSubData !== '') {
            //     // Already data is saved so compare latest sub data with tempData
            //     isFormDataChanged = _.isEqual(JSON.parse(tempData), JSON.parse(formLatestSubData))
            //     // isFormDataChanged = (getHashCodeWithBase16(formLatestSubData) == getHashCodeWithBase16(lastFormData)) ? false : true;
            // } else {
            //     // Form is not saved and does not have data to compare initial data with tempData
            //     let initialFormData = localStorage.getItem("initialFormData");

            //     isFormDataChanged = _.isEqual(tempData, JSON.parse(initialFormData));

            //     // isFormDataChanged = (getHashCodeWithBase16(formInitialSubData) == getHashCodeWithBase16(lastFormData)) ? false : true;
            // }
            var androidData = getFormRequiredDataToLoadForm();
            var formInitializedData = androidData.webViewFormData;
            var formLatestData = await getLastFormData();
            if (isFormDataChanged) {
                // Destroy web view
                destroyWebView();
            } else {
                confirmDialog(languageTranslation[selectedlanguage].UnsavedChanges)
                    .then(result => {
                        if (result == 0) {
                            // Destroy web view
                            destroyWebView();
                        } else {
                            // Save data and go back
                            var sendPartialSuccessSub = tempData;
                            sendPartialSuccessSub.partialFlag = true;
                            sendPartialSuccessSub.completedFlag = false;
                            WEB_VIEW_NATIVE.saveAndUpdateData(JSON.stringify(sendPartialSuccessSub));
                            formInitialSubData = formLatestSubData;
                            WEB_VIEW_NATIVE.showToastMessage(languageTranslation[selectedlanguage].DataSaved);
                            mandatoryFieldsIsEmpty = false;
                            partiallySaved = true;
                            //destroyWebView();
                        }
                    });
            }
        }
    } else {
        // in read mode just destroy the web view
        destroyWebView();
    }
}

// unsaved changes dialog (on navigating back)
function confirmDialog(msg) {
    return new Promise((res, rej) => {
        $.confirm({
            title: languageTranslation[selectedlanguage].Confirm,
            content: languageTranslation[selectedlanguage].UnsavedChanges,
            buttons: {
                Discard: {
                    text: languageTranslation[selectedlanguage].Discard,
                    action: function () {
                        res(0)
                    }
                },
                Save: {
                    text: languageTranslation[selectedlanguage].Save,
                    action: function () {
                        res(1)
                    }
                }
            }
        });
    })
}

// on error submission dialog
function errorDialog(msg) {
    return new Promise((res, rej) => {
        $.confirm({
            title: languageTranslation[selectedlanguage].Confirm,
            content: msg,
            buttons: {
                No: {
                    text: languageTranslation[selectedlanguage].No,
                    action: function () {
                        res(0)
                    }
                },
                Yes: {
                    text: languageTranslation[selectedlanguage].Yes,
                    action: function () {
                        res(1)
                    }
                }
            }
        });
    })
}

// completion failed dialog
function failedAlert() {
    return new Promise((res, rej) => {
        $.alert({
            title: languageTranslation[selectedlanguage].Error,
            content: languageTranslation[selectedlanguage].CannotCompleteForm
        });
        res(0);
    })
}

// close web view and finish the activity
function destroyWebView() {
    try {
        WEB_VIEW_NATIVE.goBackToPreviousScreen();
    } catch (error) {
        console.log('#### ON DESTROY ERROR #### ', error);
    }
}

// returns most recent form data (on change form)
function getLastFormData() {
    // return JSON.stringify(tempData.data);
    return new Promise((resolve, reject) => {
        resolve(resp);
    })
}

// hash code of json string
function getHashCodeWithBase16(str) {
    var h = 0,
        l = str.length,
        i = 0;
    if (l > 0)
        while (i < l)
            h = (h << 5) - h + str.charCodeAt(i++) | 0;
    return h.toString(16);
}

// Reference - https://attacomsian.com/blog/javascript-base64-encode-decode
// required to decode 16-bit or 32-bit Unicode characters
function decodeUnicode(str) {
    // Going backwards: from bytestream, to percent-encoding, to original string.
    return decodeURIComponent(atob(str).split('').map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
}

// function scanResult(resultData) {
//     console.log('#### compScanResult_1 ', resultData);
//     let readScannerResult = new CustomEvent('readScannerResult', { detail: resultData });
//     document.dispatchEvent(readScannerResult);
// }

// function testfun(data){
//     console.log('DATA ', data);
// }

function getDeviceInfo(data) {
    console.log("Device details:", data);

    try {
        let deviceDetail = data;
        if (typeof data !== 'object') {
            deviceDetail = JSON.parse(data); // Parse the JSON data to create the 'user' object
        } else {
            deviceDetail = data;
        }

        console.log("Device details JSON:", deviceDetail + new Date().getTime);

        // if (deviceDetail && deviceDetail.platform) {
        console.log("platform.platform: ", deviceDetail.platform)
        console.log("platform.v: ", window.form.iosPlatform)
        if (deviceDetail.platform === 'android') {
            window.form.isAndroid = true;
            window.form.iosPlatform = false;
        }
        if (deviceDetail.platform === 'ios') {
            console.log("platform ios: ", deviceDetail.platform)
            window.form.isAndroid = false;
            window.form.iosPlatform = true;
        }
        console.log("platform.v: ", window.form.iosPlatform)
        // }


    } catch (error) {
        console.error("Error parsing JSON:", error);
    }
}
