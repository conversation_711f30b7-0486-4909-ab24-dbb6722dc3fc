/*!
 * # Fomantic-UI - Tab
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "tab";

@import (multiple) "../../theme.config";

/*******************************
           UI Tabs
*******************************/

.ui.tab {
    display: none;
}

/*******************************
             States
*******************************/

/* --------------------
       Active
--------------------- */

.ui.tab.active,
.ui.tab.open {
    display: block;
}

& when (@variationTabLoading) {
    /* --------------------
           Loading
    --------------------- */

    .ui.tab.loading {
        position: relative;
        overflow: hidden;
        display: block;
        min-height: @loadingMinHeight;
    }
    .ui.tab.loading * {
        position: @loadingContentPosition !important;
        left: @loadingContentOffset !important;
    }

    .ui.tab.loading::before,
    .ui.tab.loading.segment::before {
        position: absolute;
        content: "";
        top: @loaderDistanceFromTop;
        left: 50%;
        margin: @loaderMargin;
        width: @loaderSize;
        height: @loaderSize;
        border-radius: @circularRadius;
        border: @loaderLineWidth solid @loaderFillColor;
    }
    .ui.tab.loading::after,
    .ui.tab.loading.segment::after {
        position: absolute;
        content: "";
        top: @loaderDistanceFromTop;
        left: 50%;
        margin: @loaderMargin;
        width: @loaderSize;
        height: @loaderSize;
        animation: loader @loaderSpeed infinite linear;
        border: @loaderLineWidth solid @loaderLineColor;
        border-radius: @circularRadius;
        box-shadow: 0 0 0 1px transparent;
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
