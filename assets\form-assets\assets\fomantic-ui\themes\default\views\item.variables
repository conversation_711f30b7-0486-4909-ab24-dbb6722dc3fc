/*******************************
             Item
*******************************/

/* -------------------
         View
-------------------- */

/* Group */
@groupMargin: 1.5em 0;

/* Item */
@display: flex;
@background: transparent;
@borderRadius: 0;
@minHeight: 0;
@padding: 0;
@width: 100%;
@boxShadow: none;
@border: none;
@zIndex: "";
@transition: box-shadow @defaultDuration @defaultEasing;

/* Responsive */
@itemSpacing: 1em;
@imageWidth: 175px;
@contentImageDistance: 1.5em;

@tabletItemSpacing: 1em;
@tabletImageWidth: 150px;
@tabletContentImageDistance: 1em;

@mobileItemSpacing: 2em;
@mobileImageWidth: auto;
@mobileImageMaxHeight: 250px;
@mobileContentImageDistance: 1.5em;

/* -------------------
       Content
-------------------- */

/* Image */
@imageDisplay: block;
@imageFloat: none;
@imageMaxHeight: "";
@imageVerticalAlign: start;
@imageMargin: 0;
@imagePadding: 0;
@imageBorder: none;
@imageBorderRadius: 0.125rem;
@imageBoxShadow: none;
@imageBorder: none;

/* Content */
@contentDisplay: block;
@contentVerticalAlign: start;

@contentWidth: auto;
@contentOffset: 0;
@contentBackground: none;
@contentMargin: 0;
@contentPadding: 0;
@contentFontSize: 1em;
@contentBorder: none;
@contentBorderRadius: 0;
@contentBoxShadow: none;
@contentColor: @textColor;

/* Header */
@headerMargin: -@lineHeightOffset 0 0;
@headerFontWeight: @bold;
@headerFontSize: @relativeBig;
@headerColor: @darkTextColor;

/* Metadata */
@metaMargin: 0.5em 0 0.5em;
@metaFontSize: 1em;
@metaLineHeight: 1em;
@metaSpacing: 0.3em;
@metaColor: rgba(0, 0, 0, 0.6);

/* Icons */
@actionOpacity: 0.75;
@actionHoverOpacity: 1;
@actionTransition: color @defaultDuration @defaultEasing;

/* Actions */
@favoriteColor: #ffb70a;
@favoriteActiveColor: #ffe623;
@likeColor: #ff2733;
@likeActiveColor: #ff2733;

/* Links */
@headerLinkColor: @headerColor;
@headerLinkHoverColor: @linkHoverColor;
@metaLinkColor: @lightTextColor;
@metaLinkHoverColor: @textColor;
@contentLinkColor: "";
@contentLinkHoverColor: "";
@contentLinkTransition: color @defaultDuration @defaultEasing;

/* Description */
@descriptionDistance: 0.6em;
@descriptionMaxWidth: none;
@descriptionFontSize: 1em;
@descriptionLineHeight: @lineHeight;
@descriptionColor: @textColor;

/* Content Image */
@contentImageWidth: "";
@contentImageVerticalAlign: center;

/* Avatar Image */
@avatarSize: @contentImageWidth;
@avatarBorderRadius: @circularRadius;

/* Paragraph */
@paragraphDistance: 0.5em;

/* Additional Content */
@extraDivider: none;
@extraHorizontalSpacing: 0.5rem;
@extraRowSpacing: 0.5rem;

@extraBackground: none;
@extraDisplay: block;
@extraPosition: relative;
@extraMargin: (1rem - @extraRowSpacing) 0 0;
@extraTop: 0;
@extraLeft: 0;
@extraWidth: 100%;
@extraPadding: 0;
@extraBoxShadow: none;
@extraColor: @lightTextColor;
@extraTransition: color @defaultDuration @defaultEasing;

/* -------------------
      Variations
-------------------- */

/* Relaxed */
@relaxedItemSpacing: 1.5em;
@veryRelaxedItemSpacing: 2em;

/* Divided */
@dividedBorder: 1px solid @borderColor;
@dividedMargin: 0;
@dividedPadding: 1em 0;

@dividedFirstLastMargin: 0;
@dividedFirstLastPadding: 0;

/* Unstackable */
@unstackableMobileImageWidth: 125px;

/* Inverted */
@invertedBackground: @background;
@invertedContentBackground: @contentBackground;
@invertedExtraBackground: @extraBackground;
@invertedHeaderColor: @invertedTextColor;
@invertedHeaderLinkColor: @invertedHeaderColor;
@invertedHeaderLinkHoverColor: @invertedHoveredTextColor;
@invertedMetaLinkColor: @invertedLightTextColor;
@invertedMetaLinkHoverColor: @invertedTextColor;
@invertedContentColor: @invertedTextColor;
@invertedContentLinkColor: if(iscolor(@linkColor), lighten(saturate(@linkColor, 30), 25, relative), @linkColor);
@invertedContentLinkHoverColor: @linkColor;
@invertedExtraColor: @invertedLightTextColor;
@invertedDescriptionColor: @invertedTextColor;
@invertedMetaColor: rgba(255, 255, 255, 0.8);
@invertedLikeColor: if(iscolor(@likeColor), lighten(@likeColor, 10), @likeColor);
@invertedLikeActiveColor: if(iscolor(@likeActiveColor), lighten(@likeActiveColor, 10), @likeActiveColor);
@invertedFavoriteColor: if(iscolor(@favoriteColor), lighten(@favoriteColor, 10), @favoriteColor);
@invertedFavoriteActiveColor: if(iscolor(@favoriteActiveColor), lighten(@favoriteActiveColor, 10), @favoriteActiveColor);
@invertedDividedBorder: 1px solid @whiteBorderColor;

@miniItemSize: @relativeMini;
@tinyItemSize: @relativeTiny;
@smallItemSize: @relativeSmall;
@largeItemSize: @relativeLarge;
@bigItemSize: @relativeBig;
@hugeItemSize: @relativeHuge;
@massiveItemSize: @relativeMassive;
