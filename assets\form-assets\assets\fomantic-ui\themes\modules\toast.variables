/*******************************
            Toast
*******************************/

/* Container */
@toastContainerDistance: @relative12px;

/* Toast */
@toastWidth: 350px;
@toastBorderRadius: @defaultBorderRadius;
@toastPadding: @inputPadding;
@toastMarginBottom: -0.01em;
@toastLeftRightMargin: 1px;
@toastBoxMarginBottom: 0.5em;
@toastBoxMarginRight: @toastBoxMarginBottom;
@toastMarginProgress: -0.2em;
@toastMargin: 0 -@toastLeftRightMargin @toastMarginBottom;
@toastTextColor: @invertedTextColor;
@toastInvertedTextColor: @textColor;
@toastNeutralTextColor: @textColor;

/* Mobile */
@mobileToastBreakpoint: 420px;
@mobileWidth: 280px;

/* on Hover */
@toastOpacityOnHover: 1;
@toastCursorOnHover: pointer;

/* Color variations */
@toastInfoColor: @infoColor;
@toastWarningColor: @warningColor;
@toastErrorColor: @errorColor;
@toastSuccessColor: @successColor;
@toastNeutralColor: @white;
@toastInvertedColor: @black;

@toastBoxBorder: 1px solid rgba(34, 36, 38, 0.12);
@invertedToastBoxBorder: 1px solid rgba(225, 225, 225, 0.5);

/* Icon */
@toastIconContentPadding: 3em;
@toastIconFontSize: 1.5em;
@toastIconMessageContentPadding: 5rem;
@toastIconMessageWidth: 4rem;

/* Image */
@toastImageContentPadding: 1em;
@toastAvatarImageContentPadding: 3em;
@toastMiniImageContentPadding: 3.4em;
@toastTinyImageContentPadding: 7em;
@toastSmallImageContentPadding: 12em;

@toastAvatarImageHeight: 2em;
@toastMiniImageHeight: 35px;
@toastTinyImageHeight: 80px;
@toastSmallImageHeight: 150px;

@toastIconCenteredAdjustment: 1.2em;
@toastImageCenteredAdjustment: 2em;

@toastImageMiniImageAdjustment: e(%("calc(%d + %d)", @inputVerticalPadding, @toastMiniImageHeight));
@toastImageMiniImagePadding: 4.4em;

/* Progressbar Colors */
@toastInfoProgressColor: if(iscolor(@toastInfoColor), darken(@toastInfoColor, 15), @toastInfoColor);
@toastWarningProgressColor: if(iscolor(@toastWarningColor), darken(@toastWarningColor, 15), @toastWarningColor);
@toastErrorProgressColor: if(iscolor(@toastErrorColor), darken(@toastErrorColor, 15), @toastErrorColor);
@toastSuccessProgressColor: if(iscolor(@toastSuccessColor), darken(@toastSuccessColor, 15), @toastSuccessColor);
@toastNeutralProgressColor: if(iscolor(@toastNeutralColor), darken(@toastNeutralColor, 15), @toastNeutralColor);

/* Close Icon */
@toastCloseTopDistance: 0.3em;
@toastCloseRightDistance: 0.3em;
@toastCloseOpacity: 0.7;
@toastCloseTransition: opacity @defaultDuration @defaultEasing;
@toastCloseDistance: 1.5em;
@toastCloseDistanceVertical: 1em;

/* Actions */
@toastActionBackground: rgba(255, 255, 255, 0.25);
@toastActionBorder: 1px solid rgba(0, 0, 0, 0.2);
@toastActionMargin: -1em;
@toastActionMarginTop: 0.5em;
@toastActionMarginLeft: 1em;
@toastActionMarginBottom: 0.3em;
@toastActionPadding: 0.5em;
@toastActionPaddingBottom: 0.75em;
@toastActionCenteredMargin: 0.25em;
