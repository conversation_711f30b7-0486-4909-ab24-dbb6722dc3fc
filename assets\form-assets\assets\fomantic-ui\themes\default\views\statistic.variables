/*******************************
           Statistic
*******************************/

/* -------------------
         View
-------------------- */

@verticalMargin: 1em;
@margin: @verticalMargin 0;
@textAlign: center;
@maxWidth: none;

/* Group */
@horizontalSpacing: 1.5em;
@rowSpacing: 1em;
@groupMargin: @verticalMargin -@horizontalSpacing -@rowSpacing;

/* Group Element */
@elementMargin: 0 @horizontalSpacing @rowSpacing;
@elementMaxWidth: @maxWidth;

/* -------------------
       Content
-------------------- */

/* Value */
@valueFont: @pageFont;
@valueFontWeight: @normal;
@valueLineHeight: 1em;
@valueColor: @black;
@valueTextTransform: uppercase;

/* Label */
@labelSize: @relativeMedium;
@topLabelDistance: 0;
@bottomLabelDistance: 0;
@labelFont: @headerFont;
@labelFontWeight: @bold;
@labelColor: @textColor;
@labelLineHeight: @relativeLarge;
@labelTextTransform: uppercase;

/* Text */
@textValueLineHeight: 1em;
@textValueMinHeight: 2em;
@textValueFontWeight: @bold;

/* Label Image */
@imageHeight: 3rem;
@imageVerticalAlign: baseline;

/* -------------------
      Types
-------------------- */

@horizontalGroupElementMargin: 1em 0;
@horizontalLabelDistance: 0.75em;

/* -------------------
      Variations
-------------------- */

/* Floated */
@leftFloatedMargin: 0 2em 1em 0;
@rightFloatedMargin: 0 0 1em 2em;

/* Inverted */
@invertedValueColor: @white;
@invertedLabelColor: @invertedTextColor;

/* Item Width */
@itemGroupMargin: 0 0 -@rowSpacing;
@itemMargin: 0 0 @rowSpacing;

/* Stackable */
@stackableRowSpacing: 2rem;
@stackableGutter: 2rem;

/* Size */
@miniTextValueSize: 1rem;
@miniValueSize: 1.5rem;
@miniHorizontalValueSize: 1.5rem;

@tinyTextValueSize: 1rem;
@tinyValueSize: 2rem;
@tinyHorizontalValueSize: 2rem;

@smallTextValueSize: 1rem;
@smallValueSize: 3rem;
@smallHorizontalValueSize: 2rem;

@textValueSize: 2rem;
@valueSize: 4rem;
@horizontalValueSize: 3rem;

@largeTextValueSize: 2.5rem;
@largeValueSize: 5rem;
@largeHorizontalValueSize: 4rem;

@bigTextValueSize: 2.5rem;
@bigValueSize: 5.5rem;
@bigHorizontalValueSize: 4.5rem;

@hugeTextValueSize: 2.5rem;
@hugeValueSize: 6rem;
@hugeHorizontalValueSize: 5rem;

@massiveTextValueSize: 3rem;
@massiveValueSize: 7rem;
@massiveHorizontalValueSize: 6rem;
