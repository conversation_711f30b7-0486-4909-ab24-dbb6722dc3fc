import 'dart:convert';

import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/l10n/l10n.dart';
import 'package:eam/models/app_url.dart';
import 'package:eam/models/language.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_image.dart';
import 'package:eam/provider/language_provider.dart';
import 'package:eam/presentation/pages/login/loginstate.dart';
import 'package:eam/presentation/pages/login/widgets/frontend_id_dialogs.dart';
import 'package:eam/widgets/eam_appbar.dart';
import 'package:eam/widgets/server_option_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../notifications/create_notification/widgets/helper.dart';
import '../../../utils/constants.dart';

bool freshLogin = false;

class LoginPage extends StatefulWidget {
  static const routeName = "/loginPage";

  bool selectFromMultipleAccounts;
  List<UnviredAccount> accountList;
  UnviredAccount? selectedAccount;
  String error;

  LoginPage(
      {Key? key,
      required this.selectFromMultipleAccounts,
      required this.accountList,
      required this.error,
      this.selectedAccount})
      : super(key: key);

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  double _windowHeight = 0.0;
  double _windowWidth = 0.0;
  List<AppUrl> urlList = [];
  List<AppLanguage> languageList = [];
  late AppLanguage defaultAppLanguage;
  AppUrl? selectedAppUrl;
  TextEditingController domainController = TextEditingController();
  TextEditingController urlController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  bool _isLoading = false;
  LoginType loginType = LoginType.unvired;
  String port = '';
  String domain = '';
  Map<String, dynamic>? responseObject;

  // http://**************:8080/UMP/API/v2/discovery?domain=CENTURY&application=EAM

  static const urlKey = '@URL@';
  static const domainKey = '@DOMAIN@';
  static const liveURL = 'https://live.unvired.io';
  static const baseURL =
      '${urlKey}/UMP/API/v2/discovery/?api=rtbbxtwfONWzs0V&domain=${domainKey}&application=EAM';

  @override
  void initState() {
    freshLogin = true;
    _initConfig();
    _initURLs();
    _initLoginState();

    // passwordController.text = 'a8Oj*3&GFuvo6U';

    // userNameController.text = 'josephs';

    // userNameController.text = 'kalep';
    // domainController.text = "CENTURY";
    // passwordController.text = "3X20&~|3XOzn";
    //3X20&~|3XOzn
    // urlController.text = 'http://cal-tp-eamdev:8080';
    //   urlController.text = 'http://192.168.98.165:8080';
    //  urlController.text = 'http://192.168.0.191:8080';
    // domainController.text = "CENTURY";
    // passwordController.text = '1Ob!7L%yC0o57v';
    // userNameController.text = 'svigneshwaran';
    domainController.text = "PD";
    userNameController.text = 'PKALE';
    passwordController.text = 'superperformance2025';
//
    if (widget.error.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        UIHelper.showSnackBar(
          context,
          message: widget.error,
        );
        if (widget.selectedAccount != null) {
          userNameController.text = widget.selectedAccount!.getUserName();
          if (widget.selectedAccount!.getAvailableFrontendIds().length > 1) {
            // _showMultipleAccounts(context);
            //  widget.selectedAccount!.setFrontendId(widget.selectedAccount!.getAvailableFrontendIds()[0]);
            Future.delayed(Duration(seconds: 1), () {
              showMultipleFrontEndIdDialog(
                  context: context,
                  title: context.locale.selectFrontEndId,
                  list: widget.selectedAccount!.getAvailableFrontendIds(),
                  callback: _onFrontEndIdSelected);
            });
          }
        }
      });
    }
    _keepAlive();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  _keepAlive() {
    setState(() {
      WakelockPlus.enable();
      // You could also use Wakelock.toggle(on: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    _windowHeight = MediaQuery.of(context).size.height;
    _windowWidth = MediaQuery.of(context).size.width;
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Scaffold(
        appBar: EamAppBar(title: '', appBarHeight: 0.0),
        body: SafeArea(
            child: _isLoading
                ? _getProgressScreen()
                : Container(
                    width: double.infinity,
                    height: double.infinity,
                    child: (PlatformDetails.isMobileScreen(context) ||
                            PlatformDetails.isTabPortraitScreen(context))
                        ? _getMobileScreen()
                        : _geTabScreen())),
      ),
    );
  }

  _getProgressScreen() {
    return Container(
      height: double.infinity,
      decoration: const BoxDecoration(
        image: DecorationImage(
            image: AssetImage("assets/images/login.png"), fit: BoxFit.cover),
      ),
      child: Center(child: CircularProgressIndicator()),
    );
  }

  Widget _getMobileScreen() {
    // Logo
    // Credentials card
    return Container(
        height: double.infinity,
        decoration: const BoxDecoration(
          image: DecorationImage(
              image: AssetImage("assets/images/login.png"), fit: BoxFit.cover),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: 60,
              ),
              _getCompanyLogo(isMobileView: true),
              _getMainCard()
            ],
          ),
        ));
  }

  Widget _geTabScreen() {
    return Container(
      height: double.infinity,
      width: double.infinity,
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: double.infinity,
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage("assets/images/login.png"),
                    fit: BoxFit.cover),
              ),
              child: Center(child: _getCompanyLogo(isMobileView: false)),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(child: Center(child: _getMainCard())),
          ),
        ],
      ),
    );
  }

  Widget _getCompanyLogo({required bool isMobileView}) {
    if (isMobileView) {
      return EamImage(
        imageName: EamImage.unvired_logo_white,
        height: _windowHeight * 0.25,
        width: _windowWidth * 0.5,
      ).image();
    }
    return Container(
      constraints: BoxConstraints(
        minWidth: 400,
        maxWidth: 400,
      ),
      child: EamImage(imageName: EamImage.unvired_logo_landscape_white).image(),
    );
  }

  Widget _getDomainCard(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 0,
      margin: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Container(
        constraints: BoxConstraints(maxWidth: 600),
        padding: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _getTitle(),
            SizedBox(
              height: _windowWidth < 800 ? 40 : 60,
            ),
            SizedBox(
              height: 50,
            ),
            _getDomainTextBox(),
            SizedBox(
              height: 50,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _getCustomURLButton(),
              ],
            ),
            SizedBox(
              height: 16,
            ),
            _getLoginButton(),
          ],
        ),
      ),
    );
  }

  Widget _getUrlCard(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 0,
      margin: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Container(
        constraints: BoxConstraints(maxWidth: 600),
        padding: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _getTitle(),
            SizedBox(
              height: _windowWidth < 800 ? 40 : 60,
            ),
            _getURLTextBox(),
            SizedBox(
              height: 24,
            ),
            _getDomainTextBox(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [_getDomainButton()],
            ),
            SizedBox(
              height: 16,
            ),
            _getLoginButton(),
          ],
        ),
      ),
    );
  }

  Widget _getMainCard() {
    // if (LoginState.currentState == LoginState.stateDomain) {
    //   return _getDomainCard(context);
    // }

    // if (LoginState.currentState == LoginState.stateURL) {
    //   return _getUrlCard(context);
    // }

    return _getCredentialsCard(context);
  }

  Widget _getCredentialsCard(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 0,
      margin: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Container(
        constraints: BoxConstraints(maxWidth: 600),
        padding: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _getTitle(),
            SizedBox(
              height: _windowWidth < 800 ? 40 : 60,
            ),
            _getUsernameTextBox(),
            SizedBox(
              height: 24,
            ),
            _getPasswordTextBox(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (languageList.isNotEmpty && languageList.length > 1) ...[
                  _getLanguageMenu(),
                ],
                _getServerOptionButton(),
                // if (urlList.isNotEmpty && urlList.length > 1) ...[
                //   _getServerOptionButton(),
                // ],
              ],
            ),
            SizedBox(
              height: 16,
            ),
            _getLoginButton()
          ],
        ),
      ),
    );
  }

  // Initialise configuration
  _initConfig() async {
    setState(() {
      _isLoading = true;
    });

    // await _initURLs();

    var result = await _initLanguage();
    if (result.isNotEmpty) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  _initLoginState() {
    domainController.text = "pd";
    if (widget.selectedAccount == null) {
      LoginState.currentState = LoginState.stateCredentials;
    } else {
      selectedAppUrl = new AppUrl(
          url: widget.selectedAccount!.getUrl(),
          name: 'Default',
          isDefault: true);
      // print(selectedAppUrl);
      LoginState.currentState = LoginState.stateCredentials;

      domainController.text = widget.selectedAccount!.getCompany();
      userNameController.text = widget.selectedAccount!.getUserName();
      port = widget.selectedAccount!.getPort();
      passwordController.text = widget.selectedAccount!.getPassword();
      LoginState.currentState = LoginState.stateDomain;
      // _getCredentialsCard(context);
    }
  }

  // Initialize URLS
  Future<List<AppUrl>> _initURLs({Map<String, dynamic>? urlData}) async {
    urlList = [];

    if (urlData == null) {
      final contents = await rootBundle.loadString(
        Constants.APP_CONFIG_PATH,
      );

      final Map<String, dynamic> config = jsonDecode(contents);
      if (config.containsKey('urls')) {
        urlList = List<dynamic>.from(config['urls'])
            .map((e) => AppUrl.fromMap(e))
            .toList();
      }
    } else {
      if (urlData.containsKey('data')) {
        urlList = List<dynamic>.from(urlData['data'])
            .map((e) => AppUrl.fromMap(e))
            .toList();
      }
    }

    selectedAppUrl = await urlList.firstWhere((element) => element.isDefault);
    return urlList;
  }

  // Initialize Languages
  Future<List<AppLanguage>> _initLanguage() async {
    languageList = [];
    final contents = await rootBundle.loadString(
      Constants.APP_CONFIG_PATH,
    );

    final Map<String, dynamic> config = jsonDecode(contents);
    if (config.containsKey('languages')) {
      languageList = List<dynamic>.from(config['languages'])
          .map((e) => AppLanguage.fromMap(e))
          .toList();
      if (languageList.length == 1) {
        defaultAppLanguage = languageList.first;
      } else {
        defaultAppLanguage =
            languageList.where((element) => element.isDefault == true).first;
      }
    }
    return languageList;
  }

  void _validateData() {
    // if (LoginState.currentState == LoginState.stateDomain) {
    //   if (domainController.text.trim().isEmpty) {
    //     UIHelper.showSnackBar(
    //       context,
    //       message: context.locale.domainCannotBeEmpty,
    //     );
    //     return;
    //   }
    //   UIHelper.showEamProgressDialog(context,
    //       title: context.locale.pleaseWaitThreeDot);
    //   _getDetailsByDomain();
    //   return;
    // } else if (LoginState.currentState == LoginState.stateURL) {
    //   if (domainController.text.trim().isEmpty) {
    //     UIHelper.showSnackBar(
    //       context,
    //       message: context.locale.domainCannotBeEmpty,
    //     );
    //     return;
    //   }

    //   if (urlController.text.trim().isEmpty) {
    //     UIHelper.showSnackBar(
    //       context,
    //       message: context.locale.urlCannotBeEmpty,
    //     );
    //     return;
    //   }
    //   UIHelper.showEamProgressDialog(context,
    //       title: context.locale.pleaseWaitThreeDot);
    //   _getDetailsByURL();
    //   return;
    // }

    if (selectedAppUrl == null) {
      UIHelper.showSnackBar(
        context,
        message: context.locale.selectServerToContinue,
      );
    } else if (userNameController.text.trim().isEmpty) {
      UIHelper.showSnackBar(
        context,
        message: context.locale.userNameCannotBeEmpty,
      );
    } else if (passwordController.text.trim().isEmpty) {
      UIHelper.showSnackBar(
        context,
        message: context.locale.passwordCannotBeEmpty,
      );
    } else {
      String targetUmpUrl = selectedAppUrl!.url;
      // Map<String, dynamic>? matchingObject = responseObject!["data"].firstWhere(
      //   (obj) => obj["ump_url"] == targetUmpUrl,
      //   orElse: () => '',
      // );

      // loginType = stringToLoginType(matchingObject?["login_type"]);
      // if (matchingObject?["login_port"] != null) {
      //   port = matchingObject?["login_port"];
      // } else {
      //   port = '';
      // }

      loginType = LoginType.email;
      port = "8443";

      debugPrint("login_type---------------------------: $loginType");
      debugPrint("login_port---------------------------: $port");

      if (widget.selectedAccount == null) {
        widget.selectedAccount = UnviredAccount()
          ..setUserName(userNameController.text.trim().toString())
          ..setPassword(passwordController.text)
          ..setCompany(domainController.text.toString().trim())
          ..setUrl(selectedAppUrl!.url)
          ..setLoginType(loginType);
      } else {
        widget.selectedAccount!.setUrl(selectedAppUrl!.url);
        widget.selectedAccount!
            .setUserName(userNameController.text.trim().toString());
        widget.selectedAccount!.setPassword(passwordController.text.toString());
        widget.selectedAccount!
            .setCompany(domainController.text.toString().trim());
        widget.selectedAccount!.setDomain(domain);
        widget.selectedAccount!.setPort(port);
        widget.selectedAccount!.setLoginType(loginType);
      }
      Navigator.pop(context, widget.selectedAccount);
    }
  }

  void _onFrontEndIdSelected(String frontEndId) {
    widget.selectedAccount!.setFrontendId(frontEndId);
  }

  _getLanguageMenu() {
    return Flexible(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 20),
        alignment: Alignment.topRight,
        width: 220,
        height: 50,
        //color: Color(0xffcadef3),
        child: DropdownButtonHideUnderline(
          child: Consumer<LanguageProvider>(
            builder: (_, provider, __) {
              return DropdownButton<AppLanguage>(
                isExpanded: true,
                value: provider.selectedLanguage,
                items: languageList.map((AppLanguage value) {
                  return DropdownMenuItem<AppLanguage>(
                    value: value,
                    child: Text(
                      value.name,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.black),
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  defaultAppLanguage = value!;
                  context.read<LanguageProvider>().updateLocale(
                      local: L10n.localeByCode(defaultAppLanguage.code),
                      selectedLanguage: defaultAppLanguage);
                },
              );
            },
          ),
        ),
      ),
    );
  }

  _getServerOptionButton() {
    return InkWell(
      onTap: () async {
        showDialog(
            context: context,
            builder: (BuildContext context) {
              return ServerDialogBox(
                title: context.locale.selectServer,
                selectedUrl: selectedAppUrl,
                serverList: urlList,
                onUrlChanged: (AppUrl appUrl) {
                  selectedAppUrl = appUrl;
                  //
                },
              );
            });
      },
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(top: 16, bottom: 16),
        child: Text(
          context.locale.changeServerString,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  _getCustomURLButton() {
    return InkWell(
      onTap: () {
        setState(() {
          LoginState.currentState = LoginState.stateURL;
        });
      },
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(top: 16, bottom: 16),
        child: Text(
          context.locale.use_custom_url,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  _getDomainButton() {
    return InkWell(
      onTap: () {
        setState(() {
          LoginState.currentState = LoginState.stateDomain;
        });
      },
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(top: 16, bottom: 16),
        child: Text(
          context.locale.use_domain,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  _getTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(context.locale.loginString,
            style: TextStyle(
                fontSize: _windowWidth < 800 ? 30 : 34,
                fontWeight: FontWeight.bold)),
        Text("Unvired Mobile EAM",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
      ],
    );
  }

  _getDomainTextBox() {
    return CustomTextField(
      controller: domainController,
      labelName: context.locale.domain,
      isRequiredField: false,
      hintText: '',
      height: 50,
      maxLines: 1,
    );
  }

  _getURLTextBox() {
    return CustomTextField(
      controller: urlController,
      labelName: context.locale.url,
      isRequiredField: false,
      hintText: '',
      height: 50,
      maxLines: 1,
    );
  }

  _getUsernameTextBox() {
    return CustomTextField(
      controller: userNameController,
      labelName: context.locale.usernameString,
      isRequiredField: false,
      hintText: '',
      height: 50,
      maxLines: 1,
    );
  }

  _getPasswordTextBox() {
    return CustomTextField(
      controller: passwordController,
      labelName: context.locale.passwordString,
      isRequiredField: false,
      hintText: '',
      height: 50,
      isPassword: true,
      maxLines: 1,
    );
  }

  _getLoginButtonText() {
    if (LoginState.currentState == LoginState.stateCredentials) {
      return context.locale.loginString;
    }
    return context.locale.continueString;
  }

  _getLoginButton() {
    return Container(
      height: 46,
      width: double.infinity,
      margin: EdgeInsets.symmetric(vertical: 10),
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor:
              MaterialStateProperty.all(Theme.of(context).primaryColor),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6.0),
            ),
          ),
          // padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(20)),
        ),
        child: Text(
          _getLoginButtonText(),
          style: TextStyle(color: Colors.white),
        ),
        onPressed: () {
          //Navigator.pushNamed(context, HomePage.routeName);
          _validateData();
        },
      ),
    );
  }

  Future<void> _getDetailsByDomain() async {
    String url = baseURL.replaceAll(urlKey, liveURL);
    url = url.replaceAll(domainKey, domainController.text.toString());
    try {
      http.Response response = await http.get(Uri.parse(url));

      if (response.statusCode == Status.httpOk) {
        responseObject = jsonDecode(response.body);

        debugPrint(responseObject.toString());
        if (responseObject!['data'].length == 0) {
          invalidDomain();
          return;
        }

        var first_item = responseObject!['data'][0];
        domain = first_item['domain']['domain'];
        debugPrint("Domain------------------------------:$domain");

        await _initURLs(urlData: responseObject);
        UIHelper.closeDialog(context);
        setState(() {
          LoginState.currentState = LoginState.stateCredentials;
        });
      } else {
        invalidDomain();
      }
    } catch (e) {
      invalidDomain();
    }
  }

  invalidDomain() {
    UIHelper.closeDialog(context);
    UIHelper.showEamDialog(context,
        title: context.locale.alertString,
        description: context.locale.invalid_domain,
        negativeActionLabel: context.locale.okayString);
  }

  LoginType stringToLoginType(String value) {
    switch (value) {
      case 'UNVIRED':
        return LoginType.unvired;
      case 'ADS':
        return LoginType.ads;
      case 'sap':
        return LoginType.sap;
      case 'email':
        return LoginType.email;
      case 'custom':
        return LoginType.custom;
      case 'saml':
        return LoginType.saml;
      case 'passwordless':
        return LoginType.passwordless;
      default:
        throw ArgumentError('Invalid LoginType: $value');
    }
  }

  Future<void> _getDetailsByURL() async {
    String url = baseURL.replaceAll(urlKey, urlController.text.toString());
    url = url.replaceAll(domainKey, domainController.text.toString());

    try {
      http.Response response = await http.get(Uri.parse(url));
      debugPrint(response.body);
      responseObject = jsonDecode(response.body);
      if (response.statusCode == Status.httpOk) {
        var first_item = responseObject!['data'][0];
        domain = first_item['domain']['domain'];
        debugPrint("Domain------------------------------:$domain");

        if (responseObject!['data'].length == 0) {
          invalidURL();
          return;
        }

        await _initURLs(urlData: responseObject);
        UIHelper.closeDialog(context);
        setState(() {
          LoginState.currentState = LoginState.stateCredentials;
        });
      } else {
        invalidURL();
      }
    } catch (e) {
      invalidURL();
    }
  }

  invalidURL() {
    UIHelper.closeDialog(context);
    UIHelper.showEamDialog(context,
        title: context.locale.alertString,
        description: context.locale.invalid_url_domain,
        negativeActionLabel: context.locale.okayString);
  }
}
