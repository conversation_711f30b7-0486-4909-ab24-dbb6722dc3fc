import 'dart:developer';

import 'package:eam/be/C_USER_STATUS_HEADER.dart';
import 'package:eam/be/NOTIF_USER_STATUS.dart';
import 'package:eam/be/ORDER_USER_STATUS.dart';
// import 'package:eam/be/NOTIF_USER_STATUS.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/notification_helper.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/models/orders/status_header_model.dart';
import 'package:eam/presentation/widgets/molecules_layer/general/general_helper_widgets.dart';
import 'package:eam/screens/orders/order_detail/general/widget/user_status_dialog.dart';
import 'package:eam/utils/utils.dart';
import 'package:eam/widgets/details_card_widget.dart';
import 'package:eam/widgets/label_value_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class NotificationUserStatusCard2 extends StatefulWidget {
  final double SPACE_1 = 8;
  final double SPACE_2 = 16;
  final NotificationModel notificationModel;
  final bool isNewNotification;
  final bool showEditIconFromDbConfig;
  final Function(List<CheckBoxStatusHeaderModel>, RadioStatusHeaderModel)
      onStatusSave;
  final Function() onStatusCancel;
  const NotificationUserStatusCard2({
    Key? key,
    required this.notificationModel,
    required this.isNewNotification,
    required this.onStatusSave,
    required this.onStatusCancel,
    required this.showEditIconFromDbConfig,
  });
  @override
  State<NotificationUserStatusCard2> createState() =>
      _NotificationUserStatusCard2State();
}

class _NotificationUserStatusCard2State
    extends State<NotificationUserStatusCard2> {
  bool isEditedMode = false;
  bool isEditable = false;
  late TextEditingController userStatusWithNo;
  late TextEditingController userStatusWithOutNo;
  late NotificationModel statusModel;
  List<NOTIF_USER_STATUS> notifStatus = [];
  List<C_USER_STATUS_HEADER> availableStatusHeaders = [];
  List<CheckBoxStatusHeaderModel> checkWidgetList = [];
  List<RadioStatusHeaderModel> radioWidgetList = [];
  @override
  void initState() {
    if (widget.isNewNotification) {
      isEditedMode = true;
      isEditable = false;
    } else {
      if (widget.showEditIconFromDbConfig == true) {
        isEditable = true;
      } else {
        isEditable = false;
      }
    }

    status();

    statusModel = NotificationModel(
      userStatus: widget.notificationModel.userStatus,
      systemStatus: widget.notificationModel.systemStatus,
    );
    _initDefaultValues();
    super.initState();
  }

  status() async {
    var status = (await NotificationHelper.getNotifUserStatus(
        notifNo: widget.notificationModel.notificationNo));
    log(status.length.toString());
  }

  @override
  Widget build(BuildContext context) {
    return _rigInfoBody();
    // DetailsCardWidget(
    //   title: AppLocalizations.of(context)!.userStatus,
    //   content: _getCardContent(),
    //   isEditable: isEditable,
    //   onTapEdit: () {
    //     setState(() {
    //       isEditedMode = true;
    //     });
    //     //_navigateToEdit(OrderEditPage.ORDER_MODE_EDIT_GENERAL);
    //   },
    //   onTapCancel: () {
    //     isEditedMode = false;
    //     _initDefaultValues();
    //     widget.onStatusCancel();
    //     setState(() {});
    //   },
    //   onTapSave: () {
    //     isEditedMode = false;
    //     widget.onStatusSave();
    //     setState(() {});
    //   },
    // );
  }

  void _initDefaultValues() {
    userStatusWithNo = TextEditingController(text: statusModel.userStatus);
    userStatusWithOutNo = TextEditingController(text: statusModel.systemStatus);
  }

  _getCardContent() {
    return Column(
      children: [
        LabelValueWidget(
          label: AppLocalizations.of(context)!.userStatusWithNumber,
          isValueEditable: true,
          isValueReadOnly: false,
          valueController:
              TextEditingController(text: widget.notificationModel.userStatus),
          onValueTap: () => {},
        ),
        SizedBox(
          height: widget.SPACE_2,
        ),
        LabelValueWidget(
          label: AppLocalizations.of(context)!.userStatusWithoutNumber,
          isValueEditable: true,
          isValueReadOnly: false,
          valueController:
              TextEditingController(text: widget.notificationModel.userStatus),
          onValueTap: () => {},
        ),
        SizedBox(
          height: widget.SPACE_2,
        ),
      ],
    );
  }

  _rigInfoBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleRow(),
        18.0.spaceY,
        PlatformDetails.isMobileScreen(context) ||
                PlatformDetails.isTabPortraitScreen(context)
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 45,
                    child: _reportedBy(),
                  ),
                  24.0.spaceY,
                  Container(
                    height: 45,
                    child: _userResPonsibel(),
                  ),
                  24.0.spaceY,
                  Container(
                    height: 45,
                    child: _userStatusWithNo(),
                  ),
                  24.0.spaceY,
                  Container(
                    height: 45,
                    child: _systemStatus(),
                  ),
                ],
              )
            : Container(
                height: 45,
                child: Row(
                  children: [
                    Expanded(child: _reportedBy()),
                    Expanded(child: _userResPonsibel()),
                    Expanded(child: _userStatusWithNo()),
                    Expanded(child: _systemStatus()),
                    _checkIsMedium() ? SizedBox() : Expanded(child: SizedBox())
                  ],
                ),
              ),
      ],
    );
  }

  _titleRow() {
    return GeneralTitleRow(
      // showEdit: widget.showEditIconFromDbConfig,
      showEdit: false,
      title: AppLocalizations.of(context)!.userStatus,
      onTapEdit: () {
        Future.delayed(
          Duration.zero,
          () {
            _showStatusDialog();
            // NavigationService.pushNamed(
            //   EditView.routeName,
            //   arguments: EditViewArguments(
            //     title: AppLocalizations.of(context)!.edit,
            //     body: _malfunctionEditCard(),
            //     bottomNavigation: _onEditSave(),
            //   ),
            // );
          },
        );
      },
    );
  }

  _showStatusDialog() async {
    if (!Utils.isNullOrEmpty(widget.notificationModel.notificationType)) {
      //binding.userStatusCard.setVisibility(View.VISIBLE);
    }
    radioWidgetList = [];
    checkWidgetList = [];
    notifStatus = await NotificationHelper.getNotifUserStatus(
        notifNo: widget.notificationModel.notificationNo);

    availableStatusHeaders = await NotificationHelper.getAllNotifUserStatus(
        notifType: widget.notificationModel.notificationType);

    for (C_USER_STATUS_HEADER statusHeader in availableStatusHeaders) {
      if (statusHeader.status_number == 0) {
        CheckBoxStatusHeaderModel checkBoxStatusHeaderModel =
            CheckBoxStatusHeaderModel(
                isChecked: false,
                statusNumber: statusHeader.status_number,
                userStatusCode: statusHeader.user_status_code,
                userStatusDescription: statusHeader.user_status_desc,
                cStatusHeader: statusHeader,
                userStatus: statusHeader.user_status ?? '');

        for (NOTIF_USER_STATUS userStatus in notifStatus) {
          if (Utils.equalsIgnoreCase(
              userStatus.user_status_code, statusHeader.user_status_code)) {
            checkBoxStatusHeaderModel.isChecked = true;
            checkBoxStatusHeaderModel.order_user_status = userStatus;
          }
        }
        checkWidgetList.add(checkBoxStatusHeaderModel);

        //binding.userStatusLayout.addView(checkBox);
      } else {
        RadioStatusHeaderModel radioStatusHeaderModel = RadioStatusHeaderModel(
            isSelected: false,
            statusNumber: statusHeader.status_number,
            userStatusCode: statusHeader.user_status_code,
            userStatusDescription: statusHeader.user_status_desc,
            cStatusHeader: statusHeader,
            userStatus: statusHeader.user_status ?? '');
        //radioButton.setTag(statusHeader);
        for (NOTIF_USER_STATUS userStatus in notifStatus) {
          if (Utils.equalsIgnoreCase(
              userStatus.user_status_code, statusHeader.user_status_code)) {
            radioStatusHeaderModel.isSelected = true;
            radioStatusHeaderModel.order_user_status = userStatus;
          }
        }
        radioWidgetList.add(radioStatusHeaderModel);
      }
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return UserStatusDialog(
          checkList: checkWidgetList,
          radioList: radioWidgetList,
          onOkayPressed:
              (selectedRadio, List<CheckBoxStatusHeaderModel> checkedList) {
            Navigator.of(context, rootNavigator: true).pop();
            print(checkedList.length);
            print(selectedRadio.userStatusCode);
            // _orderGeneralNotifier.editUserStatusSave(
            //     context, checkedList, selectedRadio);

            widget.onStatusSave(checkedList, selectedRadio);

            //UPDATE CURRENT CARD
            String userStatusString = '';
            userStatusString = selectedRadio.userStatusCode ?? '';
            checkedList
                .where((element) => element.isChecked)
                .toList()
                .forEach((element) {
              userStatusString += ' ' + (element.userStatusCode ?? '');
            });
            setState(() {
              userStatusWithNo.text = userStatusString;
            });
          },
          onCancelPressed: () {
            // _orderGeneralNotifier.clearChanges();
          },
        );
      },
    );
  }

  _userStatusWithNo() {
    return GeneralItem(
      title: AppLocalizations.of(context)!.userStatus,
      subtitle: userStatusWithNo.text,
    );
  }

  _systemStatus() {
    return GeneralItem(
        title: AppLocalizations.of(context)!.systemStatus,
        subtitle: userStatusWithOutNo.text);
  }

  _reportedBy() {
    return GeneralItem(
        title: "Reported By", subtitle: widget.notificationModel.reportedBy);
  }

  _userResPonsibel() {
    return GeneralItem(
        title: "User Responsibel",
        subtitle: widget.notificationModel.userResponsibel);
  }

  _checkIsMedium() {
    if (MediaQuery.of(context).size.width > 850) {
      return false;
    } else {
      return true;
    }
  }
}
