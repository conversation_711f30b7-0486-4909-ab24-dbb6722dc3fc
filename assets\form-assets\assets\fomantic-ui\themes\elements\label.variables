/*******************************
             Label
*******************************/

/* -------------------
       Element
-------------------- */

@verticalAlign: baseline;
@verticalMargin: 0;
@horizontalMargin: @relative2px;
@backgroundColor: #e8e8e8;
@color: @mutedTextColor;
@backgroundImage: none;
@verticalPadding: 0.5833em; /* medium is not @emSize custom value required */
@horizontalPadding: 0.833em;
@borderRadius: @absoluteBorderRadius;
@textTransform: none;
@fontWeight: @bold;
@borderWidth: 1px;
@border: 0 solid transparent;

@lineHeightOffset: -(@verticalPadding / 2);

@labelTransitionDuration: @defaultDuration;
@labelTransitionEasing: @defaultEasing;
@transition: background @labelTransitionDuration @labelTransitionEasing;

/* Group */
@groupVerticalMargin: 0.5em;
@groupHorizontalMargin: 0.5em;

/* -------------------
        Parts
-------------------- */

/* Link */
@linkOpacity: 0.5;
@linkTransition: @labelTransitionDuration opacity @labelTransitionEasing;

/* Icon */
@iconDistance: 0.75em;

/* Image */
@imageHeight: (1em + @verticalPadding * 2);

/* Detail */
@detailFontWeight: @bold;
@detailOpacity: 0.8;
@detailIconDistance: 0.25em;
@detailMargin: 1em;

/* Delete */
@deleteOpacity: @linkOpacity;
@deleteSize: @relativeSmall;
@deleteMargin: 0.5em;
@deleteTransition: background @labelTransitionDuration @labelTransitionEasing;

/* -------------------
        Types
-------------------- */

/* Image Label */
@imageLabelBackground: @backgroundColor;
@imageLabelVerticalPadding: @verticalPadding;
@imageLabelHorizontalPadding: @horizontalPadding;
@imageLabelTextDistance: 0.5em;
@imageLabelDetailDistance: @imageLabelTextDistance;
@imageLabelBorderRadius: @borderRadius;
@imageLabelBoxShadow: none;
@imageLabelPadding: @imageLabelVerticalPadding @imageLabelHorizontalPadding @imageLabelVerticalPadding @imageLabelTextDistance;

@imageLabelImageMargin: -@verticalPadding @imageLabelTextDistance -@verticalPadding -@imageLabelTextDistance;
@imageLabelImageBorderRadius: @imageLabelBorderRadius 0 0 @imageLabelBorderRadius;
@imageLabelImageHeight: @imageHeight;

@imageLabelDetailBackground: @strongTransparentBlack;
@imageLabelDetailPadding: @imageLabelVerticalPadding @imageLabelHorizontalPadding;
@imageLabelDetailMargin: -@imageLabelVerticalPadding -@imageLabelHorizontalPadding -@imageLabelVerticalPadding @imageLabelDetailDistance;

/* -------------------
        States
-------------------- */

/* Hover */
@labelHoverBackgroundColor: #e0e0e0;
@labelHoverBackgroundImage: none;
@labelHoverTextColor: @hoveredTextColor;

/* Active */
@labelActiveBackgroundColor: #d0d0d0;
@labelActiveBackgroundImage: none;
@labelActiveTextColor: @selectedTextColor;

/* Active Hover */
@labelActiveHoverBackgroundColor: #c8c8c8;
@labelActiveHoverBackgroundImage: none;
@labelActiveHoverTextColor: @selectedTextColor;

/* -------------------
      Variations
-------------------- */

/* Basic */
@basicBackground: none @white;
@basicBorderWidth: 1px;
@basicBorderWidthOffset: -@basicBorderWidth;
@basicBorderFullWidthOffset: e(%("calc(100%% + %d)", @basicBorderWidth));
@basicBorder: @basicBorderWidth solid @borderColor;
@basicColor: @textColor;
@basicBoxShadow: none;

@basicHoverBackground: @basicBackground;
@basicHoverColor: @linkHoverColor;
@basicHoverBorder: @basicBorder;
@basicHoverBoxShadow: @basicBoxShadow;

@basicVerticalPadding: e(%("calc(%d - %d)", @verticalPadding, @basicBorderWidth));
@basicHorizontalPadding: e(%("calc(%d - %d)", @horizontalPadding, @basicBorderWidth));
@basicImageLabelPadding: e(%("calc(%d - %d)", @imageLabelTextDistance, @basicBorderWidth));

/* Tag */
@tagCircleColor: @white;
@tagCircleSize: 0.5em;
@tagHorizontalPadding: 1.5em;
@tagCircleBoxShadow: 0 -1px 1px 0 rgba(0, 0, 0, 0.3);
@basicTagCircleBoxShadow: 0 -1px 3px 0 rgba(0, 0, 0, 0.8);
@tagTriangleRightOffset: 100%;
@tagTriangleTopOffset: 50%;
@tagTriangleSize: 1.56em;
@tagTriangleBackgroundImage: none;
@tagTransition: none; /* Avoids error with background: inherit; on animation */

/* Ribbon */
@ribbonTriangleSize: 1.2em;
@basicRibbonTriangleSize: e(%("calc(%d - %d)", @ribbonTriangleSize, @basicBorderWidth));
@basicRibbonTriangleSizeOffset: e(%("calc(1rem + %d - %d)", @ribbonTriangleSize, @basicBorderWidth));
@ribbonShadowColor: rgba(0, 0, 0, 0.15);

@ribbonMargin: 1rem;
@ribbonOffset: e(%("calc(%d - %d)", -@ribbonMargin, @ribbonTriangleSize));
@ribbonCenterSize: 1em;
@ribbonCenterOffset: e(%("calc(50% - %d)", @ribbonCenterSize));
@basicRibbonOffset: e(%("calc(%d - %d)", @verticalPadding, @basicBorderWidth));
@ribbonDistance: e(%("calc(%d + %d)", @ribbonMargin, @ribbonTriangleSize));
@rightRibbonOffset: e(%("calc(100%% + %d + %d)", @ribbonMargin, @ribbonTriangleSize));

@ribbonImageTopDistance: 1rem;
@ribbonImageBottomDistance: @ribbonImageTopDistance;
@ribbonImageMargin: -0.05rem; /* Rounding Offset on Triangle */
@ribbonImageOffset: e(%("calc(%d - %d)", -@ribbonImageMargin, @ribbonTriangleSize));
@rightRibbonImageOffset: e(%("calc(100%% + %d + %d)", @ribbonImageMargin, @ribbonTriangleSize));

@ribbonTableMargin: @relativeMedium; /* Rounding Offset on Triangle */
@ribbonTableOffset: e(%("calc(%d - %d)", -@ribbonTableMargin, @ribbonTriangleSize));
@rightRibbonTableOffset: e(%("calc(100%% + %d + %d)", @ribbonTableMargin, @ribbonTriangleSize));

@ribbonZIndex: 1;

/* Inverted */
@invertedBackgroundColor: if(iscolor(@backgroundColor), darken(@backgroundColor, 20), @backgroundColor);
@invertedBackground: @black;
@invertedBoxShadowSize: 2px;
@invertedBorderSize: 1px;

@basicInvertedBorderColor: rgba(255, 255, 255, 0.5);

/* Colors */
@redTextColor: @white;
@orangeTextColor: @white;
@yellowTextColor: @white;
@oliveTextColor: @white;
@greenTextColor: @white;
@tealTextColor: @white;
@blueTextColor: @white;
@violetTextColor: @white;
@purpleTextColor: @white;
@pinkTextColor: @white;
@brownTextColor: @white;
@greyTextColor: @white;
@blackTextColor: @white;

@redHoverTextColor: @white;
@orangeHoverTextColor: @white;
@yellowHoverTextColor: @white;
@oliveHoverTextColor: @white;
@greenHoverTextColor: @white;
@tealHoverTextColor: @white;
@blueHoverTextColor: @white;
@violetHoverTextColor: @white;
@purpleHoverTextColor: @white;
@pinkHoverTextColor: @white;
@brownHoverTextColor: @white;
@greyHoverTextColor: @white;
@blackHoverTextColor: @white;

@primaryHoverTextColor: @white;
@secondaryHoverTextColor: @white;

@primaryTextColor: @invertedTextColor;
@secondaryTextColor: @invertedTextColor;

/* Attached */
@attachedSegmentPadding: 2rem;
@attachedVerticalPadding: 0.75em;
@attachedHorizontalPadding: 1em;

@attachedCornerBorderRadius: @3px;
@attachedBorderRadius: @borderRadius;

@attachedOffset: -@borderWidth;
@attachedWidthOffset: e(%("calc(100%% + %d)", @borderWidth * 2));

/* Corner */
@cornerSizeRatio: 1;
@cornerTransition: color @labelTransitionDuration @labelTransitionEasing;
@cornerTriangleSize: 4em;
@cornerTriangleTransition: border-color @labelTransitionDuration @labelTransitionEasing;
@cornerTriangleZIndex: 1;

@cornerIconSize: @relativeLarge;
@cornerIconTopOffset: @relative9px;
@cornerIconLeftOffset: @relative8px;
@cornerIconRightOffset: @relative8px;

/* Corner Text */
@cornerTextWidth: 3em;
@cornerTextWeight: @bold;
@cornerTextSize: 1em;

/* Horizontal */
@horizontalLabelMinWidth: 3em;
@horizontalLabelMargin: 0.5em;
@horizontalLabelVerticalPadding: 0.4em;

/* Circular Padding */
@circularPadding: 0.5em;
@circularMinSize: 2em;
@emptyCircleSize: 0.5em;

/* Pointing */
@pointingBorderColor: inherit;
@pointingBorderWidth: @borderWidth;
@pointingVerticalDistance: 1em;
@pointingTriangleSize: 0.6666em;
@pointingHorizontalDistance: @pointingTriangleSize;

@pointingTriangleTransition: none; /* Avoids error with background: inherit; on animation */
@pointingTriangleZIndex: 2;

/* Basic Pointing */
@basicPointingTriangleOffset: -@pointingBorderWidth;

/* Floating */
@floatingTopOffset: -1em;
@floatingBottomOffset: @floatingTopOffset;
@floatingAlignOffset: 1.2em;
@floatingZIndex: 100;

/* -------------------
        Group
-------------------- */

/* Sizing */
@mini: @9px;
@tiny: @10px;
@small: @11px;
@medium: @12px;
@large: @absoluteMedium;
@big: @absoluteBig;
@huge: @absoluteHuge;
@massive: @absoluteMassive;
