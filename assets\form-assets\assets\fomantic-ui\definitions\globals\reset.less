/*!
 * # Fomantic-UI - Reset
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "global";
@element: "reset";

@import (multiple) "../../theme.config";

/*******************************
             Reset
*******************************/

/* Border-Box */
*,
*::before,
*::after {
    box-sizing: inherit;
}
html {
    box-sizing: border-box;
}

/* iPad Input Shadows */
input[type="text"],
input[type="email"],
input[type="search"],
input[type="password"] {
    -webkit-appearance: none;
    -moz-appearance: none; /* mobile firefox too! */
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
