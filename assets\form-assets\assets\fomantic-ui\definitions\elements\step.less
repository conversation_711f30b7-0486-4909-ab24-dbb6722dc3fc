/*!
 * # Fomantic-UI - Step
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
             Step
*******************************/

/* --------------
   Load Theme
--------------- */

@type: 'element';
@element: 'step';

@import (multiple) '../../theme.config';

/*******************************
            Plural
*******************************/

.ui.steps {
	display: inline-flex;
	flex-direction: row;
	align-items: stretch;
	margin: @stepMargin;
	background: @stepsBackground;
	box-shadow: @stepsBoxShadow;
	line-height: @lineHeight;
	border-radius: @stepsBorderRadius;
	border: @stepsBorder;
}
.ui.steps:not(.unstackable) {
	flex-wrap: wrap;
}

/* First Steps */
.ui.steps:first-child {
	margin-top: 0;
}

/* Last Steps */
.ui.steps:last-child {
	margin-bottom: 0;
}

/*******************************
           Singular
*******************************/

.ui.steps .step {
	position: relative;
	display: flex;
	flex: 1 0 auto;
	flex-flow: row wrap;
	vertical-align: middle;
	align-items: center;
	justify-content: @justifyContent;
	margin: @verticalMargin @horizontalMargin;
	padding: @verticalPadding @horizontalPadding;
	background: @background;
	color: @textColor;
	box-shadow: @boxShadow;
	border-radius: @borderRadius;
	border: @border;
	border-right: @divider;
	transition: @transition;
}

/* Arrow */
.ui.steps .step::after {
	display: none;
	position: absolute;
	z-index: 2;
	content: '';
	top: @arrowTopOffset;
	right: @arrowRightOffset;
	background-color: @arrowBackgroundColor;
	width: @arrowSize;
	height: @arrowSize;
	border-style: solid;
	border-color: @borderColor;
	border-width: @arrowBorderWidth;
	transition: @transition;
	transform: translateY(-50%) translateX(50%) rotate(-45deg);
}

/* First Step */
.ui.steps .step:first-child {
	padding-left: @horizontalPadding;
	border-radius: @stepsBorderRadius 0 0 @stepsBorderRadius;
}

/* Last Step */
.ui.steps .step:last-child {
	border-radius: 0 @stepsBorderRadius @stepsBorderRadius 0;
	border-right: none;
	margin-right: 0;
}

/* Only Step */
.ui.steps .step:only-child {
	border-radius: @stepsBorderRadius;
}

/*******************************
            Content
*******************************/

/* Title */
.ui.steps .step .title {
	font-family: @titleFontFamily;
	font-size: @titleFontSize;
	font-weight: @titleFontWeight;
}
.ui.steps .step > .title {
	width: 100%;
}

/* Description */
.ui.steps .step .description {
	font-weight: @descriptionFontWeight;
	font-size: @descriptionFontSize;
	color: @descriptionColor;
}
.ui.steps .step > .description {
	width: 100%;
}
.ui.steps .step .title ~ .description {
	margin-top: @descriptionDistance;
}

/* Icon */
.ui.steps .step > i.icon {
	line-height: 1;
	font-size: @iconSize;
	margin: 0 @iconDistance 0 0;
}
.ui.steps .step > i.icon,
.ui.steps .step > i.icon ~ .content {
	display: block;
	flex: 0 1 auto;
	align-self: @iconAlign;
}

/* Horizontal Icon */
.ui.steps:not(.vertical) .step > i.icon {
	width: auto;
}

/* Link */
.ui.steps .link.step,
.ui.steps a.step {
	cursor: pointer;
}

/*******************************
            Types
*******************************/

& when (@variationStepOrdered) {
	/* --------------
         Ordered
    --------------- */

	.ui.ordered.steps {
		counter-reset: ordered;
	}
	.ui.ordered.steps .step::before {
		display: block;
		position: static;
		text-align: center;
		content: counter(ordered);
		align-self: @iconAlign;
		margin-right: @iconDistance;
		font-size: @iconSize;
		counter-increment: ordered;
		font-family: @orderedFontFamily;
		font-weight: @orderedFontWeight;
	}

	.ui.ordered.steps .step > * {
		display: block;
		align-self: @iconAlign;
	}
}

& when (@variationStepVertical) {
	/* --------------
        Vertical
    --------------- */

	.ui.vertical.steps {
		display: inline-flex;
		flex-direction: column;
		overflow: visible;
	}
	.ui.vertical.steps .step {
		justify-content: flex-start;
		border-radius: @borderRadius;
		padding: @verticalPadding @horizontalPadding;
		border-right: none;
		border-bottom: @verticalDivider;
	}
	.ui.vertical.steps .step:first-child {
		padding: @verticalPadding @horizontalPadding;
		border-radius: @stepsBorderRadius @stepsBorderRadius 0 0;
	}
	.ui.vertical.steps .step:last-child {
		border-bottom: none;
		border-radius: 0 0 @stepsBorderRadius @stepsBorderRadius;
	}
	.ui.vertical.steps .step:only-child {
		border-radius: @stepsBorderRadius;
	}

	/* Arrow */
	.ui.vertical.steps .step::after {
		top: @verticalArrowTopOffset;
		right: @verticalArrowRightOffset;
		border-width: @verticalArrowBorderWidth;
		display: @verticalArrowDisplay;
	}
	.ui.right.vertical.steps .step::after {
		border-width: @verticalLeftArrowBorderWidth;
		left: @verticalLeftArrowLeftOffset;
		right: @verticalLeftArrowRightOffset;
		transform: translateY(-50%) translateX(-50%) rotate(-45deg);
	}

	.ui.vertical.steps .active.step::after {
		display: @verticalActiveArrowDisplay;
	}
	.ui.vertical.steps .step:last-child::after {
		display: @verticalLastArrowDisplay;
	}
	.ui.vertical.steps .active.step:last-child::after {
		display: @verticalActiveLastArrowDisplay;
	}
}

/* ---------------
    Responsive
---------------- */

/* Mobile (Default) */
@media only screen and (max-width: (@largestMobileScreen)) {
	/* custom */

	// .ui.steps:not(.unstackable) {
	//     display: inline-flex;
	//     overflow: visible;
	//     flex-direction: column;
	// }
	// .ui.steps:not(.unstackable) .step {
	//     width: 100% !important;
	//     flex-direction: column;
	//     border-radius: @borderRadius;
	//     padding: @verticalPadding @horizontalPadding;
	//     border-right: none;
	//     border-bottom: @stepsBorder;
	// }
	// .ui.steps:not(.unstackable) .step:first-child {
	//     padding: @verticalPadding @horizontalPadding;
	//     border-radius: @stepsBorderRadius @stepsBorderRadius 0 0;
	// }
	// .ui.steps:not(.unstackable) .step:last-child {
	//     border-radius: 0 0 @stepsBorderRadius @stepsBorderRadius;
	//     border-bottom: none;
	// }

	// /* Arrow */
	// .ui.steps:not(.unstackable) .step::after {
	//     top: unset;
	//     bottom: -@arrowSize;
	//     right: 50%;
	//     transform: translateY(-50%) translateX(50%) rotate(45deg);
	// }
	& when (@variationStepVertical) {
		.ui.vertical.steps .active.step:last-child::after {
			display: none;
		}
	}

	/* Content */
	.ui.steps:not(.unstackable) .step .content {
		text-align: center;
	}

	/* Icon */
	.ui.steps:not(.unstackable) .step > i.icon,
	.ui.ordered.steps:not(.unstackable) .step::before {
		margin: 0 0 @mobileIconDistance 0;
	}
}

/*******************************
             States
*******************************/

/* Link Hover */
.ui.steps .link.step:hover::after,
.ui.steps .link.step:hover,
.ui.steps a.step:hover::after,
.ui.steps a.step:hover {
	background: @hoverBackground;
	color: @hoverColor;
}

/* Link Down */
.ui.steps .link.step:active::after,
.ui.steps .link.step:active,
.ui.steps a.step:active::after,
.ui.steps a.step:active {
	background: @downBackground;
	color: @downColor;
}

/* Active */
.ui.steps .step.active {
	cursor: auto;
	background: @activeBackground;
}
.ui.steps .step.active::after {
	background: @activeBackground;
}
.ui.steps .step.active .title {
	color: @activeColor;
}
.ui.ordered.steps .step.active::before,
.ui.steps .active.step i.icon {
	color: @activeIconColor;
}

/* Active Arrow */
.ui.steps .step::after {
	display: @arrowDisplay;
}
.ui.steps .active.step::after {
	display: @activeArrowDisplay;
}
.ui.steps .step:last-child::after {
	display: @lastArrowDisplay;
}
.ui.steps .active.step:last-child::after {
	display: @activeLastArrowDisplay;
}

/* Active Hover */
.ui.steps .link.active.step:hover::after,
.ui.steps .link.active.step:hover,
.ui.steps a.active.step:hover::after,
.ui.steps a.active.step:hover {
	cursor: pointer;
	background: @activeHoverBackground;
	color: @activeHoverColor;
}

/* Completed */
.ui.steps .step.completed > i.icon::before,
.ui.ordered.steps .step.completed::before {
	color: @completedColor;
}

& when (@variationStepDisabled) {
	/* Disabled */
	.ui.steps .disabled.step {
		cursor: auto;
		background: @disabledBackground;
		pointer-events: none;
	}
	.ui.steps .disabled.step,
	.ui.steps .disabled.step .title,
	.ui.steps .disabled.step .description {
		color: @disabledColor;
	}
	.ui.steps .disabled.step::after {
		background: @disabledBackground;
	}
}

/*******************************
           Variations
*******************************/

& when (@variationStepStackable) {
	/* --------------
       Stackable
    --------------- */

	/* Tablet Or Below */
	@media only screen and (max-width: @largestTabletScreen) {
		.ui[class*='tablet stackable'].steps {
			display: inline-flex;
			overflow: visible;
			flex-direction: column;
		}

		/* Steps */
		.ui[class*='tablet stackable'].steps .step {
			flex-direction: column;
			border-radius: @borderRadius;
			padding: @verticalPadding @horizontalPadding;
			border-right: none;
			border-bottom: @stepsBorder;
		}
		.ui[class*='tablet stackable'].steps .step:first-child {
			padding: @verticalPadding @horizontalPadding;
			border-radius: @stepsBorderRadius @stepsBorderRadius 0 0;
		}
		.ui[class*='tablet stackable'].steps .step:last-child {
			border-radius: 0 0 @stepsBorderRadius @stepsBorderRadius;
			border-bottom: none;
		}

		/* Arrow */
		.ui[class*='tablet stackable'].steps .step::after {
			top: unset;
			bottom: -@arrowSize;
			right: 50%;
			transform: translateY(-50%) translateX(50%) rotate(45deg);
		}

		/* Content */
		.ui[class*='tablet stackable'].steps .step .content {
			text-align: center;
		}

		/* Icon */
		.ui[class*='tablet stackable'].steps .step > i.icon,
		.ui[class*='tablet stackable'].ordered.steps .step::before {
			margin: 0 0 @mobileIconDistance 0;
		}
	}
}

& when (@variationStepFluid) {
	/* --------------
          Fluid
    --------------- */

	/* Fluid */
	.ui.fluid.steps {
		display: flex;
		width: 100%;
	}
}

& when (@variationStepAttached) {
	/* --------------
        Attached
    --------------- */

	/* Top */
	.ui.attached.steps {
		width: @attachedWidth !important;
		margin: 0 @attachedHorizontalOffset @attachedVerticalOffset;
		max-width: @attachedWidth;
		border-radius: @stepsBorderRadius @stepsBorderRadius 0 0;
	}
	.ui.attached.steps .step:first-child {
		border-radius: @stepsBorderRadius 0 0 0;
	}
	.ui.attached.steps .step:last-child {
		border-radius: 0 @stepsBorderRadius 0 0;
	}

	/* Bottom */
	.ui.bottom.attached.steps {
		margin: @attachedVerticalOffset @attachedHorizontalOffset 0;
		border-radius: 0 0 @stepsBorderRadius @stepsBorderRadius;
	}
	.ui.bottom.attached.steps .step:first-child {
		border-radius: 0 0 0 @stepsBorderRadius;
	}
	.ui.bottom.attached.steps .step:last-child {
		border-radius: 0 0 @stepsBorderRadius 0;
	}
}

& when (@variationStepEqualWidth) {
	/* -------------------
        Evenly Divided
    -------------------- */

	.ui.one.steps,
	.ui.two.steps,
	.ui.three.steps,
	.ui.four.steps,
	.ui.five.steps,
	.ui.six.steps,
	.ui.seven.steps,
	.ui.eight.steps {
		width: 100%;
	}
	.ui.one.steps > .step,
	.ui.two.steps > .step,
	.ui.three.steps > .step,
	.ui.four.steps > .step,
	.ui.five.steps > .step,
	.ui.six.steps > .step,
	.ui.seven.steps > .step,
	.ui.eight.steps > .step {
		flex-wrap: nowrap;
	}
	.ui.one.steps > .step {
		width: 100%;
	}
	.ui.two.steps > .step {
		width: 50%;
	}
	.ui.three.steps > .step {
		width: 33.333%;
	}
	.ui.four.steps > .step {
		width: 25%;
	}
	.ui.five.steps > .step {
		width: 20%;
	}
	.ui.six.steps > .step {
		width: 16.666%;
	}
	.ui.seven.steps > .step {
		width: 14.285%;
	}
	.ui.eight.steps > .step {
		width: 12.5%;
	}
}

/* -------------------
       Sizes
-------------------- */

.ui.steps .step,
.ui.step {
	font-size: @medium;
}
& when not (@variationStepSizes = false) {
	each(@variationStepSizes, {
        @s: @@value;
        .ui.@{value}.steps .step,
        .ui.@{value}.step {
            font-size: @s;
        }
    });
}

& when (@variationStepInverted) {
	/* --------------
        Inverted
    --------------- */

	.ui.inverted.steps {
		border: 1px solid @solidWhiteBorderColor;
	}

	.ui.inverted.steps .step {
		color: @invertedTextColor;
		background: @black;
		border-color: @solidWhiteBorderColor;
	}

	.ui.inverted.steps .step::after {
		background-color: @black;
		border-color: @solidWhiteBorderColor;
	}

	.ui.inverted.steps .step .description {
		color: @invertedTextColor;
	}

	/* Active */
	.ui.inverted.steps .step.active,
	.ui.inverted.steps .step.active::after {
		background: @invertedActiveBackground;
	}
	.ui.inverted.ordered.steps .step.active::before,
	.ui.inverted.steps .active.step i.icon {
		color: @invertedSelectedTextColor;
	}

	& when (@variationStepDisabled) {
		/* Disabled */
		.ui.inverted.steps .disabled.step,
		.ui.inverted.steps .disabled.step::after {
			background: @invertedDisabledBackground;
		}
		.ui.inverted.steps .disabled.step,
		.ui.inverted.steps .disabled.step .title,
		.ui.inverted.steps .disabled.step .description {
			color: @invertedDisabledTextColor;
		}
	}

	/* Link Hover */
	.ui.inverted.steps .link.step:hover::after,
	.ui.inverted.steps .link.step:hover,
	.ui.inverted.steps a.step:hover::after,
	.ui.inverted.steps a.step:hover {
		background: @invertedHoverBackground;
		color: @invertedHoveredTextColor;
	}

	/* Link Down */
	.ui.inverted.steps .link.step:active::after,
	.ui.inverted.steps .link.step:active,
	.ui.inverted.steps a.step:active::after,
	.ui.inverted.steps a.step:active {
		background: @invertedActiveHoverBackground;
		color: @invertedPressedTextColor;
	}
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) '../../overrides.less';
