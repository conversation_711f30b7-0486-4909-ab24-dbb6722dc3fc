// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/presentation/common_widgets/sync_button.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/presentation/widgets/generic_item/working_file.dart';
import 'package:eam/utils/app_color.dart';
import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:eam/models/generic_ron_model.dart';
import 'package:eam/presentation/common_widgets/priority_label.dart';
import 'package:eam/utils/app_dimension.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

enum GenericModelType { order, notification, round, none }

final clockIcon = EamIcon(iconName: EamIcon.clock, color: Colors.green).icon();

class GenericModelItem extends StatefulWidget {
  const GenericModelItem({
    super.key,
    required this.genericRONModel,
    this.genericModelType = GenericModelType.none,
    this.onSyncCallBack,
    required this.isHistoryPage,
    this.iswebview = false,
  });

  final GenericRONModel genericRONModel;
  final ValueChanged<GenericRONModel>? onSyncCallBack;
  final GenericModelType? genericModelType;
  final bool isHistoryPage;
  final bool? iswebview;

  @override
  State<GenericModelItem> createState() => _GenericModelItemState();
}

class _GenericModelItemState extends State<GenericModelItem> {
  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return widget.iswebview!
        ? GenericModelWebItem(
            genericRONModel: widget.genericRONModel,
            onSubmitCallBack: widget.onSyncCallBack,
            isHistoryPage: widget.isHistoryPage!,
            genericModelType: widget.genericModelType,
          )
        : Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                        child: Text(
                      widget.genericRONModel.title,
                      maxLines: 1,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        color: HexColor("#0F1419"),
                        fontWeight: FontWeight.w700,
                        fontSize: 16,
                      ),
                    )),
                    widget.genericRONModel.operationRunning
                        ? clockIcon
                        : SizedBox()
                  ],
                ),
                //==
                Visibility(
                  visible: widget.genericModelType == GenericModelType.order,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              height: 24,
                              width: 24,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.green[100],
                              ),
                              child: Center(
                                  child: EamIcon(
                                          iconName: EamIcon.order,
                                          height: 16,
                                          width: 16)
                                      .icon()),
                            ),
                            SizedBox(width: 8),
                            Text(
                              "${widget.genericRONModel.key}",
                              maxLines: 1,
                              overflow: TextOverflow.visible,
                              style: TextStyle(
                                  fontWeight: FontWeight.w600, fontSize: 14),
                            ),
                          ],
                        ),
                        SizedBox(
                          width: 12.0,
                        ),
                        Visibility(
                          visible: widget.genericRONModel.notifNo?.isNotEmpty ??
                              false,
                          child: Row(
                            children: [
                              Container(
                                height: 24,
                                width: 24,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.green[100],
                                ),
                                child: Center(
                                    child: EamIcon(
                                            iconName: EamIcon.notification,
                                            height: 16,
                                            width: 16)
                                        .icon()),
                              ),
                              SizedBox(width: 8),
                              Text(
                                "${widget.genericRONModel.notifNo}",
                                maxLines: 1,
                                overflow: TextOverflow.visible,
                                style: TextStyle(
                                    fontWeight: FontWeight.w600, fontSize: 14),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                //==
                Visibility(
                  visible:
                      widget.genericModelType == GenericModelType.notification,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12.0),
                    child: Row(
                      children: [
                        Row(
                          children: [
                            Container(
                              height: 24,
                              width: 24,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.green[100],
                              ),
                              child: Center(
                                  child: Icon(
                                Icons.notifications_none_rounded,
                                size: 16,
                              )),
                            ),
                            SizedBox(width: 8),
                            Text(
                              "${widget.genericRONModel.key}",
                              maxLines: 1,
                              overflow: TextOverflow.visible,
                              style: TextStyle(
                                  fontWeight: FontWeight.w600, fontSize: 14),
                            ),
                          ],
                        ),
                        SizedBox(
                          width: 12.0,
                        ),
                        Visibility(
                          visible: widget.genericRONModel.orderNo?.isNotEmpty ??
                              false,
                          child: Row(
                            children: [
                              Container(
                                height: 24,
                                width: 24,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.green[100],
                                ),
                                child: Center(
                                  child: EamIcon(
                                          iconName: EamIcon.order,
                                          height: 16,
                                          width: 16)
                                      .icon(),
                                ),
                              ),
                              SizedBox(width: 8),
                              Text(
                                "${widget.genericRONModel.orderNo}",
                                maxLines: 1,
                                overflow: TextOverflow.visible,
                                style: TextStyle(
                                    fontWeight: FontWeight.w600, fontSize: 14),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                Visibility(
                  visible: widget.genericModelType ==
                          GenericModelType.notification ||
                      widget.genericModelType == GenericModelType.order,
                  child: Column(
                    children: [
                      SizedBox(
                        height: size.height * 0.01,
                      ),
                      SizedBox(
                        height: 25,
                        child: Row(
                          children: [
                            Container(
                              height: 24,
                              width: 24,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.green[100],
                              ),
                              child: Center(
                                  child: Icon(
                                Icons.straighten,
                                size: 16,
                              )),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                                child: Text(
                              "${widget.genericRONModel.mainWorkCntr} (${widget.genericRONModel.mainWorkCntrDesc})",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontWeight: FontWeight.w600, fontSize: 14),
                            )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(
                  height: size.height * 0.01,
                ),

                // Row(
                //   children: [
                //     Text(
                //       "${_orderNumber(context)} - ${widget.genericRONModel.type}",
                //       maxLines: 1,
                //       style:
                //           TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                //     ),
                //     widget.genericRONModel.maintenanceActivityType.isNotEmpty
                //         ? Text(
                //             " - ${widget.genericRONModel.maintenanceActivityType}",
                //             maxLines: 1,
                //             style: TextStyle(
                //                 fontSize: 14, fontWeight: FontWeight.w600),
                //           )
                //         : SizedBox(),
                //   ],
                // ),

                // Dimensions.kHeight10,
                // SizedBox(
                //   height: size.height * 0.01,
                // ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      height: 24,
                      width: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.green[100],
                      ),
                      child: Center(
                          child: Icon(
                        Icons.calendar_month_outlined,
                        size: 16,
                      )),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        "${widget.genericRONModel.date}",
                        style: TextStyle(
                          color: HexColor("#4F5051"),
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    PriorityLabel(
                      priority: widget.genericRONModel.priority,
                      priorityType: widget.genericRONModel.priority,
                      priorityDesc: widget.genericRONModel.priorityDesc,
                    )
                  ],
                ),

                // _status(),
                SizedBox(
                  height: size.height * 0.015,
                ),
                Container(
                  height: 25,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Row(
                          children: [
                            Container(
                              height: 24,
                              width: 24,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: HexColor("#A0D7E9"),
                              ),
                              child: Center(
                                  child: EamIcon(
                                          iconName: EamIcon.location,
                                          height: 16)
                                      .icon()),
                            ),
                            SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                getFunctionalLocation(),
                                maxLines: 1,
                                //overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    fontWeight: FontWeight.w600, fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // widget.genericModelType == GenericModelType.round
                      //     ? TabPortraitOrMobile.isTabPortraitOrMobile(context)
                      //         ? SizedBox()
                      //         :SyncButton(
                      //                 isadaptable: true,
                      //                 genericRONModel: widget.genericRONModel,
                      //                 onTapaction: () async {
                      //                   _onSyncaction();
                      //                 }, show: widget.isHistoryPage,
                      //               )

                      //     : SizedBox(),
                    ],
                  ),
                ),
                widget.genericModelType == GenericModelType.round
                    ? SizedBox()
                    : SizedBox(height: 12),

                widget.genericModelType == GenericModelType.round
                    ? SizedBox()
                    : widget.genericRONModel.equipmentName.isEmpty
                        ? SizedBox()
                        : SizedBox(
                            height: 25,
                            child: Row(
                              children: [
                                Container(
                                  height: 24,
                                  width: 24,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: HexColor("#B8B0E5"),
                                  ),
                                  child: Center(
                                      child: EamIcon(
                                              iconName: EamIcon.equip,
                                              height: 16)
                                          .icon()),
                                ),
                                SizedBox(width: 8),
                                Expanded(
                                    child: Text(
                                  "${widget.genericRONModel.equipmentName}",
                                  maxLines: 1,
                                  overflow: TextOverflow.visible,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14),
                                )),
                                // TabPortraitOrMobile.isTabPortraitOrMobile(context)
                                //     ? SizedBox()
                                //     : !widget.isHistoryPage
                                //         ? SyncButton(

                                //             genericRONModel: widget.genericRONModel,
                                //             onTapaction: () async {
                                //               _onSyncaction();
                                //             }, show:  widget.isHistoryPage,
                                //           )
                                //         : SizedBox()
                              ],
                            ),
                          ),

                widget.genericModelType == GenericModelType.round ||
                        widget.genericRONModel.equipmentName.isEmpty
                    ? SizedBox.shrink()
                    : SizedBox(height: 12),

                Visibility(
                    visible: widget.genericModelType == GenericModelType.round,
                    child: Column(
                      children: [
                        Divider(),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: 5,
                                    backgroundColor: Color(0xff285FE7),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 5.0),
                                    child: Text(
                                        "Objects : ${widget.genericRONModel.subTitle3}"),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: 5,
                                    backgroundColor: Colors.greenAccent,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 5.0),
                                    child: Text(
                                        "${AppLocalizations.of(context)!.completed} : ${widget.genericRONModel.subTitle2}"),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: 5,
                                    backgroundColor: AppColor.redColor,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 5.0),
                                    child: Text(
                                        "${AppLocalizations.of(context)!.pending} : ${widget.genericRONModel.subTitle1}"),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    )),
                widget.genericModelType == GenericModelType.notification ||
                        widget.genericModelType == GenericModelType.order
                    ? Divider()
                    : SizedBox.shrink(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Visibility(
                      visible: widget.genericModelType ==
                              GenericModelType.notification ||
                          widget.genericModelType == GenericModelType.order,
                      child: Row(
                        children: [
                          Container(
                            height: 24,
                            width: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.green[100],
                            ),
                            child: Center(
                                child: Icon(
                              Icons.account_circle_outlined,
                              size: 16,
                            )),
                          ),
                          SizedBox(width: 8),
                          Text(
                            widget.genericRONModel.reportedBy ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.visible,
                            style: TextStyle(
                                fontWeight: FontWeight.w600, fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: (widget.genericModelType ==
                                  GenericModelType.notification ||
                              widget.genericModelType ==
                                  GenericModelType.order) &&
                          (widget.genericRONModel.assgnedTo != null &&
                              widget.genericRONModel.assgnedTo!.isNotEmpty),
                      child: Row(
                        children: [
                          Container(
                            height: 24,
                            width: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.orange[100],
                            ),
                            child: Center(
                                child: Icon(
                              Icons.account_circle_outlined,
                              size: 16,
                            )),
                          ),
                          SizedBox(width: 8),
                          Text(
                            widget.genericRONModel.assgnedTo ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.visible,
                            style: TextStyle(
                                fontWeight: FontWeight.w600, fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                PlatformDetails.isMobileOrTab(context)
                    ? widget.genericRONModel.status == Colors.transparent
                        ? SizedBox()
                        : !widget.isHistoryPage
                            ? Padding(
                                padding: Dimensions.syncBtnPadding(context,
                                    showBottompadding:
                                        !PlatformDetails.isMobileOrTab(
                                            context)),
                                child: SyncButton(
                                  genericRONModel: widget.genericRONModel,
                                  onTapaction: () async {
                                    _onSyncaction();
                                  },
                                  show: widget.isHistoryPage,
                                ),
                              )
                            : SizedBox()
                    : SizedBox()
              ],
            ),
          );
  }

  String getFunctionalLocation() {
    if (widget.genericModelType == GenericModelType.round) {
      return widget.genericRONModel.rigName ?? "NA";
    } else {
      return widget.genericRONModel.functionalLocation;
    }
  }

  _status() {
    return Column(
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.015,
        ),
        Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            StatusLabel(
              status: widget.genericRONModel.systemStatus,
            ),
            13.0.spaceX,
            StatusLabel(
              status: widget.genericRONModel.userStatus,
            ),
            // 12.0.spaceX,
            // PriorityLabel(
            //   priority: widget.genericRONModel.priority,
            //   priorityType: widget.genericRONModel.priority,
            //   priorityDesc: widget.genericRONModel.priorityDesc,
            // )
          ],
        ),
      ],
    );
  }

  String _orderNumber(BuildContext context) {
    if (widget.genericRONModel.key
        .toLowerCase()
        .startsWith(AppLocalizations.of(context)!.newString.toLowerCase())) {
      return AppLocalizations.of(context)!.newString;
    }
    return widget.genericRONModel.key;
  }

  _onSyncaction() {
    if (widget.onSyncCallBack != null) {
      // setState(() {
      //   widget.genericRONModel.isSubmitRequired = false;
      //   widget.genericRONModel.status = Colors.orange;
      // });
      widget.onSyncCallBack!(widget.genericRONModel);
    }
  }
}

// 3 Dot Menu
// Padding(
//   padding: const EdgeInsets.only(left: 8.0),
//   child: Icon(
//     Icons.more_vert,
//     size: 17,
//     color: HexColor("#0F1419"),
//   ),
// )
