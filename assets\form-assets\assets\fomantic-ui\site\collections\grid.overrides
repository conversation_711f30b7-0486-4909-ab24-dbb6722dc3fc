/*******************************
         Site Overrides
*******************************/

.ui.grid > .column.un-static-column {
	@media only screen and (max-width: @largestMobileScreen) {
		width: 100% !important;
	}
}

.un-accordion {
	// empty column
	.column:has(> .formio-hidden) {
		padding: 0 !important;
	}
}

.ui.grid {
	& > .column:not(.row) {
		&.column-is-empty {
			// @media only screen and (min-width: 768px) and (max-width: 1200px) {
			@media only screen and (max-width: 1200px) {
				display: none !important;
			}
		}
	}
	&:has(> .column:not(.row).column-is-empty) {
		& > .column:not(.row):not(.column-is-empty) {
			@media only screen and (max-width: 1200px) {
				width: 100% !important;
			}
		}
	}
}
