/*
* Font Awesome 5.15.4 by @fontawesome [https://fontawesome.com]
* License - https://fontawesome.com/license (Icons: CC BY 4.0 License, Fonts: SIL OFL 1.1 License, CSS: MIT License)
*/

/*******************************

Fomantic-UI integration of FontAwesome:

// class names are separated
i.icon.angle-left  =>  i.icon.angle.left

// variations are extracted
i.icon.circle      =>  i.icon.circle
i.icon.circle-o    =>  i.icon.circle.outline

// abbreviation are replaced by full words
i.icon.*-h         =>  i.icon.*.horizontal
i.icon.*-v         =>  i.icon.*.vertical
i.icon.alpha       =>  i.icon.alphabet
i.icon.asc         =>  i.icon.ascending
i.icon.desc        =>  i.icon.descending
i.icon.alt         =>  i.icon.alternate

Icons are order A-Z in their group, Solid, Outline, Thin (Pro only) and Brand

*******************************/

/*******************************
             Icon
*******************************/

/* --------------
   Font Files
--------------- */

/* Solid Icons */
@importIcons: true;
@fontName: "icons";
@outlineFontName: "outline-icons";
@thinFontName: "thin-icons";
@brandFontName: "brand-icons";
@duotoneFontName: "duotone-icons";

@fonts: {
    @solid: {
        font-family: @fontName;
        src: url("@{fontPath}/@{fontName}.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{fontName}.woff") format("woff"));
        font-style: normal;
        font-weight: @normal;
        font-display: block;
        font-variant: normal;
        text-decoration: inherit;
        text-transform: none;
    };
    @outline: {
        font-family: @outlineFontName;
        src: url("@{fontPath}/@{outlineFontName}.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{outlineFontName}.woff") format("woff"));
        font-style: normal;
        font-weight: @normal;
        font-display: block;
        font-variant: normal;
        text-decoration: inherit;
        text-transform: none;
    };
    @brand: {
        font-family: @brandFontName;
        src: url("@{fontPath}/@{brandFontName}.woff2") format("woff2") if(@supportIE, e(",") url("@{fontPath}/@{brandFontName}.woff") format("woff"));
        font-style: normal;
        font-weight: @normal;
        font-display: block;
        font-variant: normal;
        text-decoration: inherit;
        text-transform: none;
    };
};

// Underscores in map keys will be replaced by @iconClassSeparator to separate classnames at compile time
@iconClassSeparator: ".";
@iconForcedOrder: false;
@iconForcedAttribute: class;

/* Deprecated (In/Out Naming Conflict) */
@icon-deprecated-map: {
    linkedin_in: "\f0e1";
    zoom_in: "\f00e";
    zoom_out: "\f010";
    sign_in: "\f2f6";
    in_cart: "\f218";
    log_out: "\f2f5";
    sign_out: "\f2f5";
    sign_in_alternate: "\f2f6";
    sign_out_alternate: "\f2f5";
};

/* Icons */
@icon-map: {
    ad: "\f641";
    address_book: "\f2b9";
    address_card: "\f2bb";
    adjust: "\f042";
    air_freshener: "\f5d0";
    align_center: "\f037";
    align_justify: "\f039";
    align_left: "\f036";
    align_right: "\f038";
    allergies: "\f461";
    ambulance: "\f0f9";
    american_sign_language_interpreting: "\f2a3";
    anchor: "\f13d";
    angle_double_down: "\f103";
    angle_double_left: "\f100";
    angle_double_right: "\f101";
    angle_double_up: "\f102";
    angle_left: "\f104";
    angle_right: "\f105";
    angle_up: "\f106";
    angle_down: "\f107";
    angry: "\f556";
    ankh: "\f644";
    archive: "\f187";
    archway: "\f557";
    arrow_alternate_circle_down: "\f358";
    arrow_alternate_circle_left: "\f359";
    arrow_alternate_circle_right: "\f35a";
    arrow_alternate_circle_up: "\f35b";
    arrow_circle_down: "\f0ab";
    arrow_circle_left: "\f0a8";
    arrow_circle_right: "\f0a9";
    arrow_circle_up: "\f0aa";
    arrow_down: "\f063";
    arrow_left: "\f060";
    arrow_right: "\f061";
    arrow_up: "\f062";
    arrows_alternate: "\f0b2";
    arrows_alternate_horizontal: "\f337";
    arrows_alternate_vertical: "\f338";
    assistive_listening_systems: "\f2a2";
    asterisk: "\f069";
    at: "\f1fa";
    atlas: "\f558";
    atom: "\f5d2";
    audio_description: "\f29e";
    award: "\f559";
    baby: "\f77c";
    baby_carriage: "\f77d";
    backspace: "\f55a";
    backward: "\f04a";
    bacon: "\f7e5";
    bacteria: "\e059";
    bacterium: "\e05a";
    bahai: "\f666";
    balance_scale: "\f24e";
    balance_scale_left: "\f515";
    balance_scale_right: "\f516";
    ban: "\f05e";
    band_aid: "\f462";
    barcode: "\f02a";
    bars: "\f0c9";
    baseball_ball: "\f433";
    basketball_ball: "\f434";
    bath: "\f2cd";
    battery_empty: "\f244";
    battery_full: "\f240";
    battery_half: "\f242";
    battery_quarter: "\f243";
    battery_three_quarters: "\f241";
    bed: "\f236";
    beer: "\f0fc";
    bell: "\f0f3";
    bell_slash: "\f1f6";
    bezier_curve: "\f55b";
    bible: "\f647";
    bicycle: "\f206";
    biking: "\f84a";
    binoculars: "\f1e5";
    biohazard: "\f780";
    birthday_cake: "\f1fd";
    blender: "\f517";
    blender_phone: "\f6b6";
    blind: "\f29d";
    blog: "\f781";
    bold: "\f032";
    bolt: "\f0e7";
    bomb: "\f1e2";
    bone: "\f5d7";
    bong: "\f55c";
    book: "\f02d";
    book_dead: "\f6b7";
    book_medical: "\f7e6";
    book_open: "\f518";
    book_reader: "\f5da";
    bookmark: "\f02e";
    border_all: "\f84c";
    border_none: "\f850";
    border_style: "\f853";
    bowling_ball: "\f436";
    box: "\f466";
    box_open: "\f49e";
    box_tissue: "\e05b";
    boxes: "\f468";
    braille: "\f2a1";
    brain: "\f5dc";
    bread_slice: "\f7ec";
    briefcase: "\f0b1";
    briefcase_medical: "\f469";
    broadcast_tower: "\f519";
    broom: "\f51a";
    brush: "\f55d";
    bug: "\f188";
    building: "\f1ad";
    bullhorn: "\f0a1";
    bullseye: "\f140";
    burn: "\f46a";
    bus: "\f207";
    bus_alternate: "\f55e";
    business_time: "\f64a";
    calculator: "\f1ec";
    calendar: "\f133";
    calendar_alternate: "\f073";
    calendar_check: "\f274";
    calendar_day: "\f783";
    calendar_minus: "\f272";
    calendar_plus: "\f271";
    calendar_times: "\f273";
    calendar_week: "\f784";
    camera: "\f030";
    camera_retro: "\f083";
    campground: "\f6bb";
    candy_cane: "\f786";
    cannabis: "\f55f";
    capsules: "\f46b";
    car: "\f1b9";
    car_alternate: "\f5de";
    car_battery: "\f5df";
    car_crash: "\f5e1";
    car_side: "\f5e4";
    caravan: "\f8ff";
    caret_down: "\f0d7";
    caret_left: "\f0d9";
    caret_right: "\f0da";
    caret_square_down: "\f150";
    caret_square_left: "\f191";
    caret_square_right: "\f152";
    caret_square_up: "\f151";
    caret_up: "\f0d8";
    carrot: "\f787";
    cart_arrow_down: "\f218";
    cart_plus: "\f217";
    cash_register: "\f788";
    cat: "\f6be";
    certificate: "\f0a3";
    chair: "\f6c0";
    chalkboard: "\f51b";
    chalkboard_teacher: "\f51c";
    charging_station: "\f5e7";
    chart_area: "\f1fe";
    chart_bar: "\f080";
    chart_pie: "\f200";
    chartline: "\f201";
    check: "\f00c";
    check_circle: "\f058";
    check_double: "\f560";
    check_square: "\f14a";
    cheese: "\f7ef";
    chess: "\f439";
    chess_bishop: "\f43a";
    chess_board: "\f43c";
    chess_king: "\f43f";
    chess_knight: "\f441";
    chess_pawn: "\f443";
    chess_queen: "\f445";
    chess_rook: "\f447";
    chevron_circle_down: "\f13a";
    chevron_circle_left: "\f137";
    chevron_circle_right: "\f138";
    chevron_circle_up: "\f139";
    chevron_down: "\f078";
    chevron_left: "\f053";
    chevron_right: "\f054";
    chevron_up: "\f077";
    child: "\f1ae";
    church: "\f51d";
    circle: "\f111";
    circle_notch: "\f1ce";
    city: "\f64f";
    clinic_medical: "\f7f2";
    clipboard: "\f328";
    clipboard_check: "\f46c";
    clipboard_list: "\f46d";
    clock: "\f017";
    clone: "\f24d";
    closed_captioning: "\f20a";
    cloud: "\f0c2";
    cloud_download_alternate: "\f381";
    cloud_meatball: "\f73b";
    cloud_moon: "\f6c3";
    cloud_moon_rain: "\f73c";
    cloud_rain: "\f73d";
    cloud_showers_heavy: "\f740";
    cloud_sun: "\f6c4";
    cloud_sun_rain: "\f743";
    cloud_upload_alternate: "\f382";
    cocktail: "\f561";
    code: "\f121";
    code_branch: "\f126";
    coffee: "\f0f4";
    cog: "\f013";
    cogs: "\f085";
    coins: "\f51e";
    columns: "\f0db";
    comment: "\f075";
    comment_alternate: "\f27a";
    comment_dollar: "\f651";
    comment_dots: "\f4ad";
    comment_medical: "\f7f5";
    comment_slash: "\f4b3";
    comments: "\f086";
    comments_dollar: "\f653";
    compact_disc: "\f51f";
    compass: "\f14e";
    compress: "\f066";
    compress_alternate: "\f422";
    compress_arrows_alternate: "\f78c";
    concierge_bell: "\f562";
    cookie: "\f563";
    cookie_bite: "\f564";
    copy: "\f0c5";
    copyright: "\f1f9";
    couch: "\f4b8";
    credit_card: "\f09d";
    crop: "\f125";
    crop_alternate: "\f565";
    cross: "\f654";
    crosshairs: "\f05b";
    crow: "\f520";
    crown: "\f521";
    crutch: "\f7f7";
    cube: "\f1b2";
    cubes: "\f1b3";
    cut: "\f0c4";
    database: "\f1c0";
    deaf: "\f2a4";
    democrat: "\f747";
    desktop: "\f108";
    dharmachakra: "\f655";
    diagnoses: "\f470";
    dice: "\f522";
    dice_d20: "\f6cf";
    dice_d6: "\f6d1";
    dice_five: "\f523";
    dice_four: "\f524";
    dice_one: "\f525";
    dice_six: "\f526";
    dice_three: "\f527";
    dice_two: "\f528";
    digital_tachograph: "\f566";
    directions: "\f5eb";
    disease: "\f7fa";
    divide: "\f529";
    dizzy: "\f567";
    dna: "\f471";
    dog: "\f6d3";
    dollar_sign: "\f155";
    dolly: "\f472";
    dolly_flatbed: "\f474";
    donate: "\f4b9";
    door_closed: "\f52a";
    door_open: "\f52b";
    dot_circle: "\f192";
    dove: "\f4ba";
    download: "\f019";
    drafting_compass: "\f568";
    dragon: "\f6d5";
    draw_polygon: "\f5ee";
    drum: "\f569";
    drum_steelpan: "\f56a";
    drumstick_bite: "\f6d7";
    dumbbell: "\f44b";
    dumpster: "\f793";
    dumpster_fire: "\f794";
    dungeon: "\f6d9";
    edit: "\f044";
    egg: "\f7fb";
    eject: "\f052";
    ellipsis_horizontal: "\f141";
    ellipsis_vertical: "\f142";
    envelope: "\f0e0";
    envelope_open: "\f2b6";
    envelope_open_text: "\f658";
    envelope_square: "\f199";
    equals: "\f52c";
    eraser: "\f12d";
    ethernet: "\f796";
    euro_sign: "\f153";
    exchange_alternate: "\f362";
    exclamation: "\f12a";
    exclamation_circle: "\f06a";
    exclamation_triangle: "\f071";
    expand: "\f065";
    expand_alternate: "\f424";
    expand_arrows_alternate: "\f31e";
    external_alternate: "\f35d";
    external_link_square_alternate: "\f360";
    eye: "\f06e";
    eye_dropper: "\f1fb";
    eye_slash: "\f070";
    fan: "\f863";
    fast_backward: "\f049";
    fast_forward: "\f050";
    faucet: "\e005";
    fax: "\f1ac";
    feather: "\f52d";
    feather_alternate: "\f56b";
    female: "\f182";
    fighter_jet: "\f0fb";
    file: "\f15b";
    file_alternate: "\f15c";
    file_archive: "\f1c6";
    file_audio: "\f1c7";
    file_code: "\f1c9";
    file_contract: "\f56c";
    file_csv: "\f6dd";
    file_download: "\f56d";
    file_excel: "\f1c3";
    file_export: "\f56e";
    file_image: "\f1c5";
    file_import: "\f56f";
    file_invoice: "\f570";
    file_invoice_dollar: "\f571";
    file_medical: "\f477";
    file_medical_alternate: "\f478";
    file_pdf: "\f1c1";
    file_powerpoint: "\f1c4";
    file_prescription: "\f572";
    file_signature: "\f573";
    file_upload: "\f574";
    file_video: "\f1c8";
    file_word: "\f1c2";
    fill: "\f575";
    fill_drip: "\f576";
    film: "\f008";
    filter: "\f0b0";
    fingerprint: "\f577";
    fire: "\f06d";
    fire_alternate: "\f7e4";
    fire_extinguisher: "\f134";
    first_aid: "\f479";
    fish: "\f578";
    fist_raised: "\f6de";
    flag: "\f024";
    flag_checkered: "\f11e";
    flag_usa: "\f74d";
    flask: "\f0c3";
    flushed: "\f579";
    folder: "\f07b";
    folder_minus: "\f65d";
    folder_open: "\f07c";
    folder_plus: "\f65e";
    font: "\f031";
    football_ball: "\f44e";
    forward: "\f04e";
    frog: "\f52e";
    frown: "\f119";
    frown_open: "\f57a";
    fruit-apple: "\f5d1";
    funnel_dollar: "\f662";
    futbol: "\f1e3";
    gamepad: "\f11b";
    gas_pump: "\f52f";
    gavel: "\f0e3";
    gem: "\f3a5";
    genderless: "\f22d";
    ghost: "\f6e2";
    gift: "\f06b";
    gifts: "\f79c";
    glass_cheers: "\f79f";
    glass_martini: "\f000";
    glass_martini_alternate: "\f57b";
    glass_whiskey: "\f7a0";
    glasses: "\f530";
    globe: "\f0ac";
    globe_africa: "\f57c";
    globe_americas: "\f57d";
    globe_asia: "\f57e";
    globe_europe: "\f7a2";
    golf_ball: "\f450";
    gopuram: "\f664";
    graduation_cap: "\f19d";
    greater_than: "\f531";
    greater_than_equal: "\f532";
    grimace: "\f57f";
    grin: "\f580";
    grin_alternate: "\f581";
    grin_beam: "\f582";
    grin_beam_sweat: "\f583";
    grin_hearts: "\f584";
    grin_squint: "\f585";
    grin_squint_tears: "\f586";
    grin_stars: "\f587";
    grin_tears: "\f588";
    grin_tongue: "\f589";
    grin_tongue_squint: "\f58a";
    grin_tongue_wink: "\f58b";
    grin_wink: "\f58c";
    grip_horizontal: "\f58d";
    grip_lines: "\f7a4";
    grip_lines_vertical: "\f7a5";
    grip_vertical: "\f58e";
    guitar: "\f7a6";
    h_square: "\f0fd";
    hamburger: "\f805";
    hammer: "\f6e3";
    hamsa: "\f665";
    hand_holding: "\f4bd";
    hand_holding_heart: "\f4be";
    hand_holding_medical: "\e05c";
    hand_holding_usd: "\f4c0";
    hand_holding_water: "\f4c1";
    hand_lizard: "\f258";
    hand_middle_finger: "\f806";
    hand_paper: "\f256";
    hand_peace: "\f25b";
    hand_point_down: "\f0a7";
    hand_point_left: "\f0a5";
    hand_point_right: "\f0a4";
    hand_point_up: "\f0a6";
    hand_pointer: "\f25a";
    hand_rock: "\f255";
    hand_scissors: "\f257";
    hand_sparkles: "\e05d";
    hand_spock: "\f259";
    hands: "\f4c2";
    hands_helping: "\f4c4";
    hands_wash: "\e05e";
    handshake: "\f2b5";
    handshake_alternate_slash: "\e05f";
    handshake_slash: "\e060";
    hanukiah: "\f6e6";
    hard_hat: "\f807";
    hashtag: "\f292";
    hat_cowboy: "\f8c0";
    hat_cowboy_side: "\f8c1";
    hat_wizard: "\f6e8";
    hdd: "\f0a0";
    head_side_cough: "\e061";
    head_side_cough_slash: "\e062";
    head_side_mask: "\e063";
    head_side_virus: "\e064";
    heading: "\f1dc";
    headphones: "\f025";
    headphones_alternate: "\f58f";
    headset: "\f590";
    heart: "\f004";
    heart_broken: "\f7a9";
    heartbeat: "\f21e";
    helicopter: "\f533";
    highlighter: "\f591";
    hiking: "\f6ec";
    hippo: "\f6ed";
    history: "\f1da";
    hockey_puck: "\f453";
    holly_berry: "\f7aa";
    home: "\f015";
    horse: "\f6f0";
    horse_head: "\f7ab";
    hospital: "\f0f8";
    hospital_alternate: "\f47d";
    hospital_symbol: "\f47e";
    hospital_user: "\f80d";
    hot_tub: "\f593";
    hotdog: "\f80f";
    hotel: "\f594";
    hourglass: "\f254";
    hourglass_end: "\f253";
    hourglass_half: "\f252";
    hourglass_start: "\f251";
    house_damage: "\f6f1";
    house_user: "\e065";
    hryvnia: "\f6f2";
    i_cursor: "\f246";
    ice_cream: "\f810";
    icicles: "\f7ad";
    icons: "\f86d";
    id_badge: "\f2c1";
    id_card: "\f2c2";
    id_card_alternate: "\f47f";
    igloo: "\f7ae";
    image: "\f03e";
    images: "\f302";
    inbox: "\f01c";
    indent: "\f03c";
    industry: "\f275";
    infinity: "\f534";
    info: "\f129";
    info_circle: "\f05a";
    italic: "\f033";
    jedi: "\f669";
    joint: "\f595";
    journal_whills: "\f66a";
    kaaba: "\f66b";
    key: "\f084";
    keyboard: "\f11c";
    khanda: "\f66d";
    kiss: "\f596";
    kiss_beam: "\f597";
    kiss_wink_heart: "\f598";
    kiwi_bird: "\f535";
    landmark: "\f66f";
    language: "\f1ab";
    laptop: "\f109";
    laptop_code: "\f5fc";
    laptop_house: "\e066";
    laptop_medical: "\f812";
    laugh: "\f599";
    laugh_beam: "\f59a";
    laugh_squint: "\f59b";
    laugh_wink: "\f59c";
    layer_group: "\f5fd";
    leaf: "\f06c";
    lemon: "\f094";
    less_than: "\f536";
    less_than_equal: "\f537";
    level_down_alternate: "\f3be";
    level_up_alternate: "\f3bf";
    life_ring: "\f1cd";
    lightbulb: "\f0eb";
    linkify: "\f0c1";
    lira_sign: "\f195";
    list: "\f03a";
    list_alternate: "\f022";
    list_ol: "\f0cb";
    list_ul: "\f0ca";
    location_arrow: "\f124";
    lock: "\f023";
    lock_open: "\f3c1";
    long_arrow_alternate_down: "\f309";
    long_arrow_alternate_left: "\f30a";
    long_arrow_alternate_right: "\f30b";
    long_arrow_alternate_up: "\f30c";
    low_vision: "\f2a8";
    luggage_cart: "\f59d";
    lungs: "\f604";
    lungs_virus: "\e067";
    magic: "\f0d0";
    magnet: "\f076";
    mail_bulk: "\f674";
    male: "\f183";
    map: "\f279";
    map_marked: "\f59f";
    map_marked_alternate: "\f5a0";
    map_marker: "\f041";
    map_marker_alternate: "\f3c5";
    map_pin: "\f276";
    map_signs: "\f277";
    marker: "\f5a1";
    mars: "\f222";
    mars_double: "\f227";
    mars_stroke: "\f229";
    mars_stroke_horizontal: "\f22b";
    mars_stroke_vertical: "\f22a";
    mask: "\f6fa";
    medal: "\f5a2";
    medkit: "\f0fa";
    meh: "\f11a";
    meh_blank: "\f5a4";
    meh_rolling_eyes: "\f5a5";
    memory: "\f538";
    menorah: "\f676";
    mercury: "\f223";
    meteor: "\f753";
    microchip: "\f2db";
    microphone: "\f130";
    microphone_alternate: "\f3c9";
    microphone_alternate_slash: "\f539";
    microphone_slash: "\f131";
    microscope: "\f610";
    minus: "\f068";
    minus_circle: "\f056";
    minus_square: "\f146";
    mitten: "\f7b5";
    mobile: "\f10b";
    mobile_alternate: "\f3cd";
    money_bill: "\f0d6";
    money_bill_alternate: "\f3d1";
    money_bill_wave: "\f53a";
    money_bill_wave_alternate: "\f53b";
    money_check: "\f53c";
    money_check_alternate: "\f53d";
    monument: "\f5a6";
    moon: "\f186";
    mortar_pestle: "\f5a7";
    mosque: "\f678";
    motorcycle: "\f21c";
    mountain: "\f6fc";
    mouse: "\f8cc";
    mouse_pointer: "\f245";
    mug_hot: "\f7b6";
    music: "\f001";
    network_wired: "\f6ff";
    neuter: "\f22c";
    newspaper: "\f1ea";
    not_equal: "\f53e";
    notes_medical: "\f481";
    object_group: "\f247";
    object_ungroup: "\f248";
    oil_can: "\f613";
    om: "\f679";
    otter: "\f700";
    outdent: "\f03b";
    pager: "\f815";
    paint_brush: "\f1fc";
    paint_roller: "\f5aa";
    palette: "\f53f";
    pallet: "\f482";
    paper_plane: "\f1d8";
    paperclip: "\f0c6";
    parachute_box: "\f4cd";
    paragraph: "\f1dd";
    parking: "\f540";
    passport: "\f5ab";
    pastafarianism: "\f67b";
    paste: "\f0ea";
    pause: "\f04c";
    pause_circle: "\f28b";
    paw: "\f1b0";
    peace: "\f67c";
    pen: "\f304";
    pen_alternate: "\f305";
    pen_fancy: "\f5ac";
    pen_nib: "\f5ad";
    pen_square: "\f14b";
    pencil_alternate: "\f303";
    pencil_ruler: "\f5ae";
    people_arrows: "\e068";
    people_carry: "\f4ce";
    pepper_hot: "\f816";
    percent: "\f295";
    percentage: "\f541";
    person_booth: "\f756";
    phone: "\f095";
    phone_alternate: "\f879";
    phone_slash: "\f3dd";
    phone_square: "\f098";
    phone_square_alternate: "\f87b";
    phone_volume: "\f2a0";
    photo_video: "\f87c";
    piggy_bank: "\f4d3";
    pills: "\f484";
    pizza_slice: "\f818";
    place_of_worship: "\f67f";
    plane: "\f072";
    plane_arrival: "\f5af";
    plane_departure: "\f5b0";
    plane_slash: "\e069";
    play: "\f04b";
    play_circle: "\f144";
    plug: "\f1e6";
    plus: "\f067";
    plus_circle: "\f055";
    plus_square: "\f0fe";
    podcast: "\f2ce";
    poll: "\f681";
    poll_horizontal: "\f682";
    poo: "\f2fe";
    poo_storm: "\f75a";
    poop: "\f619";
    portrait: "\f3e0";
    pound_sign: "\f154";
    power_off: "\f011";
    pray: "\f683";
    praying_hands: "\f684";
    prescription: "\f5b1";
    prescription_bottle: "\f485";
    prescription_bottle_alternate: "\f486";
    print: "\f02f";
    procedures: "\f487";
    project_diagram: "\f542";
    pump_medical: "\e06a";
    pump_soap: "\e06b";
    puzzle_piece: "\f12e";
    qrcode: "\f029";
    question: "\f128";
    question_circle: "\f059";
    quidditch: "\f458";
    quote_left: "\f10d";
    quote_right: "\f10e";
    quran: "\f687";
    radiation: "\f7b9";
    radiation_alternate: "\f7ba";
    rainbow: "\f75b";
    random: "\f074";
    receipt: "\f543";
    record_vinyl: "\f8d9";
    recycle: "\f1b8";
    redo: "\f01e";
    redo_alternate: "\f2f9";
    registered: "\f25d";
    remove_format: "\f87d";
    reply: "\f3e5";
    reply_all: "\f122";
    republican: "\f75e";
    restroom: "\f7bd";
    retweet: "\f079";
    ribbon: "\f4d6";
    ring: "\f70b";
    road: "\f018";
    robot: "\f544";
    rocket: "\f135";
    route: "\f4d7";
    rss: "\f09e";
    rss_square: "\f143";
    ruble_sign: "\f158";
    ruler: "\f545";
    ruler_combined: "\f546";
    ruler_horizontal: "\f547";
    ruler_vertical: "\f548";
    running: "\f70c";
    rupee_sign: "\f156";
    sad_cry: "\f5b3";
    sad_tear: "\f5b4";
    satellite: "\f7bf";
    satellite_dish: "\f7c0";
    save: "\f0c7";
    school: "\f549";
    screwdriver: "\f54a";
    scroll: "\f70e";
    sd_card: "\f7c2";
    search: "\f002";
    search_dollar: "\f688";
    search_location: "\f689";
    search_minus: "\f010";
    search_plus: "\f00e";
    seedling: "\f4d8";
    server: "\f233";
    shapes: "\f61f";
    share: "\f064";
    share_alternate: "\f1e0";
    share_alternate_square: "\f1e1";
    share_square: "\f14d";
    shekel_sign: "\f20b";
    shield_alternate: "\f3ed";
    shield_virus: "\e06c";
    ship: "\f21a";
    shipping_fast: "\f48b";
    shoe_prints: "\f54b";
    shopping_bag: "\f290";
    shopping_basket: "\f291";
    shopping_cart: "\f07a";
    shower: "\f2cc";
    shuttle_van: "\f5b6";
    sign: "\f4d9";
    sign_language: "\f2a7";
    signal: "\f012";
    signature: "\f5b7";
    sim_card: "\f7c4";
    sink: "\e06d";
    sitemap: "\f0e8";
    skating: "\f7c5";
    skiing: "\f7c9";
    skiing_nordic: "\f7ca";
    skull: "\f54c";
    skull_crossbones: "\f714";
    slash: "\f715";
    sleigh: "\f7cc";
    sliders_horizontal: "\f1de";
    smile: "\f118";
    smile_beam: "\f5b8";
    smile_wink: "\f4da";
    smog: "\f75f";
    smoking: "\f48d";
    smoking_ban: "\f54d";
    sms: "\f7cd";
    snowboarding: "\f7ce";
    snowflake: "\f2dc";
    snowman: "\f7d0";
    snowplow: "\f7d2";
    soap: "\e06e";
    socks: "\f696";
    solar_panel: "\f5ba";
    sort: "\f0dc";
    sort_alphabet_down: "\f15d";
    sort_alphabet_down_alternate: "\f881";
    sort_alphabet_up: "\f15e";
    sort_alphabet_up_alternate: "\f882";
    sort_amount_down: "\f160";
    sort_amount_down_alternate: "\f884";
    sort_amount_up: "\f161";
    sort_amount_up_alternate: "\f885";
    sort_down: "\f0dd";
    sort_numeric_down: "\f162";
    sort_numeric_down_alternate: "\f886";
    sort_numeric_up: "\f163";
    sort_numeric_up_alternate: "\f887";
    sort_up: "\f0de";
    spa: "\f5bb";
    space_shuttle: "\f197";
    spell_check: "\f891";
    spider: "\f717";
    spinner: "\f110";
    splotch: "\f5bc";
    spray_can: "\f5bd";
    square: "\f0c8";
    square_full: "\f45c";
    square_root_alternate: "\f698";
    stamp: "\f5bf";
    star: "\f005";
    star_and_crescent: "\f699";
    star_half: "\f089";
    star_half_alternate: "\f5c0";
    star_of_david: "\f69a";
    star_of_life: "\f621";
    step_backward: "\f048";
    step_forward: "\f051";
    stethoscope: "\f0f1";
    sticky_note: "\f249";
    stop: "\f04d";
    stop_circle: "\f28d";
    stopwatch: "\f2f2";
    stopwatch_twenty: "\e06f";
    store: "\f54e";
    store_alternate: "\f54f";
    store_alternate_slash: "\e070";
    store_slash: "\e071";
    stream: "\f550";
    street_view: "\f21d";
    strikethrough: "\f0cc";
    stroopwafel: "\f551";
    subscript: "\f12c";
    subway: "\f239";
    suitcase: "\f0f2";
    suitcase_rolling: "\f5c1";
    sun: "\f185";
    superscript: "\f12b";
    surprise: "\f5c2";
    swatchbook: "\f5c3";
    swimmer: "\f5c4";
    swimming_pool: "\f5c5";
    synagogue: "\f69b";
    sync: "\f021";
    sync_alternate: "\f2f1";
    syringe: "\f48e";
    table: "\f0ce";
    table_tennis: "\f45d";
    tablet: "\f10a";
    tablet_alternate: "\f3fa";
    tablets: "\f490";
    tachometer_alternate: "\f3fd";
    tag: "\f02b";
    tags: "\f02c";
    tape: "\f4db";
    tasks: "\f0ae";
    taxi: "\f1ba";
    teeth: "\f62e";
    teeth_open: "\f62f";
    temperature_high: "\f769";
    temperature_low: "\f76b";
    tenge: "\f7d7";
    terminal: "\f120";
    text_height: "\f034";
    text_width: "\f035";
    th: "\f00a";
    th_large: "\f009";
    th_list: "\f00b";
    theater_masks: "\f630";
    thermometer: "\f491";
    thermometer_empty: "\f2cb";
    thermometer_full: "\f2c7";
    thermometer_half: "\f2c9";
    thermometer_quarter: "\f2ca";
    thermometer_three_quarters: "\f2c8";
    thumbs_down: "\f165";
    thumbs_up: "\f164";
    thumbtack: "\f08d";
    ticket_alternate: "\f3ff";
    times: "\f00d";
    times_circle: "\f057";
    tint: "\f043";
    tint_slash: "\f5c7";
    tired: "\f5c8";
    toggle_off: "\f204";
    toggle_on: "\f205";
    toilet: "\f7d8";
    toilet_paper: "\f71e";
    toilet_paper_slash: "\e072";
    toolbox: "\f552";
    tools: "\f7d9";
    tooth: "\f5c9";
    torah: "\f6a0";
    torii_gate: "\f6a1";
    tractor: "\f722";
    trademark: "\f25c";
    traffic_light: "\f637";
    trailer: "\e041";
    train: "\f238";
    tram: "\f7da";
    transgender: "\f224";
    transgender_alternate: "\f225";
    trash: "\f1f8";
    trash_alternate: "\f2ed";
    trash_restore: "\f829";
    trash_restore_alternate: "\f82a";
    tree: "\f1bb";
    trophy: "\f091";
    truck: "\f0d1";
    truck_monster: "\f63b";
    truck_moving: "\f4df";
    truck_packing: "\f4de";
    truck_pickup: "\f63c";
    tshirt: "\f553";
    tty: "\f1e4";
    tv: "\f26c";
    umbrella: "\f0e9";
    umbrella_beach: "\f5ca";
    underline: "\f0cd";
    undo: "\f0e2";
    undo_alternate: "\f2ea";
    universal_access: "\f29a";
    university: "\f19c";
    unlink: "\f127";
    unlock: "\f09c";
    unlock_alternate: "\f13e";
    upload: "\f093";
    user: "\f007";
    user_alternate: "\f406";
    user_alternate_slash: "\f4fa";
    user_astronaut: "\f4fb";
    user_check: "\f4fc";
    user_circle: "\f2bd";
    user_clock: "\f4fd";
    user_cog: "\f4fe";
    user_edit: "\f4ff";
    user_friends: "\f500";
    user_graduate: "\f501";
    user_injured: "\f728";
    user_lock: "\f502";
    user_md: "\f0f0";
    user_minus: "\f503";
    user_ninja: "\f504";
    user_nurse: "\f82f";
    user_plus: "\f234";
    user_secret: "\f21b";
    user_shield: "\f505";
    user_slash: "\f506";
    user_tag: "\f507";
    user_tie: "\f508";
    user_times: "\f235";
    users: "\f0c0";
    users_cog: "\f509";
    users_slash: "\e073";
    utensil_spoon: "\f2e5";
    utensils: "\f2e7";
    vector_square: "\f5cb";
    venus: "\f221";
    venus_double: "\f226";
    venus_mars: "\f228";
    vest: "\e085";
    vest_patches: "\e086";
    vial: "\f492";
    vials: "\f493";
    video: "\f03d";
    video_slash: "\f4e2";
    vihara: "\f6a7";
    virus: "\e074";
    virus_slash: "\e075";
    viruses: "\e076";
    voicemail: "\f897";
    volleyball_ball: "\f45f";
    volume_down: "\f027";
    volume_mute: "\f6a9";
    volume_off: "\f026";
    volume_up: "\f028";
    vote_yea: "\f772";
    vr_cardboard: "\f729";
    walking: "\f554";
    wallet: "\f555";
    warehouse: "\f494";
    water: "\f773";
    wave_square: "\f83e";
    weight: "\f496";
    weight_hanging: "\f5cd";
    wheelchair: "\f193";
    wifi: "\f1eb";
    wind: "\f72e";
    window_close: "\f410";
    window_maximize: "\f2d0";
    window_minimize: "\f2d1";
    window_restore: "\f2d2";
    wine_bottle: "\f72f";
    wine_glass: "\f4e3";
    wine_glass_alternate: "\f5ce";
    won_sign: "\f159";
    wrench: "\f0ad";
    x_ray: "\f497";
    yen_sign: "\f157";
    yin_yang: "\f6ad";
};

@icon-aliases-map: {
    add: "\f067";
    add_circle: "\f055";
    add_square: "\f0fe";
    add_to_calendar: "\f271";
    add_to_cart: "\f217";
    add_user: "\f234";
    alarm: "\f0f3";
    alarm_mute: "\f1f6";
    ald: "\f2a2";
    als: "\f2a2";
    announcement: "\f0a1";
    area_chart: "\f1fe";
    area_graph: "\f1fe";
    arrow_down_cart: "\f218";
    asexual: "\f22d";
    asl: "\f2a3";
    asl_interpreting: "\f2a3";
    assistive_listening_devices: "\f2a2";
    attach: "\f0c6";
    attention: "\f06a";
    balance: "\f24e";
    bar: "\f0fc";
    bathtub: "\f2cd";
    battery_four: "\f240";
    battery_high: "\f241";
    battery_low: "\f243";
    battery_medium: "\f242";
    battery_one: "\f243";
    battery_three: "\f241";
    battery_two: "\f242";
    battery_zero: "\f244";
    birthday: "\f1fd";
    block_layout: "\f009";
    broken_chain: "\f127";
    browser: "\f022";
    call: "\f095";
    call_square: "\f098";
    cancel: "\f00d";
    cart: "\f07a";
    cc: "\f20a";
    chain: "\f0c1";
    chat: "\f075";
    checked_calendar: "\f274";
    checkmark: "\f00c";
    checkmark_box: "\f14a";
    chess_rock: "\f447";
    circle_notched: "\f1ce";
    circle_thin: "\f111";
    close: "\f00d";
    cloud_download: "\f381";
    cloud_upload: "\f382";
    cny: "\f157";
    cocktail: "\f000";
    commenting: "\f27a";
    compose: "\f303";
    computer: "\f108";
    configure: "\f0ad";
    content: "\f0c9";
    conversation: "\f086";
    credit_card_alternative: "\f09d";
    currency: "\f3d1";
    dashboard: "\f3fd";
    deafness: "\f2a4";
    delete: "\f00d";
    delete_calendar: "\f273";
    detective: "\f21b";
    diamond: "\f3a5";
    discussions: "\f086";
    disk: "\f0a0";
    doctor: "\f0f0";
    dollar: "\f155";
    dont: "\f05e";
    drivers_license: "\f2c2";
    dropdown: "\f0d7";
    emergency: "\f0f9";
    erase: "\f12d";
    eur: "\f153";
    euro: "\f153";
    exchange: "\f362";
    external: "\f35d";
    external_share: "\f14d";
    external_square: "\f360";
    eyedropper: "\f1fb";
    factory: "\f275";
    favorite: "\f005";
    feed: "\f09e";
    female_homosexual: "\f226";
    file_text: "\f15c";
    find: "\f1e5";
    first_aid: "\f0fa";
    food: "\f2e7";
    fork: "\f126";
    game: "\f11b";
    gay: "\f227";
    gbp: "\f154";
    grab: "\f255";
    graduation: "\f19d";
    grid_layout: "\f00a";
    group: "\f0c0";
    h: "\f0fd";
    hamburger: "\f0c9";
    hand_victory: "\f25b";
    handicap: "\f193";
    hard_of_hearing: "\f2a4";
    header: "\f1dc";
    heart_empty: "\f004";
    help: "\f128";
    help_circle: "\f059";
    heterosexual: "\f228";
    hide: "\f070";
    hotel: "\f236";
    hourglass_four: "\f254";
    hourglass_full: "\f254";
    hourglass_one: "\f251";
    hourglass_three: "\f253";
    hourglass_two: "\f252";
    hourglass_zero: "\f253";
    idea: "\f0eb";
    ils: "\f20b";
    inr: "\f156";
    intergender: "\f224";
    intersex: "\f224";
    jpy: "\f157";
    krw: "\f159";
    lab: "\f0c3";
    law: "\f24e";
    legal: "\f0e3";
    lesbian: "\f226";
    level_down: "\f3be";
    level_up: "\f3bf";
    lightning: "\f0e7";
    like: "\f004";
    linegraph: "\f201";
    linkify: "\f0c1";
    lira: "\f195";
    list_layout: "\f00b";
    magnify: "\f00e";
    mail: "\f0e0";
    mail_forward: "\f064";
    mail_square: "\f199";
    male_homosexual: "\f227";
    man: "\f222";
    marker: "\f041";
    mars_alternate: "\f229";
    mars_horizontal: "\f22b";
    mars_vertical: "\f22a";
    meanpath: "\f0c8";
    military: "\f0fb";
    money: "\f3d1";
    move: "\f0b2";
    mute: "\f131";
    non_binary_transgender: "\f223";
    numbered_list: "\f0cb";
    options: "\f1de";
    ordered_list: "\f0cb";
    other_gender: "\f229";
    other_gender_horizontal: "\f22b";
    other_gender_vertical: "\f22a";
    payment: "\f09d";
    pencil: "\f303";
    pencil_square: "\f14b";
    photo: "\f030";
    picture: "\f03e";
    pie_chart: "\f200";
    pie_graph: "\f200";
    pin: "\f08d";
    plus_cart: "\f217";
    point: "\f041";
    pointing_down: "\f0a7";
    pointing_left: "\f0a5";
    pointing_right: "\f0a4";
    pointing_up: "\f0a6";
    pound: "\f154";
    power: "\f011";
    power_cord: "\f1e6";
    privacy: "\f084";
    protect: "\f023";
    puzzle: "\f12e";
    r_circle: "\f25d";
    radio: "\f192";
    rain: "\f0e9";
    record: "\f03d";
    refresh: "\f021";
    remove: "\f00d";
    remove_bookmark: "\f02e";
    remove_circle: "\f057";
    remove_from_calendar: "\f272";
    remove_user: "\f235";
    repeat: "\f01e";
    resize_horizontal: "\f337";
    resize_vertical: "\f338";
    rmb: "\f157";
    rouble: "\f158";
    rub: "\f158";
    ruble: "\f158";
    rupee: "\f156";
    s15: "\f2cd";
    selected_radio: "\f192";
    send: "\f1d8";
    setting: "\f013";
    settings: "\f085";
    shekel: "\f20b";
    sheqel: "\f20b";
    shield: "\f3ed";
    shipping: "\f0d1";
    shop: "\f07a";
    shuffle: "\f074";
    shutdown: "\f011";
    sidebar: "\f0c9";
    signing: "\f2a7";
    signup: "\f044";
    sliders: "\f1de";
    soccer: "\f1e3";
    sort_alphabet_ascending: "\f15d";
    sort_alphabet_descending: "\f15e";
    sort_ascending: "\f0de";
    sort_content_ascending: "\f160";
    sort_content_descending: "\f161";
    sort_descending: "\f0dd";
    sort_numeric_ascending: "\f162";
    sort_numeric_descending: "\f163";
    sound: "\f025";
    spoon: "\f2e5";
    spy: "\f21b";
    star_empty: "\f005";
    star_half_empty: "\f089";
    star_half_full: "\f089";
    student: "\f19d";
    talk: "\f27a";
    target: "\f140";
    teletype: "\f1e4";
    television: "\f26c";
    text_cursor: "\f246";
    text_telephone: "\f1e4";
    theme: "\f043";
    thermometer: "\f2c7";
    thumb_tack: "\f08d";
    ticket: "\f3ff";
    time: "\f017";
    times_rectangle: "\f410";
    tm: "\f25c";
    toggle_down: "\f150";
    toggle_left: "\f191";
    toggle_right: "\f152";
    toggle_up: "\f151";
    translate: "\f1ab";
    travel: "\f0b1";
    treatment: "\f0f1";
    triangle_down: "\f0d7";
    triangle_left: "\f0d9";
    triangle_right: "\f0da";
    triangle_up: "\f0d8";
    try: "\f195";
    unhide: "\f06e";
    unlinkify: "\f127";
    unmute: "\f130";
    unordered_list: "\f0ca";
    usd: "\f155";
    user_cancel: "\f235";
    user_close: "\f235";
    user_delete: "\f235";
    user_doctor: "\f0f0";
    user_x: "\f235";
    vcard: "\f2bb";
    video_camera: "\f03d";
    video_play: "\f144";
    volume_control_phone: "\f2a0";
    wait: "\f017";
    warning: "\f12a";
    warning_circle: "\f06a";
    warning_sign: "\f071";
    wi_fi: "\f1eb";
    winner: "\f091";
    wizard: "\f0d0";
    woman: "\f221";
    won: "\f159";
    world: "\f0ac";
    write: "\f303";
    write_square: "\f14b";
    x: "\f00d";
    yen: "\f157";
    zip: "\f187";
    zoom: "\f00e";
};

@icon-outline-map: {
    address_book_outline: "\f2b9";
    address_card_outline: "\f2bb";
    angry_outline: "\f556";
    arrow_alternate_circle_down_outline: "\f358";
    arrow_alternate_circle_left_outline: "\f359";
    arrow_alternate_circle_right_outline: "\f35a";
    arrow_alternate_circle_up_outline: "\f35b";
    bell_outline: "\f0f3";
    bell_slash_outline: "\f1f6";
    bookmark_outline: "\f02e";
    building_outline: "\f1ad";
    calendar_alternate_outline: "\f073";
    calendar_check_outline: "\f274";
    calendar_minus_outline: "\f272";
    calendar_outline: "\f133";
    calendar_plus_outline: "\f271";
    calendar_times_outline: "\f273";
    caret_square_down_outline: "\f150";
    caret_square_left_outline: "\f191";
    caret_square_right_outline: "\f152";
    caret_square_up_outline: "\f151";
    chart_bar_outline: "\f080";
    check_circle_outline: "\f058";
    check_square_outline: "\f14a";
    circle_outline: "\f111";
    clipboard_outline: "\f328";
    clock_outline: "\f017";
    clone_outline: "\f24d";
    closed_captioning_outline: "\f20a";
    comment_alternate_outline: "\f27a";
    comment_dots_outline: "\f4ad";
    comment_outline: "\f075";
    comments_outline: "\f086";
    compass_outline: "\f14e";
    copy_outline: "\f0c5";
    copyright_outline: "\f1f9";
    credit_card_outline: "\f09d";
    dizzy_outline: "\f567";
    dot_circle_outline: "\f192";
    edit_outline: "\f044";
    envelope_open_outline: "\f2b6";
    envelope_outline: "\f0e0";
    eye_outline: "\f06e";
    eye_slash_outline: "\f070";
    file_alternate_outline: "\f15c";
    file_archive_outline: "\f1c6";
    file_audio_outline: "\f1c7";
    file_code_outline: "\f1c9";
    file_excel_outline: "\f1c3";
    file_image_outline: "\f1c5";
    file_outline: "\f15b";
    file_pdf_outline: "\f1c1";
    file_powerpoint_outline: "\f1c4";
    file_video_outline: "\f1c8";
    file_word_outline: "\f1c2";
    flag_outline: "\f024";
    flushed_outline: "\f579";
    folder_open_outline: "\f07c";
    folder_outline: "\f07b";
    frown_open_outline: "\f57a";
    frown_outline: "\f119";
    futbol_outline: "\f1e3";
    gem_outline: "\f3a5";
    grimace_outline: "\f57f";
    grin_alternate_outline: "\f581";
    grin_beam_outline: "\f582";
    grin_beam_sweat_outline: "\f583";
    grin_hearts_outline: "\f584";
    grin_outline: "\f580";
    grin_squint_outline: "\f585";
    grin_squint_tears_outline: "\f586";
    grin_stars_outline: "\f587";
    grin_tears_outline: "\f588";
    grin_tongue_outline: "\f589";
    grin_tongue_squint_outline: "\f58a";
    grin_tongue_wink_outline: "\f58b";
    grin_wink_outline: "\f58c";
    hand_lizard_outline: "\f258";
    hand_paper_outline: "\f256";
    hand_peace_outline: "\f25b";
    hand_point_down_outline: "\f0a7";
    hand_point_left_outline: "\f0a5";
    hand_point_right_outline: "\f0a4";
    hand_point_up_outline: "\f0a6";
    hand_pointer_outline: "\f25a";
    hand_rock_outline: "\f255";
    hand_scissors_outline: "\f257";
    hand_spock_outline: "\f259";
    handshake_outline: "\f2b5";
    hdd_outline: "\f0a0";
    heart_outline: "\f004";
    hospital_outline: "\f0f8";
    hourglass_outline: "\f254";
    id_badge_outline: "\f2c1";
    id_card_outline: "\f2c2";
    image_outline: "\f03e";
    images_outline: "\f302";
    keyboard_outline: "\f11c";
    kiss_beam_outline: "\f597";
    kiss_outline: "\f596";
    kiss_wink_heart_outline: "\f598";
    laugh_beam_outline: "\f59a";
    laugh_outline: "\f599";
    laugh_squint_outline: "\f59b";
    laugh_wink_outline: "\f59c";
    lemon_outline: "\f094";
    life_ring_outline: "\f1cd";
    lightbulb_outline: "\f0eb";
    list_alternate_outline: "\f022";
    map_outline: "\f279";
    meh_blank_outline: "\f5a4";
    meh_outline: "\f11a";
    meh_rolling_eyes_outline: "\f5a5";
    minus_square_outline: "\f146";
    money_bill_alternate_outline: "\f3d1";
    moon_outline: "\f186";
    newspaper_outline: "\f1ea";
    object_group_outline: "\f247";
    object_ungroup_outline: "\f248";
    paper_plane_outline: "\f1d8";
    pause_circle_outline: "\f28b";
    play_circle_outline: "\f144";
    plus_square_outline: "\f0fe";
    question_circle_outline: "\f059";
    registered_outline: "\f25d";
    sad_cry_outline: "\f5b3";
    sad_tear_outline: "\f5b4";
    save_outline: "\f0c7";
    share_square_outline: "\f14d";
    smile_beam_outline: "\f5b8";
    smile_outline: "\f118";
    smile_wink_outline: "\f4da";
    snowflake_outline: "\f2dc";
    square_outline: "\f0c8";
    star_half_outline: "\f089";
    star_outline: "\f005";
    sticky_note_outline: "\f249";
    stop_circle_outline: "\f28d";
    sun_outline: "\f185";
    surprise_outline: "\f5c2";
    thumbs_down_outline: "\f165";
    thumbs_up_outline: "\f164";
    times_circle_outline: "\f057";
    tired_outline: "\f5c8";
    trash_alternate_outline: "\f2ed";
    user_circle_outline: "\f2bd";
    user_outline: "\f007";
    window_close_outline: "\f410";
    window_maximize_outline: "\f2d0";
    window_minimize_outline: "\f2d1";
    window_restore_outline: "\f2d2";
};
@icon-outline-aliases-map: {
};

@icon-thin-map: {
};
@icon-thin-aliases-map: {
};

@icon-brand-map: {
    500px: "\f26e";
    accessible: "\f368";
    accusoft: "\f369";
    acquisitions_incorporated: "\f6af";
    adn: "\f170";
    adversal: "\f36a";
    affiliatetheme: "\f36b";
    airbnb: "\f834";
    algolia: "\f36c";
    alipay: "\f642";
    amazon: "\f270";
    amazon_pay: "\f42c";
    amilia: "\f36d";
    android: "\f17b";
    angellist: "\f209";
    angrycreative: "\f36e";
    angular: "\f420";
    app_store: "\f36f";
    app_store_ios: "\f370";
    apper: "\f371";
    apple: "\f179";
    apple_pay: "\f415";
    artstation: "\f77a";
    asymmetrik: "\f372";
    atlassian: "\f77b";
    audible: "\f373";
    autoprefixer: "\f41c";
    avianex: "\f374";
    aviato: "\f421";
    aws: "\f375";
    bandcamp: "\f2d5";
    battle_net: "\f835";
    behance: "\f1b4";
    behance_square: "\f1b5";
    bimobject: "\f378";
    bitbucket: "\f171";
    bitcoin: "\f379";
    bity: "\f37a";
    black_tie: "\f27e";
    blackberry: "\f37b";
    blogger: "\f37c";
    blogger_b: "\f37d";
    bluetooth: "\f293";
    bluetooth_b: "\f294";
    bootstrap: "\f836";
    btc: "\f15a";
    buffer: "\f837";
    buromobelexperte: "\f37f";
    buy_n_large: "\f8a6";
    buysellads: "\f20d";
    canadian_maple_leaf: "\f785";
    cc_amazon_pay: "\f42d";
    cc_amex: "\f1f3";
    cc_apple_pay: "\f416";
    cc_diners_club: "\f24c";
    cc_discover: "\f1f2";
    cc_jcb: "\f24b";
    cc_mastercard: "\f1f1";
    cc_paypal: "\f1f4";
    cc_stripe: "\f1f5";
    cc_visa: "\f1f0";
    centercode: "\f380";
    centos: "\f789";
    chrome: "\f268";
    chromecast: "\f838";
    cloudflare: "\e07d";
    cloudscale: "\f383";
    cloudsmith: "\f384";
    cloudversify: "\f385";
    codepen: "\f1cb";
    codiepie: "\f284";
    confluence: "\f78d";
    connectdevelop: "\f20e";
    contao: "\f26d";
    cotton_bureau: "\f89e";
    cpanel: "\f388";
    creative_commons: "\f25e";
    creative_commons_by: "\f4e7";
    creative_commons_nc: "\f4e8";
    creative_commons_nc_eu: "\f4e9";
    creative_commons_nc_jp: "\f4ea";
    creative_commons_nd: "\f4eb";
    creative_commons_pd: "\f4ec";
    creative_commons_pd_alternate: "\f4ed";
    creative_commons_remix: "\f4ee";
    creative_commons_sa: "\f4ef";
    creative_commons_sampling: "\f4f0";
    creative_commons_sampling_plus: "\f4f1";
    creative_commons_share: "\f4f2";
    creative_commons_zero: "\f4f3";
    critical_role: "\f6c9";
    css3: "\f13c";
    css3_alternate: "\f38b";
    cuttlefish: "\f38c";
    d_and_d: "\f38d";
    d_and_d_beyond: "\f6ca";
    dailymotion: "\e052";
    dashcube: "\f210";
    deezer: "\e077";
    delicious: "\f1a5";
    deploydog: "\f38e";
    deskpro: "\f38f";
    dev: "\f6cc";
    deviantart: "\f1bd";
    dhl: "\f790";
    diaspora: "\f791";
    digg: "\f1a6";
    digital_ocean: "\f391";
    discord: "\f392";
    discourse: "\f393";
    dochub: "\f394";
    docker: "\f395";
    draft2digital: "\f396";
    dribbble: "\f17d";
    dribbble_square: "\f397";
    dropbox: "\f16b";
    drupal: "\f1a9";
    dyalog: "\f399";
    earlybirds: "\f39a";
    ebay: "\f4f4";
    edge: "\f282";
    edge_legacy: "\e078";
    elementor: "\f430";
    ello: "\f5f1";
    ember: "\f423";
    empire: "\f1d1";
    envira: "\f299";
    erlang: "\f39d";
    ethereum: "\f42e";
    etsy: "\f2d7";
    evernote: "\f839";
    expeditedssl: "\f23e";
    facebook: "\f09a";
    facebook_f: "\f39e";
    facebook_messenger: "\f39f";
    facebook_square: "\f082";
    fantasy_flight_games: "\f6dc";
    fedex: "\f797";
    fedora: "\f798";
    figma: "\f799";
    firefox: "\f269";
    firefox_browser: "\e007";
    first_order: "\f2b0";
    first_order_alternate: "\f50a";
    firstdraft: "\f3a1";
    flickr: "\f16e";
    flipboard: "\f44d";
    fly: "\f417";
    font_awesome: "\f2b4";
    font_awesome_alternate: "\f35c";
    font_awesome_flag: "\f425";
    fonticons: "\f280";
    fonticons_fi: "\f3a2";
    fort_awesome: "\f286";
    fort_awesome_alternate: "\f3a3";
    forumbee: "\f211";
    foursquare: "\f180";
    free_code_camp: "\f2c5";
    freebsd: "\f3a4";
    fulcrum: "\f50b";
    galactic_republic: "\f50c";
    galactic_senate: "\f50d";
    get_pocket: "\f265";
    gg: "\f260";
    gg_circle: "\f261";
    git: "\f1d3";
    git_alternate: "\f841";
    git_square: "\f1d2";
    github: "\f09b";
    github_alternate: "\f113";
    github_square: "\f092";
    gitkraken: "\f3a6";
    gitlab: "\f296";
    gitter: "\f426";
    glide: "\f2a5";
    glide_g: "\f2a6";
    gofore: "\f3a7";
    goodreads: "\f3a8";
    goodreads_g: "\f3a9";
    google: "\f1a0";
    google_drive: "\f3aa";
    google_pay: "\e079";
    google_play: "\f3ab";
    google_plus: "\f2b3";
    google_plus_g: "\f0d5";
    google_plus_square: "\f0d4";
    google_wallet: "\f1ee";
    gratipay: "\f184";
    grav: "\f2d6";
    gripfire: "\f3ac";
    grunt: "\f3ad";
    guilded: "\e07e";
    gulp: "\f3ae";
    hacker_news: "\f1d4";
    hacker_news_square: "\f3af";
    hackerrank: "\f5f7";
    hips: "\f452";
    hire_a_helper: "\f3b0";
    hive: "\e07f";
    hooli: "\f427";
    hornbill: "\f592";
    hotjar: "\f3b1";
    houzz: "\f27c";
    html5: "\f13b";
    hubspot: "\f3b2";
    ideal: "\e013";
    imdb: "\f2d8";
    innosoft: "\e080";
    instagram: "\f16d";
    instagram_square: "\e055";
    instalod: "\e081";
    intercom: "\f7af";
    internet_explorer: "\f26b";
    invision: "\f7b0";
    ioxhost: "\f208";
    itch_io: "\f83a";
    itunes: "\f3b4";
    itunes_note: "\f3b5";
    java: "\f4e4";
    jedi_order: "\f50e";
    jenkins: "\f3b6";
    jira: "\f7b1";
    joget: "\f3b7";
    joomla: "\f1aa";
    js: "\f3b8";
    js_square: "\f3b9";
    jsfiddle: "\f1cc";
    kaggle: "\f5fa";
    keybase: "\f4f5";
    keycdn: "\f3ba";
    kickstarter: "\f3bb";
    kickstarter_k: "\f3bc";
    korvue: "\f42f";
    laravel: "\f3bd";
    lastfm: "\f202";
    lastfm_square: "\f203";
    leanpub: "\f212";
    lesscss: "\f41d";
    linechat: "\f3c0";
    linkedin: "\f08c";
    linode: "\f2b8";
    linux: "\f17c";
    lyft: "\f3c3";
    magento: "\f3c4";
    mailchimp: "\f59e";
    mandalorian: "\f50f";
    markdown: "\f60f";
    mastodon: "\f4f6";
    maxcdn: "\f136";
    mdb: "\f8ca";
    medapps: "\f3c6";
    medium: "\f23a";
    medium_m: "\f3c7";
    medrt: "\f3c8";
    meetup: "\f2e0";
    megaport: "\f5a3";
    mendeley: "\f7b3";
    microblog: "\e01a";
    microsoft: "\f3ca";
    mix: "\f3cb";
    mixcloud: "\f289";
    mixer: "\e056";
    mizuni: "\f3cc";
    modx: "\f285";
    monero: "\f3d0";
    napster: "\f3d2";
    neos: "\f612";
    nimblr: "\f5a8";
    node: "\f419";
    node_js: "\f3d3";
    npm: "\f3d4";
    ns8: "\f3d5";
    nutritionix: "\f3d6";
    octopus_deploy: "\e082";
    odnoklassniki: "\f263";
    odnoklassniki_square: "\f264";
    old_republic: "\f510";
    opencart: "\f23d";
    openid: "\f19b";
    opera: "\f26a";
    optin_monster: "\f23c";
    orcid: "\f8d2";
    osi: "\f41a";
    page4: "\f3d7";
    pagelines: "\f18c";
    palfed: "\f3d8";
    patreon: "\f3d9";
    paypal: "\f1ed";
    penny_arcade: "\f704";
    perbyte: "\e083";
    periscope: "\f3da";
    phabricator: "\f3db";
    phoenix_framework: "\f3dc";
    phoenix_squadron: "\f511";
    php: "\f457";
    pied_piper: "\f2ae";
    pied_piper_alternate: "\f1a8";
    pied_piper_hat: "\f4e5";
    pied_piper_pp: "\f1a7";
    pied_piper_square: "\e01e";
    pinterest: "\f0d2";
    pinterest_p: "\f231";
    pinterest_square: "\f0d3";
    playstation: "\f3df";
    product_hunt: "\f288";
    pushed: "\f3e1";
    python: "\f3e2";
    qq: "\f1d6";
    quinscape: "\f459";
    quora: "\f2c4";
    r_project: "\f4f7";
    raspberry_pi: "\f7bb";
    ravelry: "\f2d9";
    react: "\f41b";
    reacteurope: "\f75d";
    readme: "\f4d5";
    rebel: "\f1d0";
    reddit: "\f1a1";
    reddit_alien: "\f281";
    reddit_square: "\f1a2";
    redhat: "\f7bc";
    redriver: "\f3e3";
    redyeti: "\f69d";
    renren: "\f18b";
    replyd: "\f3e6";
    researchgate: "\f4f8";
    resolving: "\f3e7";
    rev: "\f5b2";
    rocketchat: "\f3e8";
    rockrms: "\f3e9";
    rust: "\e07a";
    safari: "\f267";
    salesforce: "\f83b";
    sass: "\f41e";
    schlix: "\f3ea";
    scribd: "\f28a";
    searchengin: "\f3eb";
    sellcast: "\f2da";
    sellsy: "\f213";
    servicestack: "\f3ec";
    shirtsinbulk: "\f214";
    shopify: "\e057";
    shopware: "\f5b5";
    simplybuilt: "\f215";
    sistrix: "\f3ee";
    sith: "\f512";
    sketch: "\f7c6";
    skyatlas: "\f216";
    skype: "\f17e";
    slack: "\f198";
    slack_hash: "\f3ef";
    slideshare: "\f1e7";
    snapchat: "\f2ab";
    snapchat_ghost: "\f2ac";
    snapchat_square: "\f2ad";
    soundcloud: "\f1be";
    sourcetree: "\f7d3";
    speakap: "\f3f3";
    speaker_deck: "\f83c";
    spotify: "\f1bc";
    squarespace: "\f5be";
    stack_exchange: "\f18d";
    stack_overflow: "\f16c";
    stackpath: "\f842";
    staylinked: "\f3f5";
    steam: "\f1b6";
    steam_square: "\f1b7";
    steam_symbol: "\f3f6";
    sticker_mule: "\f3f7";
    strava: "\f428";
    stripe: "\f429";
    stripe_s: "\f42a";
    studiovinari: "\f3f8";
    stumbleupon: "\f1a4";
    stumbleupon_circle: "\f1a3";
    superpowers: "\f2dd";
    supple: "\f3f9";
    suse: "\f7d6";
    swift: "\f8e1";
    symfony: "\f83d";
    teamspeak: "\f4f9";
    telegram: "\f2c6";
    telegram_plane: "\f3fe";
    tencent_weibo: "\f1d5";
    themeco: "\f5c6";
    themeisle: "\f2b2";
    think_peaks: "\f731";
    tiktok: "\e07b";
    trade_federation: "\f513";
    trello: "\f181";
    tumblr: "\f173";
    tumblr_square: "\f174";
    twitch: "\f1e8";
    twitter: "\f099";
    twitter_square: "\f081";
    typo3: "\f42b";
    uber: "\f402";
    ubuntu: "\f7df";
    uikit: "\f403";
    umbraco: "\f8e8";
    uncharted: "\e084";
    uniregistry: "\f404";
    unity: "\e049";
    unsplash: "\e07c";
    untappd: "\f405";
    ups: "\f7e0";
    usb: "\f287";
    usps: "\f7e1";
    ussunnah: "\f407";
    vaadin: "\f408";
    viacoin: "\f237";
    viadeo: "\f2a9";
    viadeo_square: "\f2aa";
    viber: "\f409";
    vimeo: "\f40a";
    vimeo_square: "\f194";
    vimeo_v: "\f27d";
    vine: "\f1ca";
    vk: "\f189";
    vnv: "\f40b";
    vuejs: "\f41f";
    watchman_monitoring: "\e087";
    waze: "\f83f";
    weebly: "\f5cc";
    weibo: "\f18a";
    weixin: "\f1d7";
    whatsapp: "\f232";
    whatsapp_square: "\f40c";
    whmcs: "\f40d";
    wikipedia_w: "\f266";
    windows: "\f17a";
    wix: "\f5cf";
    wizards_of_the_coast: "\f730";
    wodu: "\e088";
    wolf_pack_battalion: "\f514";
    wordpress: "\f19a";
    wordpress_simple: "\f411";
    wpbeginner: "\f297";
    wpexplorer: "\f2de";
    wpforms: "\f298";
    wpressr: "\f3e4";
    xbox: "\f412";
    xing: "\f168";
    xing_square: "\f169";
    y_combinator: "\f23b";
    yahoo: "\f19e";
    yammer: "\f840";
    yandex: "\f413";
    yandex_international: "\f414";
    yarn: "\f7e3";
    yelp: "\f1e9";
    yoast: "\f2b1";
    youtube: "\f167";
    youtube_square: "\f431";
    zhihu: "\f63f";
};
@icon-brand-aliases-map: {
    american_express: "\f1f3";
    american_express_card: "\f1f3";
    amex: "\f1f3";
    bitbucket_square: "\f171";
    bluetooth_alternative: "\f294";
    credit_card_amazon_pay: "\f42d";
    credit_card_american_express: "\f1f3";
    credit_card_diners_club: "\f24c";
    credit_card_discover: "\f1f2";
    credit_card_jcb: "\f24b";
    credit_card_mastercard: "\f1f1";
    credit_card_paypal: "\f1f4";
    credit_card_stripe: "\f1f5";
    credit_card_visa: "\f1f0";
    diners_club: "\f24c";
    diners_club_card: "\f24c";
    discover: "\f1f2";
    discover_card: "\f1f2";
    disk_outline: "\f369";
    dribble: "\f17d";
    eercast: "\f2da";
    envira_gallery: "\f299";
    fa: "\f2b4";
    facebook_official: "\f082";
    five_hundred_pixels: "\f26e";
    gittip: "\f184";
    google_plus_circle: "\f2b3";
    google_plus_official: "\f2b3";
    japan_credit_bureau: "\f24b";
    japan_credit_bureau_card: "\f24b";
    jcb: "\f24b";
    linkedin_square: "\f08c";
    mastercard: "\f1f1";
    mastercard_card: "\f1f1";
    microsoft_edge: "\f282";
    ms_edge: "\f282";
    new_pied_piper: "\f2ae";
    optinmonster: "\f23c";
    paypal_card: "\f1f4";
    pied_piper_hat: "\f2ae";
    pocket: "\f265";
    stripe_card: "\f1f5";
    theme_isle: "\f2b2";
    visa: "\f1f0";
    visa_card: "\f1f0";
    wechat: "\f1d7";
    wikipedia: "\f266";
    wordpress_beginner: "\f297";
    wordpress_forms: "\f298";
    yc: "\f23b";
    ycombinator: "\f23b";
    youtube_play: "\f167";
};

@icon-duotone-map: {
};
@icon-duotone-secondary-map: {
};
@icon-duotone-aliases-map: {
};
@icon-duotone-secondary-aliases-map: {
};

/* --------------
   Definition
--------------- */

/* Icon Variables */
@opacity: 1;
@width: @iconWidth;
@height: 1em;
@distanceFromText: 0.25rem;
@lineHeight: 1;

/* Variations */
@linkOpacity: 0.8;
@linkDuration: 0.3s;
@loadingDuration: 2s;

@circularSize: 2em;
@circularPadding: 0.5em 0;
@circularShadow: 0 0 0 0.1em rgba(0, 0, 0, 0.1) inset;

@borderedSize: 2em;
@borderedVerticalPadding: ((@borderedSize - @height) / 2);
@borderedHorizontalPadding: 0;
@borderedShadow: 0 0 0 0.1em rgba(0, 0, 0, 0.1) inset;

@coloredBoxShadow: 0 0 0 0.1em currentColor inset;

@cornerIconSize: 0.45em;
@cornerIconStroke: 1px;
@cornerIconShadow:
    -@cornerIconStroke -@cornerIconStroke 0 @white,
    @cornerIconStroke -@cornerIconStroke 0 @white,
    -@cornerIconStroke @cornerIconStroke 0 @white,
    @cornerIconStroke @cornerIconStroke 0 @white;
@cornerIconInvertedShadow:
    -@cornerIconStroke -@cornerIconStroke 0 @black,
    @cornerIconStroke -@cornerIconStroke 0 @black,
    -@cornerIconStroke @cornerIconStroke 0 @black,
    @cornerIconStroke @cornerIconStroke 0 @black;

@cornerOffset: 0;
@borderedGroupCornerOffset: 1.15em;

@mini: 0.4em;
@tiny: 0.5em;
@small: 0.75em;
@medium: 1em;
@large: 1.5em;
@big: 2em;
@huge: 4em;
@massive: 8em;

/* Duotone specifics */
@duotonePrimaryColor: inherit;
@duotonePrimaryOpacity: 1;
@duotoneSecondaryColor: inherit;
@duotoneSecondaryOpacity: 0.4;
