/*******************************
            Message
*******************************/

// @textColor

/* -------------------
       Elements
-------------------- */

@verticalMargin: 1em;
@verticalPadding: 1em;
@horizontalPadding: 1.5em;
@padding: @verticalPadding @horizontalPadding;
@background: #f8f8f9;
@lineHeightOffset: ((@lineHeight - 1em) / 2);

@borderRadius: @defaultBorderRadius;
@borderWidth: 1px;
@borderShadow: 0 0 0 @borderWidth @strongBorderColor inset;
@shadowShadow: 0 0 0 0 rgba(0, 0, 0, 0);
@boxShadow: @borderShadow, @shadowShadow;

@transition:
    opacity @defaultDuration @defaultEasing,
    color @defaultDuration @defaultEasing,
    background @defaultDuration @defaultEasing,
    box-shadow @defaultDuration @defaultEasing;

/* Header */
@headerFontSize: @relativeLarge;
@headerFontWeight: @bold;
@headerDisplay: block;
@headerDistance: 0;
@headerMargin: -@headerLineHeightOffset 0 @headerDistance 0;
@headerParagraphDistance: 0.25em;

/* Paragraph */
@messageTextOpacity: 0.85;
@messageParagraphMargin: 0.75em;

/* List */
@listOpacity: 0.85;
@listStylePosition: inside;
@listMargin: 0.5em;
@listItemIndent: 1em;
@listItemMargin: 0.3em;

/* Icon */
@iconDistance: 0.6em;

/* Close Icon */
@closeTopDistance: @verticalPadding - @lineHeightOffset;
@closeRightDistance: 0.5em;
@closeOpacity: 0.7;
@closeTransition: opacity @defaultDuration @defaultEasing;

/* -------------------
        Types
-------------------- */

/* Icon Message */
@iconSize: 3em;
@iconOpacity: 0.8;
@iconContentDistance: 0;
@iconVerticalAlign: middle;

/* Attached */
@attachedXOffset: -1px;
@attachedYOffset: -1px;
@attachedBoxShadow: 0 0 0 @borderWidth @borderColor inset;
@attachedBottomBoxShadow: @attachedBoxShadow, @subtleShadow;

/* Floating */
@floatingBoxShadow: @borderShadow, @floatingShadow;

/* Colors */
@redBoxShadow:
    0 0 0 @borderWidth @redBorderColor inset,
    @shadowShadow;
@redBoxFloatingShadow:
    0 0 0 @borderWidth @redBorderColor inset,
    @floatingShadow;
@orangeBoxShadow:
    0 0 0 @borderWidth @orangeBorderColor inset,
    @shadowShadow;
@orangeBoxFloatingShadow:
    0 0 0 @borderWidth @orangeBorderColor inset,
    @floatingShadow;
@yellowBoxShadow:
    0 0 0 @borderWidth @yellowBorderColor inset,
    @shadowShadow;
@yellowBoxFloatingShadow:
    0 0 0 @borderWidth @yellowBorderColor inset,
    @floatingShadow;
@oliveBoxShadow:
    0 0 0 @borderWidth @oliveBorderColor inset,
    @shadowShadow;
@oliveBoxFloatingShadow:
    0 0 0 @borderWidth @oliveBorderColor inset,
    @floatingShadow;
@greenBoxShadow:
    0 0 0 @borderWidth @greenBorderColor inset,
    @shadowShadow;
@greenBoxFloatingShadow:
    0 0 0 @borderWidth @greenBorderColor inset,
    @floatingShadow;
@tealBoxShadow:
    0 0 0 @borderWidth @tealBorderColor inset,
    @shadowShadow;
@tealBoxFloatingShadow:
    0 0 0 @borderWidth @tealBorderColor inset,
    @floatingShadow;
@blueBoxShadow:
    0 0 0 @borderWidth @blueBorderColor inset,
    @shadowShadow;
@blueBoxFloatingShadow:
    0 0 0 @borderWidth @blueBorderColor inset,
    @floatingShadow;
@violetBoxShadow:
    0 0 0 @borderWidth @violetBorderColor inset,
    @shadowShadow;
@violetBoxFloatingShadow:
    0 0 0 @borderWidth @violetBorderColor inset,
    @floatingShadow;
@purpleBoxShadow:
    0 0 0 @borderWidth @purpleBorderColor inset,
    @shadowShadow;
@purpleBoxFloatingShadow:
    0 0 0 @borderWidth @purpleBorderColor inset,
    @floatingShadow;
@pinkBoxShadow:
    0 0 0 @borderWidth @pinkBorderColor inset,
    @shadowShadow;
@pinkBoxFloatingShadow:
    0 0 0 @borderWidth @pinkBorderColor inset,
    @floatingShadow;
@brownBoxShadow:
    0 0 0 @borderWidth @brownBorderColor inset,
    @shadowShadow;
@brownBoxFloatingShadow:
    0 0 0 @borderWidth @brownBorderColor inset,
    @floatingShadow;

/* Warning / Positive / Negative / Info */
@positiveBoxShadow:
    0 0 0 @borderWidth @positiveBorderColor inset,
    @shadowShadow;
@positiveBoxFloatingShadow:
    0 0 0 @borderWidth @positiveBorderColor inset,
    @floatingShadow;
@negativeBoxShadow:
    0 0 0 @borderWidth @negativeBorderColor inset,
    @shadowShadow;
@negativeBoxFloatingShadow:
    0 0 0 @borderWidth @negativeBorderColor inset,
    @floatingShadow;
@infoBoxShadow:
    0 0 0 @borderWidth @infoBorderColor inset,
    @shadowShadow;
@infoBoxFloatingShadow:
    0 0 0 @borderWidth @infoBorderColor inset,
    @floatingShadow;
@warningBoxShadow:
    0 0 0 @borderWidth @warningBorderColor inset,
    @shadowShadow;
@warningBoxFloatingShadow:
    0 0 0 @borderWidth @warningBorderColor inset,
    @floatingShadow;
@errorBoxShadow:
    0 0 0 @borderWidth @errorBorderColor inset,
    @shadowShadow;
@errorBoxFloatingShadow:
    0 0 0 @borderWidth @errorBorderColor inset,
    @floatingShadow;
@successBoxShadow:
    0 0 0 @borderWidth @successBorderColor inset,
    @shadowShadow;
@successBoxFloatingShadow:
    0 0 0 @borderWidth @successBorderColor inset,
    @floatingShadow;

@miniMessageSize: @relativeMini;
@tinyMessageSize: @relativeTiny;
@smallMessageSize: @relativeSmall;
@largeMessageSize: @relativeLarge;
@bigMessageSize: @relativeBig;
@hugeMessageSize: @relativeHuge;
@massiveMessageSize: @relativeMassive;
