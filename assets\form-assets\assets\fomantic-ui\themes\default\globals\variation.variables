/***********************************************************
  Central element variation compilation enablers
***********************************************************/

/* General */
@variationAllSizes: mini, tiny, small, large, big, huge, massive;
@variationAllColors: primary, secondary, red, orange, yellow, olive, green, teal, blue, violet, purple, pink, brown, grey, black;
@variationAllStates: error, info, success, warning;
@variationAllConsequences: positive, negative, error, info, success, warning;

/*******************************
           Elements
*******************************/

/* Button */
@variationButtonDisabled: true;
@variationButtonAnimated: true;
@variationButtonAnimatedFade: true;
@variationButtonInverted: true;
@variationButtonSocial: true;
@variationButtonFloated: true;
@variationButtonCompact: true;
@variationButtonIcon: true;
@variationButtonLoading: true;
@variationButtonBasic: true;
@variationButtonTertiary: true;
@variationButtonLabeled: true;
@variationButtonLabeledIcon: true;
@variationButtonToggle: true;
@variationButtonOr: true;
@variationButtonAttached: true;
@variationButtonFluid: true;
@variationButtonEqualWidth: true;
@variationButtonVertical: true;
@variationButtonCircular: true;
@variationButtonGroups: true;
@variationButtonStackable: true;
@variationButtonWrapping: true;
@variationButtonWrapped: true;
@variationButtonSpaced: true;
@variationButtonConsequences: positive, negative;
@variationButtonSizes: @variationAllSizes;
@variationButtonColors: @variationAllColors;

/* Container */
@variationContainerGrid: true;
@variationContainerRelaxed: true;
@variationContainerVeryRelaxed: true;
@variationContainerText: true;
@variationContainerWide: true;
@variationContainerFluid: true;
@variationContainerAligned: true;
@variationContainerJustified: true;
@variationContainerResizable: true;
@variationContainerScrolling: true;
@variationContainerScrollingShort: true;
@variationContainerScrollingVeryShort: true;
@variationContainerScrollingLong: true;
@variationContainerScrollingVeryLong: true;

/* Divider */
@variationDividerInverted: true;
@variationDividerHorizontal: true;
@variationDividerVertical: true;
@variationDividerIcon: true;
@variationDividerHidden: true;
@variationDividerFitted: true;
@variationDividerClearing: true;
@variationDividerSection: true;
@variationDividerSizes: @variationAllSizes;
@variationDividerColors: @variationAllColors;

/* Header */
@variationHeaderDisabled: true;
@variationHeaderInverted: true;
@variationHeaderSub: true;
@variationHeaderIcon: true;
@variationHeaderAligned: true;
@variationHeaderJustified: true;
@variationHeaderFloated: true;
@variationHeaderFitted: true;
@variationHeaderDividing: true;
@variationHeaderBlock: true;
@variationHeaderAttached: true;
@variationHeaderSeamless: true;
@variationHeaderTags: h1, h2, h3, h4, h5, h6;
@variationHeaderSizes: @variationAllSizes;
@variationHeaderColors: @variationAllColors;

/* Icon */
@variationIconDeprecated: true;
@variationIconSolid: true;
@variationIconAliases: true;
@variationIconOutline: true;
@variationIconBrand: true;
@variationIconThin: false; /* Font Awesome Pro only */
@variationIconDuotone: false; /* Font Awesome Pro only */
@variationIconDisabled: true;
@variationIconInverted: true;
@variationIconLoading: true;
@variationIconFitted: true;
@variationIconLink: true;
@variationIconCircular: true;
@variationIconBordered: true;
@variationIconColored: true;
@variationIconRotated: true;
@variationIconFlipped: true;
@variationIconCorner: true;
@variationIconGroups: true;
@variationIconSizes: @variationAllSizes;
@variationIconColors: @variationAllColors;

/* Image */
@variationImageDisabled: true;
@variationImageCircular: true;
@variationImageBordered: true;
@variationImageRounded: true;
@variationImageInline: true;
@variationImageAligned: true;
@variationImageFluid: true;
@variationImageAvatar: true;
@variationImageFloated: true;
@variationImageSpaced: true;
@variationImageCentered: true;
@variationImageGroups: true;
@variationImageSizes: @variationAllSizes;

/* Input */
@variationInputDisabled: true;
@variationInputInverted: true;
@variationInputStates: @variationAllStates;
@variationInputInvalid: false;
@variationInputTransparent: true;
@variationInputCorner: true;
@variationInputLoading: true;
@variationInputIcon: true;
@variationInputLabeled: true;
@variationInputAction: true;
@variationInputFluid: true;
@variationInputFile: true;
@variationInputColors: @variationAllColors;
@variationInputSizes: @variationAllSizes;

/* Label */
@variationLabelDisabled: true;
@variationLabelInverted: true;
@variationLabelImage: true;
@variationLabelTag: true;
@variationLabelCorner: true;
@variationLabelRibbon: true;
@variationLabelCircular: true;
@variationLabelPointing: true;
@variationLabelFloating: true;
@variationLabelBasic: true;
@variationLabelAttached: true;
@variationLabelFluid: true;
@variationLabelCentered: true;
@variationLabelSizes: @variationAllSizes;
@variationLabelColors: @variationAllColors;

/* List */
@variationListInverted: true;
@variationListDisabled: true;
@variationListBulleted: true;
@variationListOrdered: true;
@variationListIcon: true;
@variationListImage: true;
@variationListFloated: true;
@variationListLink: true;
@variationListAligned: true;
@variationListFitted: true;
@variationListAnimated: true;
@variationListHorizontal: true;
@variationListSuffixed: true;
@variationListSelection: true;
@variationListDivided: true;
@variationListCelled: true;
@variationListRelaxed: true;
@variationListVeryRelaxed: true;
@variationListHeader: true;
@variationListDescription: true;
@variationListSizes: @variationAllSizes;

/* Loader */
@variationLoaderSpeeds: true;
@variationLoaderIndeterminate: true;
@variationLoaderText: true;
@variationLoaderInline: true;
@variationLoaderElastic: true;
@variationLoaderSizes: @variationAllSizes;
@variationLoaderColors: @variationAllColors;

/* Placeholder */
@variationPlaceholderInverted: true;
@variationPlaceholderImage: true;
@variationPlaceholderLine: true;
@variationPlaceholderHeader: true;
@variationPlaceholderFluid: true;
@variationPlaceholderLengths: true;

/* Rail */
@variationRailInternal: true;
@variationRailDividing: true;
@variationRailDistance: true;
@variationRailAttached: true;
@variationRailSizes: @variationAllSizes;

/* Reveal */
@variationRevealDisabled: true;
@variationRevealSlide: true;
@variationRevealFade: true;
@variationRevealMove: true;
@variationRevealRotate: true;
@variationRevealSizes: @variationAllSizes;

/* Segment */
@variationSegmentInverted: true;
@variationSegmentDisabled: true;
@variationSegmentVertical: true;
@variationSegmentPlaceholder: true;
@variationSegmentHorizontal: true;
@variationSegmentPiled: true;
@variationSegmentStacked: true;
@variationSegmentPadded: true;
@variationSegmentVeryPadded: true;
@variationSegmentCircular: true;
@variationSegmentCompact: true;
@variationSegmentRaised: true;
@variationSegmentGroups: true;
@variationSegmentBasic: true;
@variationSegmentClearing: true;
@variationSegmentLoading: true;
@variationSegmentFloating: true;
@variationSegmentAligned: true;
@variationSegmentSecondary: true;
@variationSegmentTertiary: true;
@variationSegmentAttached: true;
@variationSegmentSeamless: true;
@variationSegmentFitted: true;
@variationSegmentResizable: true;
@variationSegmentScrolling: true;
@variationSegmentScrollingShort: true;
@variationSegmentScrollingVeryShort: true;
@variationSegmentScrollingLong: true;
@variationSegmentScrollingVeryLong: true;
@variationSegmentSizes: @variationAllSizes;
@variationSegmentColors: @variationAllColors;

/* Step */
@variationStepInverted: true;
@variationStepDisabled: true;
@variationStepStackable: true;
@variationStepVertical: true;
@variationStepOrdered: true;
@variationStepFluid: true;
@variationStepAttached: true;
@variationStepEqualWidth: true;
@variationStepSizes: @variationAllSizes;

/* Text */
@variationTextInverted: true;
@variationTextDisabled: true;
@variationTextStates: @variationAllStates;
@variationTextSizes: @variationAllSizes;
@variationTextColors: @variationAllColors;

/*******************************
           Collections
*******************************/

/* Breadcrumb */
@variationBreadcrumbInverted: true;
@variationBreadcrumbSizes: @variationAllSizes;

/* Form */
@variationFormInverted: true;
@variationFormDisabled: true;
@variationFormTransparent: true;
@variationFormLoading: true;
@variationFormStates: @variationAllStates;
@variationFormInvalid: true;
@variationFormRequired: true;
@variationFormInline: true;
@variationFormGrouped: true;
@variationFormEqualWidth: true;
@variationFormWide: true;
@variationFormSizes: @variationAllSizes;

/* Grid */
@variationGridInverted: true;
@variationGridPage: true;
@variationGridCelled: true;
@variationGridCentered: true;
@variationGridRelaxed: true;
@variationGridVeryRelaxed: true;
@variationGridPadded: true;
@variationGridFloated: true;
@variationGridDivided: true;
@variationGridVertical: true;
@variationGridAligned: true;
@variationGridAttached: true;
@variationGridStretched: true;
@variationGridJustified: true;
@variationGridReversed: true;
@variationGridDoubling: true;
@variationGridStackable: true;
@variationGridCompact: true;
@variationGridVeryCompact: true;
@variationGridWide: true;
@variationGridEqualWidth: true;
@variationGridColors: @variationAllColors;

/* Menu */
@variationMenuInverted: true;
@variationMenuSecondary: true;
@variationMenuPointing: true;
@variationMenuVertical: true;
@variationMenuTabular: true;
@variationMenuPagination: true;
@variationMenuText: true;
@variationMenuFluid: true;
@variationMenuLabeled: true;
@variationMenuStackable: true;
@variationMenuFloated: true;
@variationMenuCentered: true;
@variationMenuFitted: true;
@variationMenuBorderless: true;
@variationMenuCompact: true;
@variationMenuFixed: true;
@variationMenuAttached: true;
@variationMenuIcon: true;
@variationMenuEqualWidth: true;
@variationMenuWrapping: true;
@variationMenuWrapped: true;
@variationMenuSizes: @variationAllSizes;
@variationMenuColors: @variationAllColors;

/* Message */
@variationMessageInverted: true;
@variationMessageCompact: true;
@variationMessageAttached: true;
@variationMessageIcon: true;
@variationMessageFloating: true;
@variationMessageConsequences: @variationAllConsequences;
@variationMessageCentered: true;
@variationMessageRightAligned: true;
@variationMessageSizes: @variationAllSizes;
@variationMessageColors: @variationAllColors;

/* Table */
@variationTableInverted: true;
@variationTableDisabled: true;
@variationTableDefinition: true;
@variationTableStructured: true;
@variationTablePositive: true;
@variationTableNegative: true;
@variationTableError: true;
@variationTableWarning: true;
@variationTableActive: true;
@variationTableStackable: true;
@variationTableAligned: true;
@variationTableFixed: true;
@variationTableSelectable: true;
@variationTableAttached: true;
@variationTableStriped: true;
@variationTableSortable: true;
@variationTableCollapsing: true;
@variationTableBasic: true;
@variationTableVeryBasic: true;
@variationTableCelled: true;
@variationTablePadded: true;
@variationTableVeryPadded: true;
@variationTableCompact: true;
@variationTableVeryCompact: true;
@variationTableMarked: true;
@variationTableEqualWidth: true;
@variationTableWide: true;
@variationTableResizable: true;
@variationTableScrolling: true;
@variationTableScrollingShort: true;
@variationTableScrollingVeryShort: true;
@variationTableScrollingLong: true;
@variationTableScrollingVeryLong: true;
@variationTableStuck: true;
@variationTableStuckHead: true;
@variationTableStuckFoot: true;
@variationTableStuckFirst: true;
@variationTableStuckLast: true;
@variationTableOverflowing: true;
@variationTableOverflowingShort: true;
@variationTableOverflowingVeryShort: true;
@variationTableOverflowingLong: true;
@variationTableOverflowingVeryLong: true;
@variationTableSizes: @variationAllSizes;
@variationTableColors: @variationAllColors;

/*******************************
             Views
*******************************/

/* Ad */
@variationAdLeaderboard: true;
@variationAdBillboard: true;
@variationAdPanorama: true;
@variationAdNetboard: true;
@variationAdRectangle: true;
@variationAdSquare: true;
@variationAdButton: true;
@variationAdSkyscraper: true;
@variationAdBanner: true;
@variationAdMobile: true;
@variationAdCentered: true;
@variationAdTest: true;

/* Card */
@variationCardInverted: true;
@variationCardBasic: true;
@variationCardDisabled: true;
@variationCardLoading: true;
@variationCardHorizontal: true;
@variationCardRaised: true;
@variationCardCentered: true;
@variationCardFluid: true;
@variationCardLink: true;
@variationCardDoubling: true;
@variationCardStackable: true;
@variationCardFloated: true;
@variationCardAligned: true;
@variationCardImage: true;
@variationCardHeader: true;
@variationCardDescription: true;
@variationCardMeta: true;
@variationCardExtra: true;
@variationCardButton: true;
@variationCardStar: true;
@variationCardLike: true;
@variationCardEqualWidth: true;
@variationCardSizes: @variationAllSizes;
@variationCardColors: @variationAllColors;

/* Comment */
@variationCommentInverted: true;
@variationCommentDisabled: true;
@variationCommentThreaded: true;
@variationCommentMinimal: true;
@variationCommentAvatar: true;
@variationCommentAuthor: true;
@variationCommentMeta: true;
@variationCommentActions: true;
@variationCommentReply: true;
@variationCommentSizes: @variationAllSizes;
@variationCommentColors: @variationAllColors;

/* Feed */
@variationFeedInverted: true;
@variationFeedDisabled: true;
@variationFeedMeta: true;
@variationFeedSummary: true;
@variationFeedUser: true;
@variationFeedExtra: true;
@variationFeedDate: true;
@variationFeedLike: true;
@variationFeedLabel: true;
@variationFeedLabelIcon: true;
@variationFeedLabelImage: true;
@variationFeedLabelText: true;
@variationFeedLabelUiLabel: true;
@variationFeedConnected: true;
@variationFeedDivided: true;
@variationFeedOrdered: true;
@variationFeedBasic: true;
@variationFeedRightFloated: true;
@variationFeedSizes: @variationAllSizes;
@variationFeedColors: @variationAllColors;

/* Item */
@variationItemInverted: true;
@variationItemDisabled: true;
@variationItemImage: true;
@variationItemHeader: true;
@variationItemDescription: true;
@variationItemMeta: true;
@variationItemExtra: true;
@variationItemFavorite: true;
@variationItemLike: true;
@variationItemFloated: true;
@variationItemAligned: true;
@variationItemRelaxed: true;
@variationItemVeryRelaxed: true;
@variationItemDivided: true;
@variationItemLink: true;
@variationItemUnstackable: true;
@variationItemSizes: @variationAllSizes;

/* Statistic */
@variationStatisticInverted: true;
@variationStatisticStackable: true;
@variationStatisticFloated: true;
@variationStatisticHorizontal: true;
@variationStatisticEqualWidth: true;
@variationStatisticFluid: true;
@variationStatisticSizes: @variationAllSizes;
@variationStatisticColors: @variationAllColors;

/*******************************
            Modules
*******************************/

/* Accordion */
@variationAccordionInverted: true;
@variationAccordionStyled: true;
@variationAccordionBasicStyled: true;
@variationAccordionFluid: true;
@variationAccordionCompact: true;
@variationAccordionVeryCompact: true;
@variationAccordionRightDropdown: true;
@variationAccordionTree: true;

/* Calendar */
@variationCalendarInverted: true;
@variationCalendarDisabled: true;
@variationCalendarMultiMonth: true;
@variationCalendarSizes: @variationAllSizes;

/* Checkbox */
@variationCheckboxDisabled: true;
@variationCheckboxReadonly: true;
@variationCheckboxInverted: true;
@variationCheckboxInvisible: true;
@variationCheckboxRadio: true;
@variationCheckboxSlider: true;
@variationCheckboxToggle: true;
@variationCheckboxIndeterminate: true;
@variationCheckboxFitted: true;
@variationCheckboxRightAligned: true;
@variationCheckboxSizes: @variationAllSizes;
@variationCheckboxColors: @variationAllColors;

/* Dimmer */
@variationDimmerInverted: true;
@variationDimmerDisabled: true;
@variationDimmerLegacy: true;
@variationDimmerAligned: true;
@variationDimmerPage: true;
@variationDimmerBlurring: true;
@variationDimmerShades: true;
@variationDimmerSimple: true;
@variationDimmerPartially: true;

/* Dropdown */
@variationDropdownInverted: true;
@variationDropdownDisabled: true;
@variationDropdownReadonly: true;
@variationDropdownLabel: true;
@variationDropdownButton: true;
@variationDropdownSelection: true;
@variationDropdownShort: true;
@variationDropdownLong: true;
@variationDropdownCompact: true;
@variationDropdownSearch: true;
@variationDropdownMultiple: true;
@variationDropdownInline: true;
@variationDropdownLoading: true;
@variationDropdownStates: @variationAllStates;
@variationDropdownClear: true;
@variationDropdownLeft: true;
@variationDropdownUpward: true;
@variationDropdownSimple: true;
@variationDropdownResizable: true;
@variationDropdownScrolling: true;
@variationDropdownFluid: true;
@variationDropdownFloating: true;
@variationDropdownPointing: true;
@variationDropdownColumnar: true;
@variationDropdownScrollhint: true;
@variationDropdownHighlightMatches: false;
@variationDropdownSizes: @variationAllSizes;

/* Embed */
@variationEmbedRatio: true;

/* Flyout */
@variationFlyoutInverted: true;
@variationFlyoutBasic: true;
@variationFlyoutFullscreen: true;
@variationFlyoutCentered: true;
@variationFlyoutActions: true;
@variationFlyoutLeftActions: true;
@variationFlyoutBlurring: true;
@variationFlyoutColumnWidth: true;
@variationFlyoutThin: true;
@variationFlyoutWide: true;
@variationFlyoutTop: true;
@variationFlyoutBottom: true;
@variationFlyoutLeft: true;
@variationFlyoutRight: true;
@variationFlyoutOverlay: true;
@variationFlyoutScrolling: true;

/* Modal */
@variationModalInverted: true;
@variationModalBasic: true;
@variationModalFullscreen: true;
@variationModalOverlay: true;
@variationModalAligned: true;
@variationModalResizable: true;
@variationModalScrolling: true;
@variationModalLegacy: true;
@variationModalCloseInside: true;
@variationModalCentered: true;
@variationModalActions: true;
@variationModalLeftActions: true;
@variationModalSizes: @variationAllSizes;

/* Nag */
@variationNagInverted: true;
@variationNagBottom: true;
@variationNagOverlay: true;
@variationNagFixed: true;
@variationNagGroups: true;
@variationNagSizes: @variationAllSizes;
@variationNagColors: @variationAllColors;

/* Popup */
@variationPopupInverted: true;
@variationPopupTooltip: true;
@variationPopupPosition: true;
@variationPopupTop: true;
@variationPopupBottom: true;
@variationPopupLeft: true;
@variationPopupRight: true;
@variationPopupCenter: true;
@variationPopupLoading: true;
@variationPopupBasic: true;
@variationPopupWide: true;
@variationPopupFluid: true;
@variationPopupFlowing: true;
@variationPopupFixed: true;
@variationPopupMultiline: true;
@variationPopupSizes: @variationAllSizes;
@variationPopupColors: @variationAllColors;

/* Progress */
@variationProgressInverted: true;
@variationProgressDisabled: true;
@variationProgressBasic: true;
@variationProgressIndicating: true;
@variationProgressIndeterminate: true;
@variationProgressSliding: true;
@variationProgressFilling: true;
@variationProgressSwinging: true;
@variationProgressMultiple: true;
@variationProgressSuccess: true;
@variationProgressWarning: true;
@variationProgressError: true;
@variationProgressActive: true;
@variationProgressAttached: true;
@variationProgressSpeeds: true;
@variationProgressRightAligned: true;
@variationProgressSizes: @variationAllSizes;
@variationProgressColors: @variationAllColors;

/* Rating */
@variationRatingDisabled: true;
@variationRatingPartial: true;
@variationRatingSizes: @variationAllSizes;
@variationRatingColors: @variationAllColors;

/* Search */
@variationSearchDisabled: true;
@variationSearchSelection: true;
@variationSearchCategory: true;
@variationSearchHorizontalCategory: true;
@variationSearchLoading: true;
@variationSearchAligned: true;
@variationSearchFluid: true;
@variationSearchShort: true;
@variationSearchVeryShort: true;
@variationSearchLong: true;
@variationSearchVeryLong: true;
@variationSearchResizable: true;
@variationSearchScrolling: true;
@variationSearchHighlightMatches: false;
@variationSearchSizes: @variationAllSizes;

/* Shape */
@variationShapeCube: true;
@variationShapeText: true;
@variationShapeLoading: true;

/* Sidebar */
@variationSidebarThin: true;
@variationSidebarVeryThin: true;
@variationSidebarWide: true;
@variationSidebarVeryWide: true;
@variationSidebarTop: true;
@variationSidebarBottom: true;
@variationSidebarLeft: true;
@variationSidebarRight: true;
@variationSidebarOverlay: true;
@variationSidebarSlideAlong: true;
@variationSidebarSlideOut: true;
@variationSidebarScale: true;
@variationSidebarPush: true;
@variationSidebarUncover: true;
@variationSidebarBlurring: true;

/* Slider */
@variationSliderInverted: true;
@variationSliderDisabled: true;
@variationSliderReversed: true;
@variationSliderLabeled: true;
@variationSliderTicked: true;
@variationSliderVertical: true;
@variationSliderBasic: true;
@variationSliderSizes: small, large, big;
@variationSliderColors: @variationAllColors;

/* Tab */
@variationTabLoading: true;

/* Toast */
@variationToastInverted: true;
@variationToastTop: true;
@variationToastBottom: true;
@variationToastLeft: true;
@variationToastRight: true;
@variationToastCenter: true;
@variationToastCentered: true;
@variationToastInfo: true;
@variationToastWarning: true;
@variationToastSuccess: true;
@variationToastError: true;
@variationToastHorizontal: true;
@variationToastFloating: true;
@variationToastProgress: true;
@variationToastIcon: true;
@variationToastClose: true;
@variationToastImage: true;
@variationToastMessage: true;
@variationToastCard: true;
@variationToastActions: true;
@variationToastVertical: true;
@variationToastAttached: true;
@variationToastCompact: true;
@variationToastCentered: true;
@variationToastColors: @variationAllColors;

/* Transition */
@variationTransitionDisabled: true;
@variationTransitionLoading: true;
@variationTransitionLooping: true;
@variationTransitionInverted: true;
@variationTransitionBrowse: true;
@variationTransitionDrop: true;
@variationTransitionFade: true;
@variationTransitionFlip: true;
@variationTransitionScale: true;
@variationTransitionFly: true;
@variationTransitionSlide: true;
@variationTransitionSwing: true;
@variationTransitionZoom: true;
@variationTransitionFlash: true;
@variationTransitionShake: true;
@variationTransitionBounce: true;
@variationTransitionTada: true;
@variationTransitionPulse: true;
@variationTransitionJiggle: true;
@variationTransitionGlow: true;
@variationTransitionPulsating: true;
@variationTransitionColors: @variationAllColors;

/* Emojis */
@variationEmojiColons: true;
@variationEmojiNoColons: true;
@variationEmojiDisabled: true;
@variationEmojiLoading: true;
@variationEmojiLink: true;
