import 'dart:io';
import 'dart:ui';

import 'package:ai_ml_support/providers/chat_provider.dart';
import 'package:ai_ml_support/providers/voice_assistance_provider.dart';
import 'package:ai_ml_support/screens/ai_ml_support_package.dart';
import 'package:app_links/app_links.dart';
import 'package:eam/l10n/l10n.dart';
import 'package:eam/models/common/device_info.dart';
import 'package:eam/models/enum.dart';
import 'package:eam/models/language.dart';
import 'package:eam/presentation/eam_packages/tree_view/controller/controller.dart';
import 'package:eam/presentation/eam_packages/web_menu/menu_controller.dart';
import 'package:eam/provider/assistant/FabVisibilityProvider.dart';
import 'package:eam/provider/badge_count_provider.dart';
import 'package:eam/provider/chart/barchart_provider.dart';
import 'package:eam/provider/chart/piechart_provider.dart';
import 'package:eam/provider/document_provider.dart';
import 'package:eam/provider/language_provider.dart';
import 'package:eam/provider/notification/activity/notif_activity_list_provider.dart';
import 'package:eam/provider/notification/activity/selected_notif_activity_provider.dart';
import 'package:eam/provider/notification/genieSuggestionProvider.dart';
import 'package:eam/provider/notification/item/selected_notification_item_provider.dart';
import 'package:eam/provider/notification/notification_field_value_provider.dart';
import 'package:eam/provider/notification/notification_list_provider.dart';
import 'package:eam/provider/notification/selected_notification_provider.dart';
import 'package:eam/provider/notification/task/notif_task_list_provider.dart';
import 'package:eam/provider/notification/task/selected_notif_task_provider.dart';
import 'package:eam/provider/order/add_order_form_provider.dart';
import 'package:eam/provider/order/location_provider.dart';
import 'package:eam/provider/order/order_filter_provider.dart';
import 'package:eam/provider/order/order_provider.dart';
import 'package:eam/provider/order/selected_order_provider.dart';
import 'package:eam/provider/order_detail/order_form_provider.dart';
import 'package:eam/provider/progress_provider.dart';
import 'package:eam/provider/round/select_round_provider.dart';
import 'package:eam/provider/round_detail/round_detail_provider.dart';
import 'package:eam/provider/rounds_provider.dart';
import 'package:eam/provider/server_connection_provider.dart';
import 'package:eam/provider/single_selection_provider.dart';
import 'package:eam/provider/tech_objects/equipment_provider.dart';
import 'package:eam/provider/tech_objects/floc_details_provider.dart';
import 'package:eam/provider/tech_objects/floc_provider.dart';
import 'package:eam/provider/tech_objects/tech_objects_hierarchy_provider.dart';
import 'package:eam/route/route_generator.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_failure2.dart';
import 'package:eam/services/navigation_service.dart';
import 'package:eam/theme/theme_data.dart';
import 'package:easy_dynamic_theme/easy_dynamic_theme.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:unvired_settings/screens/log_viewer/provider/log_provider.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'presentation/eam_packages/dropdown/controller/controller.dart';
import 'presentation/eam_packages/tab_bar/tab_bar_provider.dart';
import 'presentation/pages/materials/working/sparepart_provider.dart';
import 'presentation/pages/tech_objects/tech_treeview_notifier.dart';
import 'presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'provider/dropdown/dropdown_item_provider.dart';
import 'provider/forms/form_provider.dart';
import 'provider/material/material_list_provider.dart';
import 'provider/notification/item/notification_item_list_provider.dart';
import 'provider/notification/notification_filter_provider.dart';
import 'provider/order_detail/order_material_provider.dart';
import 'provider/order_detail/time_confirmation_provider.dart';
import 'provider/selected_operation_provider.dart';
import 'package:path_provider/path_provider.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  var directory = await getApplicationSupportDirectory();

  debugPrint("Application Support Directory: ${directory.path}");

  //DartPluginRegistrant.ensureInitialized();
  //await _setDeviceOrientation();
  ThemeData theme = await lightThemeData();
  final appLinks = AppLinks();
  final sub = appLinks.uriLinkStream.listen((uri) {
    debugPrint("callBack recived, ${uri.toString()}");
    // Do something (navigation, ...)
  });
  AppLanguage? appLanguage = await getSelectedAppLanguage();
  await initializeAiMlSupport();
  await FabVisibilityProvider.initializeSharedPrefs();

  HttpOverrides.global = AppHttpOverrides();
  if (!kIsWeb) {
    if (!Platform.isWindows) {
      await Firebase.initializeApp();
    }
  } else {
    await Firebase.initializeApp(
      options: FirebaseOptions(
        apiKey: "AIzaSyA4KxBd6Yju4ACU2DEN3sP9DlwmuRdlLYE",
        appId: "1:747025537889:web:110a0a7ac1972c9c898320",
        messagingSenderId: "G-D9ZRSD3J73",
        projectId: "unvired-eam",
      ),
    );
  }
  //if (!Platform.isWindows) {
  // Pass all uncaught "fatal" errors from the framework to Crashlytics
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  // }

  final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);
  if (data.size.shortestSide <= 600) {
    // check if its bigger
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  runApp(
    EasyDynamicThemeWidget(
      initialThemeMode: ThemeMode.light,
      child: MyApp(theme: theme, appLanguage: appLanguage),
    ),
  );
}

class AppHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class MyApp extends StatefulWidget {
  final ThemeData theme;
  final AppLanguage? appLanguage;

  const MyApp({Key? key, required this.theme, this.appLanguage})
      : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    // Add this line to observe app lifecycle events
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    super.dispose();
    // Remove the observer when the widget is disposed
    // WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        // The app has come to the foreground (active state).
        // Enable the wake lock if needed.
        // PAHelper.doRefresh();
        _initAllProviders();
        WakelockPlus.enable();
        print("resumed");
        break;
      case AppLifecycleState.paused:
        // The app is in the background (inactive state).
        // Disable the wake lock to conserve battery.
        WakelockPlus.disable();
        print("paused");
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        // Handle these states as needed.
        print("inactive");
        break;
      // Add a case for AppLifecycleState.hidden
      // case AppLifecycleState.hidden:
      //   // Handle the hidden state if needed.
      //   break;
      // Add a default case to handle any future additions to the enum
      default:
        // Handle other states or future additions.
        break;
    }
  }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   switch (state) {
  //     case AppLifecycleState.resumed:
  //       // The app has come to the foreground (active state).
  //       // Enable the wake lock if needed.
  //       PAHelper.doRefresh();
  //       WakelockPlus.enable();
  //       break;
  //     case AppLifecycleState.paused:
  //       // The app is in the background (inactive state).
  //       // Disable the wake lock to conserve battery.
  //       WakelockPlus.disable();
  //       break;
  //     case AppLifecycleState.inactive:
  //     case AppLifecycleState.detached:
  //       // Handle these states as needed.
  //       break;
  //   }
  // }

    void _initAllProviders() {
    _initNotifProvider();
    _initOrderProvider();
  }

  void _initOrderProvider() async {
    if (mounted) {
      context.read<OrderNotifier>().getOrders(context, from: 'Dashboard');
      // await context.read<ProgressProvider>().setInboxCount();
    }
  }

  void _initNotifProvider() {
    var notificationProvider =
        Provider.of<NotificationListProvider>(context, listen: false);
    notificationProvider.getNotificationList(context,
        withOrder: notificationProvider.withOrder);
  }


  @override
  Widget build(BuildContext context) {
    final _router = RouteGenerator();

    return MultiProvider(
      providers: _providers(),
      child: ScreenUtilInit(
          //phone 1080, 2340
          //phone 1600, 2560

          designSize: Size(
            1080,
            2340,
          ),
          minTextAdapt: true,
          builder: (_, __) {
            return Consumer<LanguageProvider>(
              builder: (_, provider, __) {
                return MaterialApp(
                  scrollBehavior: EamScrollBehavior(),
                  navigatorKey: NavigationService.appNavigationKey,
                  debugShowCheckedModeBanner: false,
                  theme: widget.theme,
                  // darkTheme: darkThemeData,
                  themeMode: EasyDynamicTheme.of(context).themeMode,
                  title: 'EAM',
                  localizationsDelegates: [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate
                  ],
                  supportedLocales: L10n.all,
                  locale: provider.locale,
                  initialRoute: '/',
                  onGenerateRoute: _router.generateRoute,

                  //onGenerateRoute: onGenerateRouteNewTest,
                  //  home: SparePartsView(),
                );
              },
            );
          }),
    );
  }

  List<SingleChildWidget> _providers() => [
        ChangeNotifierProvider(
          create: (context) => LanguageProvider(
            locale: Locale.fromSubtags(
                languageCode: widget.appLanguage != null
                    ? widget.appLanguage!.code
                    : 'en'),
            selectedLanguage: widget.appLanguage != null
                ? widget.appLanguage!
                : AppLanguage(name: "English", code: "en", isDefault: true),
          ),
        ),
        Provider(
          create: (context) => DeviceInfo(
            deviceInfo: _getDeviceInfo(),
          ),
        ),

        ChangeNotifierProvider<NotificationGeneralNotifier>(
          create: (_) => NotificationGeneralNotifier(),
        ),
        ChangeNotifierProvider<OrderNotifier>(
          create: (_) => OrderNotifier(),
        ),
        ChangeNotifierProvider<FlocDetailsProvider>(
          create: (_) => FlocDetailsProvider(),
        ),

        ChangeNotifierProvider<EquipmentProvider>(
          create: (_) => EquipmentProvider(),
        ),

        //New
        // Logger Provider from Settings Package
        ChangeNotifierProvider(create: (_) => LoggerProvider()),

        ChangeNotifierProvider<TechTreeViewNotifier>(
          create: (_) => TechTreeViewNotifier(),
        ),
        ChangeNotifierProvider<DocumentProvider>(
          create: (_) => DocumentProvider(),
        ),

        ChangeNotifierProvider<FlocProvider>(
          create: (_) => FlocProvider(),
        ),
        ChangeNotifierProvider<SelectedOrderProvider>(
          create: (_) => SelectedOrderProvider(),
        ),
        ChangeNotifierProvider<OrderProvider>(
          create: (_) => OrderProvider(),
        ),
        ChangeNotifierProvider<RoundProvider>(
          create: (_) => RoundProvider(),
        ),
        ChangeNotifierProvider<OperationProvider>(
          create: (_) => OperationProvider(),
        ),
        ChangeNotifierProvider<DropDownController>(
          create: (_) => DropDownController(),
        ),
        ChangeNotifierProvider<BadgeCountProvider>(
          create: (_) => BadgeCountProvider(),
        ),
        ChangeNotifierProvider<TimeConfirmationProvider>(
          create: (_) => TimeConfirmationProvider(),
        ),
        ChangeNotifierProvider<OrderMaterialProvider>(
          create: (_) => OrderMaterialProvider(),
        ),
        ChangeNotifierProvider<MaterialListProvider>(
          create: (_) => MaterialListProvider(),
        ),
        ChangeNotifierProvider<FormProvider>(
          create: (_) => FormProvider(),
        ),
        ChangeNotifierProvider<NotificationItemListProvider>(
          create: (_) => NotificationItemListProvider(),
        ),
        ChangeNotifierProvider<SelectedNotificationProvider>(
          create: (_) => SelectedNotificationProvider(),
        ),
        ChangeNotifierProvider<NotifActivityListProvider>(
          create: (_) => NotifActivityListProvider(),
        ),
        ChangeNotifierProvider<NotifTaskListProvider>(
          create: (_) => NotifTaskListProvider(),
        ),
        ChangeNotifierProvider<TechObjectsHierarchyProvider>(
          create: (context) => TechObjectsHierarchyProvider(),
        ),
        //added
        ChangeNotifierProvider<TabBarProvider>(
          create: (context) => TabBarProvider(),
        ),
        ChangeNotifierProvider<SingleSelectProvider>(
          create: (context) => SingleSelectProvider(),
        ),

        ChangeNotifierProvider<NotificationListProvider>(
          create: (_) => NotificationListProvider(),
        ),
        ChangeNotifierProvider<LocationProvider>(
          create: (_) => LocationProvider(),
        ),
        ChangeNotifierProvider<SelectRoundProvider>(
            create: (_) => SelectRoundProvider()),
        ChangeNotifierProvider<RoundsDetailProvider>(
          create: (_) => RoundsDetailProvider(),
        ),
        ChangeNotifierProvider(
            create: (context) => SinglePageFieldValueProvider()),
        ChangeNotifierProvider<MenuControllerNotifier>(
            create: (_) => MenuControllerNotifier()),
        ChangeNotifierProvider<NotificationFilterProvider>(
          create: (_) => NotificationFilterProvider(),
        ),
        ChangeNotifierProvider<OrderFilterProvider>(
          create: (_) => OrderFilterProvider(),
        ),

        ChangeNotifierProvider<OrderFormProvider>(
          create: (_) => OrderFormProvider(),
        ),
        ChangeNotifierProvider<AddOrderFormProvider>(
          create: (_) => AddOrderFormProvider(),
        ),
        /////testing
        ///SparePartsProvider
        ChangeNotifierProvider<SparePartsProvider>(
          create: (_) => SparePartsProvider(),
        ),
        ChangeNotifierProvider<ProgressProvider>(
          create: (_) => ProgressProvider(),
        ),
        ChangeNotifierProvider<DropdownItemProvider>(
          create: (_) => DropdownItemProvider(),
        ),

        //Local Packages
        ChangeNotifierProvider<EamTreeViewController>(
          create: (_) => EamTreeViewController(),
        ),
        ChangeNotifierProvider<ServerConnectionProvider>(
          create: (_) => ServerConnectionProvider(),
        ),
        ChangeNotifierProvider<PieChartProvider>(
          create: (_) => PieChartProvider(),
        ),
        ChangeNotifierProvider<BarChartProvider>(
          create: (_) => BarChartProvider(),
        ),
        ChangeNotifierProvider<GenieSuggestionProvider>(
            create: (_) => GenieSuggestionProvider()),
        ChangeNotifierProvider<SelectedNotificationItemProvider>(
            create: (_) => SelectedNotificationItemProvider()),
        ChangeNotifierProvider(
          create: (_) => ChatProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => VoiceAssistanceProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => FabVisibilityProvider(),
        ),
        ChangeNotifierProvider<SelectedNotifTaskProvider>(
          create: (_) => SelectedNotifTaskProvider(),
        ),
        ChangeNotifierProvider<SelectedNotifActivityProvider>(
          create: (_) => SelectedNotifActivityProvider(),
        ),
      ];
}

DeviceInfoEnum _getDeviceInfo() {
  final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);

  if (data.size.shortestSide < 600) {
    return DeviceInfoEnum.mobile;
  } else {
    return DeviceInfoEnum.tablet;
  }
}

class EamScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.trackpad
      };
}
