import 'package:eam/be/ORDER_HEADER.dart';
import 'package:eam/be/ROUND_HEADER.dart';
import 'package:eam/helpers/order_helper.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/rounds_helper.dart';
import 'package:eam/presentation/common_widgets/common_icons.dart';
import 'package:eam/presentation/common_widgets/eam_tab.dart';
import 'package:eam/presentation/pages/rounds/widgets/rounds_detail_grid.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/provider/round/select_round_provider.dart';
import 'package:eam/provider/round_detail/round_detail_provider.dart';
import 'package:eam/utils/app_dimension.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/enum.dart';
import 'package:eam/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../widgets/eam_toolbar2.dart';
import '../../widgets/atoms_layer/eam_icons.dart';
import '../../widgets/molecules_layer/general/mobile_error_text.dart';
import '../notifications/create_notification/widgets/helper.dart';

class RoundsDetailView extends StatefulWidget {
  static const routeName = 'rounds-detail-page';
  final String? roundId;

  const RoundsDetailView({
    Key? key,
    required this.roundId,
  }) : super(key: key);

  @override
  State<RoundsDetailView> createState() => _RoundsDetailViewState();
}

class _RoundsDetailViewState extends State<RoundsDetailView> {
  // DeviceInfoEnum deviceInfo = DeviceInfoEnum.mobile;

  String roundStatus = RoundStatus.All.name;
  List _tabItems = [
    RoundStatus.All.name,
    RoundStatus.Pending.name,
  ];

  bool _isInit = false;

  @override
  void initState() {
    _init();

    super.initState();
  }

  _init() async {
    var prov = Provider.of<RoundsDetailProvider>(context, listen: false);
    prov.isEditable2(roundId: widget.roundId.toString());
    _initDefault();
  }

  _initDefault() {
    var selectedRoundProvider =
        Provider.of<SelectRoundProvider>(context, listen: false);
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      await selectedRoundProvider.getRoundHeader(
          roundId: widget.roundId.toString());
      _checkEditable();
      setState(() {
        _isInit = true;
      });
    });
  }

  _checkEditable() async {
    var res = await RoundsHelper.isEditable(
        roundHeader: context.read<SelectRoundProvider>().selectedRoundHeader);
    context.read<SelectRoundProvider>().isRoundEditable = res;
  }

  loadPendingRounds() {
    var roundDetailProvider =
        Provider.of<RoundsDetailProvider>(context, listen: false);
    roundDetailProvider.getAllRoundObjectList(
        roundId: widget.roundId!, filterCharSeq: Constants.ENABLED);
  }

  loadRounds(BuildContext context, String status) {
    // var roundDetailProvider =
    //     Provider.of<RoundsDetailProvider>(context, listen: false);
    var roundDetailProvider = context.read<RoundsDetailProvider>();
    if (status == RoundStatus.All.name) {
      roundDetailProvider.getAllRoundObjectList(
          roundId: roundDetailProvider.roundId.toString(), filterCharSeq: '');
    } else {
      roundDetailProvider.getAllRoundObjectList(
          roundId: roundDetailProvider.roundId.toString(),
          filterCharSeq: Constants.ENABLED);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _getAppbar(),
      body: !_isInit
          ? Center(child: CircularProgressIndicator())
          : PlatformDetails.isMobileScreen(context)
              ? _isMobileView()
              : Padding(
                  padding: Dimensions.padding(context),
                  child: PointerInterceptor(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // _displayErrorWidget(),
                          EamTabs(
                            items: _tabItems,
                            onChanged: (i, status) {
                              setState(() {
                                roundStatus = status;
                                print(roundStatus);
                              });

                              loadRounds(context, status);
                            },
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Expanded(
                            child: RoundsDetailGrid(
                              isMobile: PlatformDetails.isMobileScreen(context)
                                  ? true
                                  : false,
                              status: roundStatus,
                              roundId: widget.roundId ?? "",
                            ),
                          )
                        ]),
                  ),
                ),
    );
  }

  _getAppbar() {
    return EamAppBar(
      context: context,
      enableBack: true,
      title: "Readings",
      actionButton: [_completeRound()],
    );
  }

  _isMobileView() {
    return Padding(
      padding: Dimensions.padding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //   return Padding(
          //     padding: const EdgeInsets.only(left: 4.0, top: 0),
          //     child: Text(
          //       "This is short Text",
          //       maxLines: 2,
          //       overflow: TextOverflow.ellipsis,
          //       style: TextStyle(
          //           fontSize: 18,
          //           fontWeight: FontWeight.bold,
          //           color: Colors.black),
          //     ),
          //   );
          // }),
          // Padding(
          //   padding: const EdgeInsets.only(left: 4.0, top: 0),
          //   child: Text(
          //     "This is short Text",
          //     maxLines: 2,
          //     overflow: TextOverflow.ellipsis,
          //     style: TextStyle(
          //         fontSize: 18,
          //         fontWeight: FontWeight.bold,
          //         color: Colors.black),
          //   ),
          // ),
          // 20.0.spaceY,
          // OrderGeneralRow(
          //   showNoti: false,
          //   showHistory: false,
          // ),
          // _mobilErrorView(),
          // 20.0.spaceY,
          // EamTabs(
          //   items: _tabItems,
          //   onChanged: (i, status) {
          //     setState(() {
          //       roundStatus = status;
          //     });
          //     loadRounds(context, status);
          //   },
          // ),
          // SizedBox(
          //   height: 15,
          // ),
          // Expanded(
          //   child: RoundsDetailGrid(
          //     isMobile: MediaQuery.of(context).size.width < 560 &&
          //             PlatformDetails.isMobile
          //         ? true
          //         : false,
          //     status: roundStatus,
          //     orderNumber: widget.roundId ?? "",
          //   ),
          // )
        ],
      ),
    );
  }

  _mobilErrorView() {
    // return Consumer<OrderNotifier>(builder: (context, pro, child) {
    //   return pro.orderHeader.syncStatus == SyncStatus.error
    //       ? Padding(
    //           padding: const EdgeInsets.only(bottom: 18, top: 20),
    //           child: MobileErrorview(
    //             infoCat: pro.orderHeader.infoMsgCat ?? '',
    //             errormsg: pro.orderGeneral.infoMessage,
    //             color: pro.orderHeader.infoMsgCat == Constants.WARNING
    //                 ? Colors.orange.shade50
    //                 : Color(0XFFFCE4EC),
    //             borderColor: pro.orderHeader.infoMsgCat == Constants.WARNING
    //                 ? Colors.orange
    //                 : Colors.red,
    //           ),
    //         )
    //       : SizedBox();
    // });
    return Text("Error");
  }

  _displayErrorWidget() {
    // return Consumer<OrderNotifier>(builder: (context, pro, child) {
    //   return pro.orderHeader.syncStatus == SyncStatus.error
    //       ? Column(
    //           children: [
    //             Container(
    //               decoration: BoxDecoration(
    //                   color: pro.orderHeader.infoMsgCat == Constants.WARNING
    //                       ? Colors.orange.shade50
    //                       : Color(0XFFFCE4EC),
    //                   borderRadius: BorderRadius.circular(8),
    //                   border: Border.all(
    //                       color: pro.orderHeader.infoMsgCat == Constants.WARNING
    //                           ? Colors.orange
    //                           : Colors.red)),
    //               child: Padding(
    //                 padding: const EdgeInsets.all(8.0),
    //                 child: Column(
    //                   crossAxisAlignment: CrossAxisAlignment.start,
    //                   children: [
    //                     // Row(
    //                     //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //                     //   children: [
    //                     //     Text(
    //                     //       AppLocalizations.of(context)!.errorString,
    //                     //       style: TextStyle(
    //                     //         fontSize: 18,
    //                     //         color: Colors.red,
    //                     //         fontWeight: FontWeight.bold,
    //                     //       ),
    //                     //     ),
    //                     //   ],
    //                     // ),
    //                     // SizedBox(
    //                     //   height: ScreenUtils.SPACE_LABEL_VALUE_SECTION,
    //                     // ),
    //                     Text(
    //                       pro.orderGeneral.infoMessage.trim(),
    //                       style: AppStyles.headLine15_600,
    //                     )
    //                   ],
    //                 ),
    //               ),
    //             ),
    //             15.0.spaceY,
    //           ],
    //         )
    //       : SizedBox();
    // });
    return SizedBox();
  }

  Widget _completeRound_old() {
    var isTabPortraitOrMobile = PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context);
    return BlueBorderButton(
        isIconRequired: isTabPortraitOrMobile ? true : false,
        buttonName: AppLocalizations.of(context)!.completeRound,
        buttonNameColor: Colors.white,
        onTap: () async {
          await confirmAndSave();
        },
        color: isTabPortraitOrMobile
            ? Colors.white
            : Color.fromRGBO(40, 95, 231, 1),
        icon: EamIcon(
                iconName: EamIcon.flag, color: Theme.of(context).primaryColor)
            .icon());
  }

  Widget _completeRound() {
    return Consumer<SelectRoundProvider>(builder: (context, provider, child) {
      if (provider.isRoundEditable) {
        return CompleteIcon(
          onPressed: () => confirmAndSave(),
          title: "Save",
        );
      } else {
        return SizedBox.shrink();
      }
    });
  }

  Future<void> confirmAndSave() async {
    var roundsDetailProvider =
        Provider.of<RoundsDetailProvider>(context, listen: false);

    ROUND_HEADER round_header =
        await RoundsHelper.getRoundHeader(roundId: widget.roundId!);
    roundsDetailProvider.updateRoundListModel();
  }
}
