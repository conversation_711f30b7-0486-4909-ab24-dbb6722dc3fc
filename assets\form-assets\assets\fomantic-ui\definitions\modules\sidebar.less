/*!
 * # Fomantic-UI - Sidebar
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "sidebar";

@import (multiple) "../../theme.config";

/*******************************
            Sidebar
*******************************/

/* Sidebar Menu */
.ui.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    backface-visibility: hidden;
    transition: none;
    will-change: transform;
    transform: translate3d(0, 0, 0);
    visibility: hidden;
    height: 100% !important;
    max-height: 100%;
    border-radius: 0 !important;
    margin: 0 !important;
    overflow-y: auto !important;
    overscroll-behavior: @overscrollBehavior;
    z-index: @topLayer;
}

/* GPU Layers for Child Elements */
.ui.sidebar > * {
    backface-visibility: hidden;
}

/* --------------
   Direction
--------------- */
& when (@variationSidebarLeft) {
    .ui.left.sidebar {
        right: auto;
        left: 0;
        transform: translate3d(-100%, 0, 0);
    }
}
& when (@variationSidebarRight) {
    .ui.right.sidebar {
        right: 0 !important;
        left: auto !important;
        transform: translate3d(100%, 0, 0);
    }
}

& when (@variationSidebarTop) or (@variationSidebarBottom) {
    .ui.top.sidebar,
    .ui.bottom.sidebar {
        width: 100% !important;
        height: auto !important;
    }
    & when (@variationSidebarTop) {
        .ui.top.sidebar {
            top: 0 !important;
            bottom: auto !important;
            transform: translate3d(0, -100%, 0);
        }
    }
    & when (@variationSidebarBottom) {
        .ui.bottom.sidebar {
            top: auto !important;
            bottom: 0 !important;
            transform: translate3d(0, 100%, 0);
        }
    }
}

/* --------------
     Pushable
--------------- */

.pushable.pushable.pushable {
    height: 100%;
    overflow-x: hidden;
    padding: 0;
    &.locked {
        overflow-y: hidden;
        background: inherit;
    }
}

/* Whole Page */
body.pushable {
    background: @canvasBackground;
    &.dimmed {
        background: inherit;
    }
}

/* Page Context */
.pushable:not(body) {
    transform: translate3d(0, 0, 0);
    overflow-y: hidden;
}
.pushable:not(body) > .ui.sidebar,
.pushable:not(body) > .fixed,
.pushable:not(body) > .pusher::after {
    position: absolute;
}

/* --------------
     Fixed
--------------- */

.pushable > .fixed {
    position: fixed;
    backface-visibility: hidden;
    transition: transform @duration @easing;
    will-change: transform;
    z-index: @fixedLayer;
}

/* --------------
     Page
--------------- */

.pushable > .pusher {
    position: relative;
    backface-visibility: hidden;
    min-height: 100%;
    transition: transform @duration @easing;
    z-index: @middleLayer;

    /* Pusher should inherit background from context */
    background: inherit;
    &:not(.overflowing) {
        overflow: hidden;
    }
}

body.pushable > .pusher {
    background: @pageBackground;
}

/* --------------
     Dimmer
--------------- */

.pushable > .pusher::after {
    position: fixed;
    top: 0;
    right: 0;
    content: "";
    background: @dimmerColor;
    overflow: hidden;
    opacity: 0;
    transition: @dimmerTransition;
    will-change: opacity;
    z-index: @dimmerLayer;
}

/* --------------
    Coupling
--------------- */

.ui.sidebar.menu .item {
    border-radius: 0 !important;
}

/*******************************
            States
*******************************/

/* --------------
     Dimmed
--------------- */

.pushable > .pusher.dimmed::after {
    width: 100% !important;
    height: 100% !important;
    opacity: 1 !important;
}
& when (@variationSidebarBlurring) {
    .pushable > .pusher.dimmed.blurring:not(.closing)::after {
        background: @blurredBackgroundColor;
        -webkit-backdrop-filter: @blurredEndFilter;
        backdrop-filter: @blurredEndFilter;
    }
}
.pushable > .pusher.closing.dimmed::after {
    opacity: 0 !important;
}

/* --------------
    Animating
--------------- */

.ui.animating.sidebar {
    visibility: visible;
}

/* --------------
     Visible
--------------- */

.ui.visible.sidebar {
    visibility: visible;
    transform: translate3d(0, 0, 0);
}

/* Shadow Direction */
& when (@variationSidebarLeft) or (@variationSidebarRight) {
    .ui.left.visible.sidebar,
    .ui.right.visible.sidebar {
        box-shadow: @horizontalBoxShadow;
    }
}
& when (@variationSidebarTop) or (@variationSidebarBottom) {
    .ui.top.visible.sidebar,
    .ui.bottom.visible.sidebar {
        box-shadow: @verticalBoxShadow;
    }
}

/* Visible On Load */
& when (@variationSidebarLeft) {
    .ui.visible.left.sidebar ~ .fixed,
    .ui.visible.left.sidebar ~ .pusher {
        transform: translate3d(@width, 0, 0);
    }
}
& when (@variationSidebarRight) {
    .ui.visible.right.sidebar ~ .fixed,
    .ui.visible.right.sidebar ~ .pusher {
        transform: translate3d(-@width, 0, 0);
    }
}
& when (@variationSidebarTop) {
    .ui.visible.top.sidebar ~ .fixed,
    .ui.visible.top.sidebar ~ .pusher {
        transform: translate3d(0, @height, 0);
    }
}
& when (@variationSidebarBottom) {
    .ui.visible.bottom.sidebar ~ .fixed,
    .ui.visible.bottom.sidebar ~ .pusher {
        transform: translate3d(0, -@height, 0);
    }
}

& when (@variationSidebarLeft) or (@variationSidebarRight) {
    /* opposite sides visible forces content overlay */
    .ui.visible.left.sidebar ~ .ui.visible.right.sidebar ~ .fixed,
    .ui.visible.left.sidebar ~ .ui.visible.right.sidebar ~ .pusher,
    .ui.visible.right.sidebar ~ .ui.visible.left.sidebar ~ .fixed,
    .ui.visible.right.sidebar ~ .ui.visible.left.sidebar ~ .pusher {
        transform: translate3d(0, 0, 0);
    }
}

/* --------------
       iOS
--------------- */

/*******************************
          Variations
*******************************/

/* --------------
     Width
--------------- */

/* Left / Right */
& when (@variationSidebarLeft) or (@variationSidebarRight) {
    & when (@variationSidebarThin) {
        .ui.thin.left.sidebar,
        .ui.thin.right.sidebar {
            width: @thinWidth;
        }

        & when (@variationSidebarVeryThin) {
            .ui[class*="very thin"].left.sidebar,
            .ui[class*="very thin"].right.sidebar {
                width: @veryThinWidth;
            }
        }
    }

    .ui.left.sidebar,
    .ui.right.sidebar {
        width: @width;
    }

    & when (@variationSidebarWide) {
        .ui.wide.left.sidebar,
        .ui.wide.right.sidebar {
            width: @wideWidth;
        }

        & when (@variationSidebarVeryWide) {
            .ui[class*="very wide"].left.sidebar,
            .ui[class*="very wide"].right.sidebar {
                width: @veryWideWidth;
            }
        }
    }

    /* Left Visible */
    & when (@variationSidebarLeft) {
        & when (@variationSidebarThin) {
            .ui.visible.thin.left.sidebar ~ .fixed,
            .ui.visible.thin.left.sidebar ~ .pusher {
                transform: translate3d(@thinWidth, 0, 0);
            }

            & when (@variationSidebarVeryThin) {
                .ui.visible[class*="very thin"].left.sidebar ~ .fixed,
                .ui.visible[class*="very thin"].left.sidebar ~ .pusher {
                    transform: translate3d(@veryThinWidth, 0, 0);
                }
            }
        }

        & when (@variationSidebarWide) {
            .ui.visible.wide.left.sidebar ~ .fixed,
            .ui.visible.wide.left.sidebar ~ .pusher {
                transform: translate3d(@wideWidth, 0, 0);
            }

            & when (@variationSidebarVeryWide) {
                .ui.visible[class*="very wide"].left.sidebar ~ .fixed,
                .ui.visible[class*="very wide"].left.sidebar ~ .pusher {
                    transform: translate3d(@veryWideWidth, 0, 0);
                }
            }
        }
    }

    /* Right Visible */
    & when (@variationSidebarRight) {
        & when (@variationSidebarThin) {
            .ui.visible.thin.right.sidebar ~ .fixed,
            .ui.visible.thin.right.sidebar ~ .pusher {
                transform: translate3d(-@thinWidth, 0, 0);
            }

            & when (@variationSidebarVeryThin) {
                .ui.visible[class*="very thin"].right.sidebar ~ .fixed,
                .ui.visible[class*="very thin"].right.sidebar ~ .pusher {
                    transform: translate3d(-@veryThinWidth, 0, 0);
                }
            }
        }

        & when (@variationSidebarWide) {
            .ui.visible.wide.right.sidebar ~ .fixed,
            .ui.visible.wide.right.sidebar ~ .pusher {
                transform: translate3d(-@wideWidth, 0, 0);
            }

            & when (@variationSidebarVeryWide) {
                .ui.visible[class*="very wide"].right.sidebar ~ .fixed,
                .ui.visible[class*="very wide"].right.sidebar ~ .pusher {
                    transform: translate3d(-@veryWideWidth, 0, 0);
                }
            }
        }
    }
}

/*******************************
          Animations
*******************************/

& when (@variationSidebarOverlay) {
    /* --------------
        Overlay
    --------------- */

    /* Set-up */
    .ui.overlay.sidebar {
        z-index: @topLayer;
    }

    /* Initial */
    & when (@variationSidebarLeft) {
        .ui.left.overlay.sidebar {
            transform: translate3d(-100%, 0, 0);
        }
    }
    & when (@variationSidebarRight) {
        .ui.right.overlay.sidebar {
            transform: translate3d(100%, 0, 0);
        }
    }
    & when (@variationSidebarTop) {
        .ui.top.overlay.sidebar {
            transform: translate3d(0, -100%, 0);
        }
    }
    & when (@variationSidebarBottom) {
        .ui.bottom.overlay.sidebar {
            transform: translate3d(0, 100%, 0);
        }
    }

    /* Animation */
    .animating.ui.overlay.sidebar,
    .ui.visible.overlay.sidebar {
        transition: transform @duration @easing;
    }

    /* End - Sidebar */
    & when (@variationSidebarLeft) {
        .ui.visible.left.overlay.sidebar {
            transform: translate3d(0, 0, 0);
        }
    }
    & when (@variationSidebarRight) {
        .ui.visible.right.overlay.sidebar {
            transform: translate3d(0, 0, 0);
        }
    }
    & when (@variationSidebarTop) {
        .ui.visible.top.overlay.sidebar {
            transform: translate3d(0, 0, 0);
        }
    }
    & when (@variationSidebarBottom) {
        .ui.visible.bottom.overlay.sidebar {
            transform: translate3d(0, 0, 0);
        }
    }

    /* End - Pusher */
    .ui.visible.overlay.sidebar ~ .fixed,
    .ui.visible.overlay.sidebar ~ .pusher {
        transform: none !important;
    }
}

& when (@variationSidebarPush) {
    /* --------------
          Push
    --------------- */

    /* Initial */
    .ui.push.sidebar {
        transition: transform @duration @easing;
        z-index: @topLayer;
    }

    /* Sidebar - Initial */
    & when (@variationSidebarLeft) {
        .ui.left.push.sidebar {
            transform: translate3d(-100%, 0, 0);
        }
    }
    & when (@variationSidebarRight) {
        .ui.right.push.sidebar {
            transform: translate3d(100%, 0, 0);
        }
    }
    & when (@variationSidebarTop) {
        .ui.top.push.sidebar {
            transform: translate3d(0, -100%, 0);
        }
    }
    & when (@variationSidebarBottom) {
        .ui.bottom.push.sidebar {
            transform: translate3d(0, 100%, 0);
        }
    }

    /* End */
    .ui.visible.push.sidebar {
        transform: translate3d(0, 0, 0);
    }
}

& when (@variationSidebarUncover) {
    /* --------------
        Uncover
    --------------- */

    /* Initial */
    .ui.uncover.sidebar {
        transform: translate3d(0, 0, 0);
        z-index: @bottomLayer;
    }

    /* End */
    .ui.visible.uncover.sidebar {
        transform: translate3d(0, 0, 0);
        transition: transform @duration @easing;
    }
}

& when (@variationSidebarSlideAlong) {
    /* --------------
       Slide Along
    --------------- */

    /* Initial */
    .ui.slide.along.sidebar {
        z-index: @bottomLayer;
    }

    /* Sidebar - Initial */
    & when (@variationSidebarLeft) {
        .ui.left.slide.along.sidebar {
            transform: translate3d(-50%, 0, 0);
        }
    }
    & when (@variationSidebarRight) {
        .ui.right.slide.along.sidebar {
            transform: translate3d(50%, 0, 0);
        }
    }
    & when (@variationSidebarTop) {
        .ui.top.slide.along.sidebar {
            transform: translate3d(0, -50%, 0);
        }
    }
    & when (@variationSidebarBottom) {
        .ui.bottom.slide.along.sidebar {
            transform: translate3d(0, 50%, 0);
        }
    }

    /* Animation */
    .ui.animating.slide.along.sidebar {
        transition: transform @duration @easing;
    }

    /* End */
    .ui.visible.slide.along.sidebar {
        transform: translate3d(0, 0, 0);
    }
}

& when (@variationSidebarSlideOut) {
    /* --------------
       Slide Out
    --------------- */

    /* Initial */
    .ui.slide.out.sidebar {
        z-index: @bottomLayer;
    }

    /* Sidebar - Initial */
    & when (@variationSidebarLeft) {
        .ui.left.slide.out.sidebar {
            transform: translate3d(50%, 0, 0);
        }
    }
    & when (@variationSidebarRight) {
        .ui.right.slide.out.sidebar {
            transform: translate3d(-50%, 0, 0);
        }
    }
    & when (@variationSidebarTop) {
        .ui.top.slide.out.sidebar {
            transform: translate3d(0, 50%, 0);
        }
    }
    & when (@variationSidebarBottom) {
        .ui.bottom.slide.out.sidebar {
            transform: translate3d(0, -50%, 0);
        }
    }

    /* Animation */
    .ui.animating.slide.out.sidebar {
        transition: transform @duration @easing;
    }

    /* End */
    .ui.visible.slide.out.sidebar {
        transform: translate3d(0, 0, 0);
    }
}

& when (@variationSidebarScale) {
    /* --------------
       Scale Down
    --------------- */

    /* Initial */
    .ui.scale.down.sidebar {
        transition: transform @duration @easing;
        z-index: @topLayer;
    }

    & when (@variationSidebarLeft) {
        .ui.left.scale.down.sidebar {
            transform: translate3d(-100%, 0, 0);
        }
    }
    & when (@variationSidebarRight) {
        .ui.right.scale.down.sidebar {
            transform: translate3d(100%, 0, 0);
        }
    }
    & when (@variationSidebarTop) {
        .ui.top.scale.down.sidebar {
            transform: translate3d(0, -100%, 0);
        }
    }
    & when (@variationSidebarBottom) {
        .ui.bottom.scale.down.sidebar {
            transform: translate3d(0, 100%, 0);
        }
    }

    /* Pusher - Initial */
    & when (@variationSidebarLeft) {
        .ui.scale.down.left.sidebar ~ .pusher {
            transform-origin: 75% 50%;
        }
    }
    & when (@variationSidebarRight) {
        .ui.scale.down.right.sidebar ~ .pusher {
            transform-origin: 25% 50%;
        }
    }
    & when (@variationSidebarTop) {
        .ui.scale.down.top.sidebar ~ .pusher {
            transform-origin: 50% 75%;
        }
    }
    & when (@variationSidebarBottom) {
        .ui.scale.down.bottom.sidebar ~ .pusher {
            transform-origin: 50% 25%;
        }
    }

    /* Animation */
    .ui.animating.scale.down > .visible.ui.sidebar {
        transition: transform @duration @easing;
    }
    .ui.visible.scale.down.sidebar ~ .pusher,
    .ui.animating.scale.down.sidebar ~ .pusher {
        display: block !important;
        width: 100%;
        height: 100%;
        overflow: hidden !important;
    }

    /* End */
    .ui.visible.scale.down.sidebar {
        transform: translate3d(0, 0, 0);
    }
    .ui.visible.scale.down.sidebar ~ .pusher {
        transform: scale(0.75);
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
