/*!
 * # Fomantic-UI - Rating
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "rating";

@import (multiple) "../../theme.config";

/*******************************
           Rating
*******************************/

.ui.rating {
    display: inline-flex;
    white-space: @whiteSpace;
    vertical-align: @verticalAlign;
}
.ui.rating:last-child {
    margin-right: 0;
}

/* Icon */
.ui.rating .icon {
    padding: 0;
    margin: 0;
    text-align: center;
    font-weight: @normal;
    font-style: normal;
    flex: 1 0 auto;
    cursor: @iconCursor;
    width: @iconWidth;
    height: @iconHeight;
    transition: @iconTransition;
    line-height: 1;
    backface-visibility: hidden;
}

/*******************************
             Types
*******************************/

/* -------------------
      Standard
-------------------- */

/* Inactive Icon */
.ui.rating .icon {
    background: @inactiveBackground;
    color: @inactiveColor;
}

/* Active Icon */
.ui.rating .active.icon {
    background: @activeBackground;
    color: @activeColor;
}

& when (@variationRatingPartial) {
    /* Partially Active Icon */
    .ui.rating .icon.partial.active {
        background: linear-gradient(to right, @activeColor 0% var(--full), @inactiveColor var(--full) 100%);
    }

    /* central override for colors */
    .ui.ui.ui.rating .icon.partial.active {
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        text-shadow: none;
    }
}

/* Selected Icon */
.ui.rating .icon.selected,
.ui.rating .icon.selected.active,
.ui.rating .icon.selected.partial.active {
    background: @selectedBackground;
    color: @selectedColor;
    background-clip: unset;
}

/* --------------
     Colors
-------------- */
& when not (@variationRatingColors = false) {
    each(@variationRatingColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @l: @colors[@@color][light];
        @h: @colors[@@color][hover];
        @lh: @colors[@@color][lightHover];

        .ui.@{color}.rating .active.icon {
            color: @l;
            text-shadow:
                0 -@shadowWidth 0 @c,
                -@shadowWidth 0 0 @c,
                0 @shadowWidth 0 @c,
                @shadowWidth 0 0 @c;
        }
        .ui.@{color}.rating .icon.selected,
        .ui.@{color}.rating .icon.selected.active,
        .ui.@{color}.rating .icon.selected.partial.active {
            background: inherit;
            color: @lh;
            text-shadow:
                0 -@shadowWidth 0 @h,
                -@shadowWidth 0 0 @h,
                0 @shadowWidth 0 @h,
                @shadowWidth 0 0 @h;

            -webkit-text-stroke: unset;
            background-clip: unset;
        }
        & when (@variationRatingPartial) {
            .ui.@{color}.rating .icon.partial.active {
                background: linear-gradient(to right, @l 0% var(--full), @inactiveColor var(--full) 100%);
                -webkit-text-stroke: @c 0.78px;
            }
        }
    });
}

/*******************************
             States
*******************************/

& when (@variationRatingDisabled) {
    /* -------------------
           Disabled
    -------------------- */

    /* disabled rating */
    .ui.disabled.rating .icon {
        cursor: default;
        pointer-events: none;
    }
}

/* -------------------
   User Interactive
-------------------- */

/* Selected Rating */
.ui.rating.selected .active.icon {
    opacity: @interactiveActiveIconOpacity;
}
.ui.rating.selected .icon.selected,
.ui.rating .icon.selected {
    opacity: @interactiveSelectedIconOpacity;
}

/*******************************
          Variations
*******************************/

.ui.rating {
    font-size: @medium;
}
& when not (@variationRatingSizes = false) {
    each(@variationRatingSizes, {
        @s: @@value;
        .ui.@{value}.rating {
            font-size: @s;
        }
    });
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
