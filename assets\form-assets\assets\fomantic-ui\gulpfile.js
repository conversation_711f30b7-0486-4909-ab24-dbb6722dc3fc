const gulp = require('gulp');
const less = require('gulp-less');
const path = require('path');
const cleanCSS = require('gulp-clean-css'); // Include gulp-clean-css
const rename = require('gulp-rename'); // Include gulp-rename

// gulpfile.js

gulp.task('less', function () {
	return gulp
		.src('semantic.less')
		.pipe(
			less({
				paths: [path.join(__dirname, 'less', 'includes')],
			}),
		)
		.pipe(gulp.dest('css/semantic'))
		.pipe(cleanCSS()) // Add this line to minify the CSS
		.pipe(rename({ suffix: '.min' })) // Add this line to rename the minified file
		.pipe(gulp.dest('css/semantic'));
});

gulp.task('watch', function () {
	gulp.watch(
		['fomantic-ui/**/*.less', 'fomantic-ui/**/*.overrides', 'fomantic-ui/**/*.variables'],
		gulp.series('less'),
	);
});

gulp.task('default', gulp.series('less', 'watch'));
