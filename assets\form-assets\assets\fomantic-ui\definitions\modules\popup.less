/*!
 * # Fomantic-UI - Popup
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "popup";

@import (multiple) "../../theme.config";

/*******************************
            Popup
*******************************/

.ui.popup {
    display: none;
    position: absolute;
    top: 0;
    right: 0;

    /* Fixes content being squished when inline (moz only) */
    min-width: min-content;
    z-index: @zIndex;
    border: @border;
    line-height: @lineHeight;
    max-width: @maxWidth;
    background: @background;
    padding: @verticalPadding @horizontalPadding;
    font-weight: @fontWeight;
    font-style: @fontStyle;
    color: @color;
    border-radius: @borderRadius;
    box-shadow: @boxShadow;
}
.ui.popup > .header {
    padding: 0;
    font-family: @headerFont;
    font-size: @headerFontSize;
    line-height: @headerLineHeight;
    font-weight: @headerFontWeight;
}
.ui.popup > .header + .content {
    padding-top: @headerDistance;
}

.ui.popup::before {
    position: absolute;
    content: "";
    width: @arrowSize;
    height: @arrowSize;
    background: @arrowBackground;
    transform: rotate(45deg);
    z-index: @arrowZIndex;
    box-shadow: @arrowBoxShadow;
}

/*******************************
            Types
*******************************/

& when (@variationPopupTooltip) {
    /* --------------
        Tooltip
    --------------- */

    /* Content */
    [data-tooltip] {
        position: relative;
    }

    /* Arrow */
    [data-tooltip]::before {
        pointer-events: none;
        position: absolute;
        content: "";
        font-size: @medium;
        width: @arrowSize;
        height: @arrowSize;
        background: @tooltipArrowBackground;
        z-index: @arrowZIndex;
        box-shadow: @tooltipArrowBoxShadow;
    }

    /* Popup */
    [data-tooltip]::after {
        pointer-events: none;
        content: attr(data-tooltip);
        position: absolute;
        text-transform: none;
        text-align: left;
        text-shadow: none;
        white-space: nowrap;
        font-size: @tooltipFontSize;
        border: @tooltipBorder;
        line-height: @tooltipLineHeight;
        max-width: @tooltipMaxWidth;
        background: @tooltipBackground;
        padding: @tooltipPadding;
        font-weight: @tooltipFontWeight;
        font-style: @tooltipFontStyle;
        color: @tooltipColor;
        border-radius: @tooltipBorderRadius;
        box-shadow: @tooltipBoxShadow;
        z-index: @tooltipZIndex;
    }

    /* Animation */
    [data-tooltip]::before,
    [data-tooltip]::after {
        pointer-events: none;
        opacity: 0;
        transition:
            transform @tooltipDuration @tooltipEasing,
            opacity @tooltipDuration @tooltipEasing;
    }
    [data-tooltip]::after,
    [data-tooltip]:hover::before {
        transition-delay: @tooltipDelay;
    }
    [data-tooltip]::before,
    [data-tooltip]:hover::after {
        transition-delay: 0s;
    }
    [data-tooltip]::before {
        transform: rotate(45deg) scale(@tooltipScaleInit);
        transform-origin: center top;
    }
    [data-tooltip]::after {
        transform-origin: center bottom;
    }
    [data-tooltip][data-variation~="visible"]::before,
    [data-tooltip][data-variation~="visible"]::after,
    [data-tooltip]:hover::before,
    [data-tooltip]:hover::after {
        pointer-events: auto;
        opacity: 1;
    }
    [data-tooltip]:hover::before {
        transform: rotate(45deg) scale(1);
    }

    /* Animation Position */
    [data-tooltip]:not([data-position])::after,
    [data-tooltip][data-position="top center"]::after,
    [data-tooltip][data-position="bottom center"]::after {
        transform: translateX(-50%) scale(@tooltipScaleInit);
    }
    [data-tooltip]:not([data-position]):hover::after,
    [data-tooltip][data-position="top center"]:hover::after,
    [data-tooltip][data-position="bottom center"]:hover::after {
        transform: translateX(-50%) scale(1);
    }
    [data-tooltip][data-position="left center"]::after,
    [data-tooltip][data-position="right center"]::after {
        transform: translateY(-50%) scale(@tooltipScaleInit);
    }
    [data-tooltip][data-position="left center"]:hover::after,
    [data-tooltip][data-position="right center"]:hover::after {
        transform: translateY(-50%) scale(1);
        // https://github.com/fomantic/Fomantic-UI/pull/1537
        // stylelint-disable-next-line property-no-vendor-prefix
        -moz-transform: translateY(-50%) scale(1.0001) !important;
    }
    [data-tooltip][data-position="top left"]::after,
    [data-tooltip][data-position="top right"]::after,
    [data-tooltip][data-position="bottom left"]::after,
    [data-tooltip][data-position="bottom right"]::after {
        transform: scale(@tooltipScaleInit);
    }
    [data-tooltip][data-position="top left"]:hover::after,
    [data-tooltip][data-position="top right"]:hover::after,
    [data-tooltip][data-position="bottom left"]:hover::after,
    [data-tooltip][data-position="bottom right"]:hover::after {
        transform: scale(1);
    }
    & when (@variationPopupFixed) {
        [data-tooltip][data-variation~="fixed"]::after {
            white-space: normal;
            width: @maxWidth;
        }
        [data-tooltip][data-variation*="wide fixed"]::after {
            width: @wideWidth;
        }
        [data-tooltip][data-variation*="very wide fixed"]::after {
            width: @veryWideWidth;
        }
        @media only screen and (max-width: @largestMobileScreen) {
            [data-tooltip][data-variation~="fixed"]::after {
                width: @maxWidth;
            }
        }
    }
    & when (@variationPopupInverted) {
        /* --------------
            Inverted
        --------------- */

        /* Arrow */
        [data-tooltip][data-inverted]::before {
            box-shadow: none;
        }

        /* Arrow Position */
        [data-tooltip]:not([data-position])[data-inverted]::before {
            background: @invertedArrowBottomBackground;
        }

        /* Popup */
        [data-tooltip][data-inverted]::after {
            background: @tooltipInvertedBackground;
            color: @tooltipInvertedColor;
            border: @tooltipInvertedBorder;
            box-shadow: @tooltipInvertedBoxShadow;
        }
    }

    & when (@variationPopupPosition) {
        /* --------------
            Position
        --------------- */
        & when (@variationPopupTop) {
            [data-position~="top"][data-tooltip]::before {
                background: @arrowBottomBackground;
            }
            & when (@variationPopupCenter) {
                /* Top Center (default) */
                [data-tooltip]:not([data-position])::after,
                [data-position="top center"][data-tooltip]::after {
                    top: auto;
                    right: auto;
                    left: 50%;
                    bottom: 100%;
                    margin-bottom: @tooltipDistanceAway;
                }
                [data-tooltip]:not([data-position])::before,
                [data-position="top center"][data-tooltip]::before {
                    top: auto;
                    right: auto;
                    bottom: 100%;
                    left: 50%;
                    background: @tooltipArrowTopBackground;
                    margin-left: @tooltipArrowHorizontalOffset;
                    margin-bottom: -@tooltipArrowVerticalOffset;
                }
            }
            & when (@variationPopupLeft) {
                /* Top Left */
                [data-position="top left"][data-tooltip]::after {
                    top: auto;
                    right: auto;
                    left: 0;
                    bottom: 100%;
                    margin-bottom: @tooltipDistanceAway;
                }
                [data-position="top left"][data-tooltip]::before {
                    top: auto;
                    right: auto;
                    bottom: 100%;
                    left: @arrowDistanceFromEdge;
                    margin-left: @tooltipArrowHorizontalOffset;
                    margin-bottom: -@tooltipArrowVerticalOffset;
                }
            }
            & when (@variationPopupRight) {
                /* Top Right */
                [data-position="top right"][data-tooltip]::after {
                    top: auto;
                    left: auto;
                    right: 0;
                    bottom: 100%;
                    margin-bottom: @tooltipDistanceAway;
                }
                [data-position="top right"][data-tooltip]::before {
                    top: auto;
                    left: auto;
                    bottom: 100%;
                    right: @arrowDistanceFromEdge;
                    margin-left: @tooltipArrowHorizontalOffset;
                    margin-bottom: -@tooltipArrowVerticalOffset;
                }
            }
        }

        & when (@variationPopupBottom) {
            [data-position~="bottom"][data-tooltip]::before {
                background: @arrowTopBackground;
                box-shadow: @bottomArrowBoxShadow;
            }
            & when (@variationPopupCenter) {
                /* Bottom Center */
                [data-position="bottom center"][data-tooltip]::after {
                    bottom: auto;
                    right: auto;
                    left: 50%;
                    top: 100%;
                    margin-top: @tooltipDistanceAway;
                }
                [data-position="bottom center"][data-tooltip]::before {
                    bottom: auto;
                    right: auto;
                    top: 100%;
                    left: 50%;
                    margin-left: @tooltipArrowHorizontalOffset;
                    margin-top: -@arrowOffset;
                }
            }
            & when (@variationPopupLeft) {
                /* Bottom Left */
                [data-position="bottom left"][data-tooltip]::after {
                    left: 0;
                    top: 100%;
                    margin-top: @tooltipDistanceAway;
                }
                [data-position="bottom left"][data-tooltip]::before {
                    bottom: auto;
                    right: auto;
                    top: 100%;
                    left: @arrowDistanceFromEdge;
                    margin-left: @tooltipArrowHorizontalOffset;
                    margin-top: -@tooltipArrowVerticalOffset;
                }
            }
            & when (@variationPopupRight) {
                /* Bottom Right */
                [data-position="bottom right"][data-tooltip]::after {
                    right: 0;
                    top: 100%;
                    margin-top: @tooltipDistanceAway;
                }
                [data-position="bottom right"][data-tooltip]::before {
                    bottom: auto;
                    left: auto;
                    top: 100%;
                    right: @arrowDistanceFromEdge;
                    margin-left: @tooltipArrowVerticalOffset;
                    margin-top: -@tooltipArrowHorizontalOffset;
                }
            }
        }
        & when (@variationPopupCenter) {
            & when (@variationPopupLeft) {
                /* Left Center */
                [data-position="left center"][data-tooltip]::after {
                    right: 100%;
                    top: 50%;
                    margin-right: @tooltipDistanceAway;
                }
                [data-position="left center"][data-tooltip]::before {
                    right: 100%;
                    top: 50%;
                    margin-top: @tooltipArrowVerticalOffset;
                    margin-right: @tooltipArrowHorizontalOffset;
                    background: @arrowCenterBackground;
                    box-shadow: @leftArrowBoxShadow;
                }
            }
            & when (@variationPopupRight) {
                /* Right Center */
                [data-position="right center"][data-tooltip]::after {
                    left: 100%;
                    top: 50%;
                    margin-left: @tooltipDistanceAway;
                }
                [data-position="right center"][data-tooltip]::before {
                    left: 100%;
                    top: 50%;
                    margin-top: @tooltipArrowHorizontalOffset;
                    margin-left: -@tooltipArrowVerticalOffset;
                    background: @arrowCenterBackground;
                    box-shadow: @rightArrowBoxShadow;
                }
            }
        }

        & when (@variationPopupInverted) {
            /* Inverted Arrow Color */
            & when (@variationPopupBottom) {
                [data-inverted][data-position~="bottom"][data-tooltip]::before {
                    background: @invertedArrowTopBackground;
                    box-shadow: @bottomArrowBoxShadow;
                }
            }
            & when (@variationPopupCenter) {
                & when (@variationPopupLeft) {
                    [data-inverted][data-position="left center"][data-tooltip]::before {
                        background: @invertedArrowCenterBackground;
                        box-shadow: @leftArrowBoxShadow;
                    }
                }
                & when (@variationPopupRight) {
                    [data-inverted][data-position="right center"][data-tooltip]::before {
                        background: @invertedArrowCenterBackground;
                        box-shadow: @rightArrowBoxShadow;
                    }
                }
            }
            & when (@variationPopupTop) {
                [data-inverted][data-position~="top"][data-tooltip]::before {
                    background: @invertedArrowBottomBackground;
                }
            }
        }

        & when (@variationPopupBottom) {
            [data-position~="bottom"][data-tooltip]::before {
                transform-origin: center bottom;
            }
            [data-position~="bottom"][data-tooltip]::after {
                transform-origin: center top;
            }
        }
        & when (@variationPopupCenter) {
            & when (@variationPopupBottom) {
                [data-position="bottom center"][data-tooltip]::before {
                    transform-origin: center top;
                }
            }
            & when (@variationPopupLeft) {
                [data-position="left center"][data-tooltip]::before {
                    transform-origin: top center;
                }
                [data-position="left center"][data-tooltip]::after {
                    transform-origin: right center;
                }
            }
            & when (@variationPopupRight) {
                [data-position="right center"][data-tooltip]::before {
                    transform-origin: right center;
                }
                [data-position="right center"][data-tooltip]::after {
                    transform-origin: left center;
                }
            }
        }
    }

    [data-position="top left"][data-tooltip]::after {
        transform-origin: bottom left;
    }
    [data-position="top right"][data-tooltip]::after {
        transform-origin: bottom right;
    }
    [data-position="bottom left"][data-tooltip]::after {
        transform-origin: top left;
    }
    [data-position="bottom right"][data-tooltip]::after {
        transform-origin: top right;
    }

    & when (@variationPopupBasic) {
        /* --------------
            Basic
        --------------- */
        [data-tooltip][data-variation~="basic"]::before {
            display: none;
        }
    }

    & when (@variationPopupMultiline) {
        [data-tooltip][data-variation~="multiline"]::after {
            white-space: @multilineWhiteSpace;
        }
    }
}

/* --------------
     Spacing
--------------- */

.ui.popup {
    margin: 0;
}

& when (@variationPopupTop) {
    /* Extending from Top */
    .ui.top.popup {
        margin: 0 0 @popupDistanceAway;
    }
    & when (@variationPopupLeft) {
        .ui.top.left.popup {
            transform-origin: left bottom;
        }
    }
    & when (@variationPopupCenter) {
        .ui.top.center.popup {
            transform-origin: center bottom;
        }
    }
    & when (@variationPopupRight) {
        .ui.top.right.popup {
            transform-origin: right bottom;
        }
    }
}

& when (@variationPopupCenter) {
    /* Extending from Vertical Center */
    & when (@variationPopupLeft) {
        .ui.left.center.popup {
            margin: 0 @popupDistanceAway 0 0;
            transform-origin: right 50%;
        }
    }
    & when (@variationPopupRight) {
        .ui.right.center.popup {
            margin: 0 0 0 @popupDistanceAway;
            transform-origin: left 50%;
        }
    }
}

& when (@variationPopupBottom) {
    /* Extending from Bottom */
    .ui.bottom.popup {
        margin: @popupDistanceAway 0 0;
    }
    & when (@variationPopupLeft) {
        .ui.bottom.left.popup {
            transform-origin: left top;
        }
    }
    & when (@variationPopupCenter) {
        .ui.bottom.center.popup {
            transform-origin: center top;
        }
    }
    & when (@variationPopupRight) {
        .ui.bottom.right.popup {
            transform-origin: right top;
        }
    }

    /* --------------
         Pointer
    --------------- */

    /* --- Below --- */
    .ui.bottom.center.popup::before {
        margin-left: @arrowOffset;
        top: @arrowOffset;
        left: 50%;
        right: auto;
        bottom: auto;
        box-shadow: @bottomArrowBoxShadow;
    }

    .ui.bottom.left.popup {
        margin-left: @boxArrowOffset;
    }

    /* rtl:rename */
    .ui.bottom.left.popup::before {
        top: @arrowOffset;
        left: @arrowDistanceFromEdge;
        right: auto;
        bottom: auto;
        margin-left: 0;
        box-shadow: @bottomArrowBoxShadow;
    }

    .ui.bottom.right.popup {
        margin-right: @boxArrowOffset;
    }

    /* rtl:rename */
    .ui.bottom.right.popup::before {
        top: @arrowOffset;
        right: @arrowDistanceFromEdge;
        bottom: auto;
        left: auto;
        margin-left: 0;
        box-shadow: @bottomArrowBoxShadow;
    }
}

& when (@variationPopupTop) {
    /* --- Above --- */
    & when (@variationPopupCenter) {
        .ui.top.center.popup::before {
            top: auto;
            right: auto;
            bottom: @arrowOffset;
            left: 50%;
            margin-left: @arrowOffset;
        }
    }
    & when (@variationPopupLeft) {
        .ui.top.left.popup {
            margin-left: @boxArrowOffset;
        }

        /* rtl:rename */
        .ui.top.left.popup::before {
            bottom: @arrowOffset;
            left: @arrowDistanceFromEdge;
            top: auto;
            right: auto;
            margin-left: 0;
        }
    }
    & when (@variationPopupRight) {
        .ui.top.right.popup {
            margin-right: @boxArrowOffset;
        }

        /* rtl:rename */
        .ui.top.right.popup::before {
            bottom: @arrowOffset;
            right: @arrowDistanceFromEdge;
            top: auto;
            left: auto;
            margin-left: 0;
        }
    }
}

& when (@variationPopupCenter) {
    /* --- Left Center --- */

    /* rtl:rename */
    & when (@variationPopupLeft) {
        .ui.left.center.popup::before {
            top: 50%;
            right: @arrowOffset;
            bottom: auto;
            left: auto;
            margin-top: @arrowOffset;
            box-shadow: @leftArrowBoxShadow;
        }
    }
    & when (@variationPopupRight) {
        /* --- Right Center --- */

        /* rtl:rename */
        .ui.right.center.popup::before {
            top: 50%;
            left: @arrowOffset;
            bottom: auto;
            right: auto;
            margin-top: @arrowOffset;
            box-shadow: @rightArrowBoxShadow;
        }
    }
    & when (@variationPopupLeft) or (@variationPopupRight) {
        .ui.right.center.popup::before,
        .ui.left.center.popup::before {
            background: @arrowCenterBackground;
        }
    }
}

& when (@variationPopupBottom) {
    /* Arrow Color By Location */
    .ui.bottom.popup::before {
        background: @arrowTopBackground;
    }
}

& when (@variationPopupTop) {
    .ui.top.popup::before {
        background: @arrowBottomBackground;
    }
}

& when (@variationPopupInverted) {
    & when (@variationPopupBottom) {
        /* Inverted Arrow Color */
        .ui.inverted.bottom.popup::before {
            background: @invertedArrowTopBackground;
        }
    }
    & when (@variationPopupCenter) {
        .ui.inverted.right.center.popup::before,
        .ui.inverted.left.center.popup::before {
            background: @invertedArrowCenterBackground;
        }
    }
    & when (@variationPopupTop) {
        .ui.inverted.top.popup::before {
            background: @invertedArrowBottomBackground;
        }
    }
}

/*******************************
            Coupling
*******************************/

/* Immediate Nested Grid */
.ui.ui.ui.popup > .ui.grid:not(.padded) {
    width: @nestedGridWidth;
    margin: @nestedGridMargin;
}

/*******************************
            States
*******************************/
& when (@variationPopupLoading) {
    .ui.loading.popup {
        cursor: default;
        pointer-events: @loadingPointerEvents;
        user-select: none;
        & * {
            opacity: @loadingOpacity;
        }
        &::after {
            position: absolute;
            content: "";
            top: 50%;
            left: 50%;
            margin: @loaderMargin;
            width: @loaderSize;
            height: @loaderSize;
            animation: loader @loaderSpeed infinite linear;
            border: @loaderLineWidth solid @loaderLineColor;
            border-radius: @circularRadius;
            box-shadow: 0 0 0 1px transparent;
            visibility: visible;
            z-index: @loaderLineZIndex;
        }
    }
}

.ui.invisible.popup {
    display: block;
    visibility: hidden !important;
    z-index: @invisibleZIndex;
}

.ui.animating.popup,
.ui.visible.popup {
    display: block;
}

.ui.visible.popup {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/*******************************
            Variations
*******************************/

& when (@variationPopupBasic) {
    /* --------------
         Basic
    --------------- */

    .ui.basic.popup::before {
        display: none;
    }
}

& when (@variationPopupFixed) {
    .ui.fixed.popup {
        width: @maxWidth;
    }
}

& when (@variationPopupWide) {
    /* --------------
         Wide
    --------------- */

    .ui.wide.popup {
        max-width: @wideWidth;
        &.fixed when (@variationPopupFixed) {
            width: @wideWidth;
        }
    }
    .ui[class*="very wide"].popup {
        max-width: @veryWideWidth;
        &.fixed when (@variationPopupFixed) {
            width: @veryWideWidth;
        }
    }

    @media only screen and (max-width: @largestMobileScreen) {
        .ui.wide.popup,
        .ui[class*="very wide"].popup {
            max-width: @maxWidth;
            &.fixed when (@variationPopupFixed) {
                width: @maxWidth;
            }
        }
    }
}

& when (@variationPopupFluid) {
    /* --------------
         Fluid
    --------------- */

    .ui.fluid.popup {
        width: 100%;
        max-width: none;
    }
}

& when (@variationPopupInverted) {
    /* --------------
         Colors
    --------------- */

    /* Inverted colors */
    .ui.inverted.popup {
        background: @invertedBackground;
        color: @invertedColor;
        border: @invertedBorder;
        box-shadow: @invertedBoxShadow;
    }
    .ui.inverted.popup .header {
        background: @invertedHeaderBackground;
        color: @invertedHeaderColor;
    }
    .ui.inverted.popup::before {
        background-color: @invertedArrowColor;
        box-shadow: none;
    }
}

& when not (@variationPopupColors = false) {
    each(@variationPopupColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @l: @colors[@@color][light];
        @cl: if(iscolor(@c), lighten(@c, @loadingColoredPercent), @c);
        @ll: if(iscolor(@l), darken(@l, @loadingColoredPercent), @l);

        & when not (@variationPopupTooltip) {
            .ui.ui.ui.@{color}.popup::before,
            .ui.@{color}.popup {
                background: @c;
                color: @white;
                border: none;
                .loading& when (@variationPopupLoading) {
                    background: @cl;
                }
            }
            .ui.ui.ui.@{color}.popup::before {
                box-shadow: none;
            }
            & when (@variationPopupInverted) {
                .ui.inverted.@{color}.popup::before,
                .ui.inverted.@{color}.popup {
                    background: @l;
                    color: @black;
                    .loading& when (@variationPopupLoading) {
                        background: @ll;
                    }
                }
            }
        }
        & when (@variationPopupTooltip) {
            .ui.ui.ui.@{color}.popup::before,
            .ui.@{color}.popup,
            [data-tooltip][data-variation~="@{color}"]::after,
            [data-tooltip][data-variation~="@{color}"]::before {
                background: @c;
                color: @white;
                border: none;
            }
            & when (@variationPopupLoading) {
                .ui.ui.ui.@{color}.loading.popup::before,
                .ui.@{color}.loading.popup {
                    background: @cl;
                }
            }
            .ui.ui.ui.@{color}.popup::before,
            [data-tooltip][data-variation~="@{color}"]::before {
                box-shadow: none;
            }
            & when (@variationPopupInverted) {
                .ui.ui.ui.inverted.@{color}.popup::before,
                .ui.inverted.@{color}.popup,
                [data-tooltip][data-inverted][data-variation~="@{color}"]::after,
                [data-tooltip][data-inverted][data-variation~="@{color}"]::before {
                    background: @l;
                    color: @black;
                }
                & when (@variationPopupLoading) {
                    .ui.ui.ui.inverted.@{color}.loading.popup::before,
                    .ui.inverted.@{color}.loading.popup {
                        background: @ll;
                    }
                }
            }
        }
    });
}

& when (@variationPopupFlowing) {
    /* --------------
         Flowing
    --------------- */

    .ui.flowing.popup {
        max-width: none;
    }
}

/* --------------
     Sizes
--------------- */

.ui.popup {
    font-size: @medium;
}
& when not (@variationPopupSizes = false) {
    each(@variationPopupSizes, {
        @s: @@value;
        .ui.@{value}.popup {
            font-size: @s;
        }
        & when (@variationPopupTooltip) {
            [data-tooltip][data-variation~="@{value}"]::before,
            [data-tooltip][data-variation~="@{value}"]::after {
                font-size: @s;
            }
        }
    });
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
