/*!
 * # Fomantic-UI - Flyout
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "flyout";

@import (multiple) "../../theme.config";

/*******************************
             Flyout
*******************************/

/* Flyout Menu */
.ui.flyout {
    position: fixed;
    top: 0;
    left: 0;
    backface-visibility: hidden;
    transition: none;
    will-change: transform;
    transform: translate3d(0, 0, 0);
    visibility: hidden;
    height: 100%;
    max-height: 100%;
    max-width: 100%;
    border-radius: 0;
    margin: 0;
    overflow-y: hidden;
    z-index: @topLayer;
    background: #fff;
    outline: none;
}

/* GPU Layers for Child Elements */
.ui.flyout > * {
    backface-visibility: hidden;
}

/* --------------
   Close
--------------- */
.ui.flyout > .close {
    cursor: pointer;
    position: absolute;
    top: @closeTop;
    right: @closeRight;
    z-index: 1;
    opacity: @closeOpacity;
    font-size: @closeSize;
    color: @closeColor;
    width: @closeHitbox;
    height: @closeHitbox;
    padding: @closePadding;
    margin: @closeMargin;
    text-align: right;
}

.ui.flyout > .close:focus,
.ui.flyout > .close:hover {
    opacity: 1;
    outline: none;
}

/* --------------
   Header
--------------- */
.ui.flyout > .header {
    display: block;
    background: @headerBackground;
    margin: @headerMargin;
    padding: @headerPadding;
    box-shadow: @headerBoxShadow;
    color: @headerColor;
    border-bottom: @headerBorder;
}

/* --------------
   Content
--------------- */
.ui.flyout > .content {
    display: block;
    width: 100%;
    font-size: @contentFontSize;
    line-height: @contentLineHeight;
    padding: @contentPadding;
    background: @contentBackground;
    &.scrolling when(@variationFlyoutScrolling) {
        overflow: auto;
        overscroll-behavior: @overscrollBehavior;
    }
}
.ui.flyout.left > .content,
.ui.flyout.right > .content {
    min-height: @contentMinHeightWithoutHeader;
}
.ui.flyout.left > .header + .content,
.ui.flyout.right > .header + .content {
    min-height: @contentMinHeight;
}
& when(@variationFlyoutScrolling) {
    .ui.flyout.left > .scrolling.content,
    .ui.flyout.right > .scrolling.content {
        max-height: @scrollingContentMaxHeightWithoutHeader;
    }
    .ui.flyout.left > .header + .scrolling.content,
    .ui.flyout.right > .header + .scrolling.content {
        max-height: @scrollingContentMaxHeight;
    }

    .ui.flyout.top > .scrolling.content,
    .ui.flyout.bottom > .scrolling.content {
        max-height: @scrollingContentMaxHeightTopBottom;
    }
}

& when (@variationFlyoutActions) {
    /* --------------
       Actions
    --------------- */
    .ui.flyout > .actions {
        background: @actionBackground;
        padding: @actionPadding;
        border-top: @actionBorder;
        text-align: @actionAlign;
    }
    .ui.flyout .actions > .button:not(.fluid) {
        margin-left: @buttonDistance;
    }
    & when (@variationFlyoutBasic) {
        .ui.ui.flyout > .basic.actions {
            border-top: none;
        }
    }
    & when (@variationFlyoutLeftActions) {
        .ui.flyout > .left.actions {
            text-align: left;
            & > .button:not(.fluid) {
                margin-left: @buttonLeftDistance;
                margin-right: @buttonLeftDistance;
            }
        }
    }
}
& when (@variationFlyoutCentered) {
    .ui.flyout > .centered,
    .ui.flyout > .center.aligned {
        text-align: center;
        &.actions > .button:not(.fluid) when (@variationFlyoutActions) {
            margin-left: @buttonCenteredDistance;
            margin-right: @buttonCenteredDistance;
        }
    }
}
& when (@variationFlyoutBasic) {
    .ui.ui.flyout > .basic.header,
    .ui.ui.flyout > .basic.actions {
        background-color: transparent;
    }
    .ui.flyout > .basic.header {
        border-bottom: none;
    }
}

/* --------------
   Direction
--------------- */
& when (@variationFlyoutLeft) {
    .ui.left.flyout {
        right: auto;
        left: 0;
        transform: translate3d(-100%, 0, 0);
    }
}
& when (@variationFlyoutRight) {
    .ui.right.flyout {
        right: 0;
        left: auto;
        transform: translate3d(100%, 0, 0);
    }
}

& when (@variationFlyoutTop) or (@variationFlyoutBottom) {
    .ui.top.flyout,
    .ui.bottom.flyout {
        width: 100%;
        height: auto;
    }
    & when (@variationFlyoutTop) {
        .ui.top.flyout {
            top: 0;
            bottom: auto;
            transform: translate3d(0, -100%, 0);
        }
    }
    & when (@variationFlyoutBottom) {
        .ui.bottom.flyout {
            top: auto;
            bottom: 0;
            transform: translate3d(0, 100%, 0);
        }
    }
}

/* --------------
     Pushable
--------------- */

.pushable.pushable.pushable {
    height: 100%;
    overflow-x: hidden;
    padding: 0;
    &.locked {
        overflow-y: hidden;
        background: inherit;
    }
}

/* Whole Page */
body.pushable {
    background: @canvasBackground;
    &.dimmed {
        background: inherit;
    }
}

/* Page Context */
.pushable:not(body) {
    transform: translate3d(0, 0, 0);
    overflow-y: hidden;
}
.pushable:not(body) > .ui.flyout,
.pushable:not(body) > .fixed,
.pushable:not(body) > .pusher::after {
    position: absolute;
}

/* --------------
     Fixed
--------------- */

.pushable > .fixed {
    position: fixed;
    backface-visibility: hidden;
    transition: transform @duration @easing;
    will-change: transform;
    z-index: @fixedLayer;
}

/* --------------
     Page
--------------- */

.pushable > .pusher {
    position: relative;
    backface-visibility: hidden;
    min-height: 100%;
    transition: transform @duration @easing;
    z-index: @middleLayer;

    /* Pusher should inherit background from context */
    background: inherit;
    &:not(.overflowing) {
        overflow: hidden;
    }
}

body.pushable > .pusher {
    background: @pageBackground;
}

/* --------------
     Dimmer
--------------- */

.pushable > .pusher::after {
    position: fixed;
    top: 0;
    right: 0;
    content: "";
    background: @dimmerColor;
    overflow: hidden;
    opacity: 0;
    transition: @dimmerTransition;
    will-change: opacity;
    z-index: @dimmerLayer;
}

/* --------------
    Coupling
--------------- */

.ui.flyout.menu .item {
    border-radius: 0 !important;
}

/*******************************
            States
*******************************/

/* --------------
     Dimmed
--------------- */

.pushable > .pusher.dimmed::after {
    width: 100% !important;
    height: 100% !important;
    opacity: 1 !important;
}
& when (@variationFlyoutBlurring) {
    .pushable > .pusher.dimmed.blurring:not(.closing)::after {
        background: @blurredBackgroundColor;
        -webkit-backdrop-filter: @blurredEndFilter;
        backdrop-filter: @blurredEndFilter;
    }
}
.pushable > .pusher.closing.dimmed::after {
    opacity: 0 !important;
}

/* --------------
    Animating
--------------- */

.ui.animating.flyout {
    visibility: visible;
}

/* --------------
     Visible
--------------- */

.ui.visible.flyout {
    visibility: visible;
    transform: translate3d(0, 0, 0);
}

/* Shadow Direction */
& when (@variationFlyoutLeft) or (@variationFlyoutRight) {
    .ui.left.visible.flyout,
    .ui.right.visible.flyout {
        box-shadow: @horizontalBoxShadow;
    }
}
& when (@variationFlyoutTop) or (@variationFlyoutBottom) {
    .ui.top.visible.flyout,
    .ui.bottom.visible.flyout {
        box-shadow: @verticalBoxShadow;
    }
}

/* Visible On Load */
& when (@variationFlyoutLeft) {
    .ui.visible.left.flyout ~ .fixed,
    .ui.visible.left.flyout ~ .pusher {
        transform: translate3d(@width, 0, 0);
    }
}
& when (@variationFlyoutRight) {
    .ui.visible.right.flyout ~ .fixed,
    .ui.visible.right.flyout ~ .pusher {
        transform: translate3d(-@width, 0, 0);
    }
}
& when (@variationFlyoutTop) {
    .ui.visible.top.flyout ~ .fixed,
    .ui.visible.top.flyout ~ .pusher {
        transform: translate3d(0, @height, 0);
    }
}
& when (@variationFlyoutBottom) {
    .ui.visible.bottom.flyout ~ .fixed,
    .ui.visible.bottom.flyout ~ .pusher {
        transform: translate3d(0, -@height, 0);
    }
}

& when (@variationFlyoutLeft) or (@variationFlyoutRight) {
    /* opposite sides visible forces content overlay */
    .ui.visible.left.flyout ~ .ui.visible.right.flyout ~ .fixed,
    .ui.visible.left.flyout ~ .ui.visible.right.flyout ~ .pusher,
    .ui.visible.right.flyout ~ .ui.visible.left.flyout ~ .fixed,
    .ui.visible.right.flyout ~ .ui.visible.left.flyout ~ .pusher {
        transform: translate3d(0, 0, 0);
    }
}

& when (@variationFlyoutInverted) {
    /* --------------
         Inverted
    --------------- */
    .ui.flyout.inverted {
        background: @invertedBackground;
    }

    .ui.flyout.inverted > .close {
        color: @invertedCloseColor;
    }

    .ui.flyout.inverted > .header,
    .ui.flyout.inverted > .content {
        background: @invertedBackground;
        color: @invertedHeaderColor;
    }
    .ui.flyout.inverted > .actions {
        background: @invertedActionBackground;
        border-top: @invertedActionBorder;
        color: @invertedActionColor;
    }
}

/*******************************
          Variations
*******************************/

/* --------------
     Width
--------------- */

/* Left / Right */
& when (@variationFlyoutLeft) or (@variationFlyoutRight) {
    .ui.left.flyout,
    .ui.right.flyout {
        width: @width;
    }
    & when (@variationFlyoutThin) {
        .ui.thin.left.flyout,
        .ui.thin.right.flyout {
            width: @thinWidth;
        }

        .ui[class*="very thin"].left.flyout,
        .ui[class*="very thin"].right.flyout {
            width: @veryThinWidth;
        }
    }

    & when (@variationFlyoutWide) {
        .ui.wide.left.flyout,
        .ui.wide.right.flyout {
            width: @wideWidth;
        }

        .ui[class*="very wide"].left.flyout,
        .ui[class*="very wide"].right.flyout {
            width: @veryWideWidth;
        }
    }

    /* Left Visible */
    & when (@variationFlyoutLeft) {
        & when (@variationFlyoutThin) {
            .ui.visible.thin.left.flyout ~ .fixed,
            .ui.visible.thin.left.flyout ~ .pusher {
                transform: translate3d(@thinWidth, 0, 0);
            }

            .ui.visible[class*="very thin"].left.flyout ~ .fixed,
            .ui.visible[class*="very thin"].left.flyout ~ .pusher {
                transform: translate3d(@veryThinWidth, 0, 0);
            }
        }

        & when (@variationFlyoutWide) {
            .ui.visible.wide.left.flyout ~ .fixed,
            .ui.visible.wide.left.flyout ~ .pusher {
                transform: translate3d(@wideWidth, 0, 0);
            }

            .ui.visible[class*="very wide"].left.flyout ~ .fixed,
            .ui.visible[class*="very wide"].left.flyout ~ .pusher {
                transform: translate3d(@veryWideWidth, 0, 0);
            }
        }
    }

    /* Right Visible */
    & when (@variationFlyoutRight) {
        & when (@variationFlyoutThin) {
            .ui.visible.thin.right.flyout ~ .fixed,
            .ui.visible.thin.right.flyout ~ .pusher {
                transform: translate3d(-@thinWidth, 0, 0);
            }

            .ui.visible[class*="very thin"].right.flyout ~ .fixed,
            .ui.visible[class*="very thin"].right.flyout ~ .pusher {
                transform: translate3d(-@veryThinWidth, 0, 0);
            }
        }

        & when (@variationFlyoutWide) {
            .ui.visible.wide.right.flyout ~ .fixed,
            .ui.visible.wide.right.flyout ~ .pusher {
                transform: translate3d(-@wideWidth, 0, 0);
            }

            .ui.visible[class*="very wide"].right.flyout ~ .fixed,
            .ui.visible[class*="very wide"].right.flyout ~ .pusher {
                transform: translate3d(-@veryWideWidth, 0, 0);
            }
        }
    }

    & when (@variationFlyoutFullscreen) {
        /* Fullscreen */
        .ui.fullscreen.flyout {
            width: 100%;
        }
    }

    /* -------------------
      Column Width
    -------------------- */

    & when (@variationFlyoutColumnWidth) {
        /* Sizing Combinations */

        .ui[class*="one wide"].flyout:not(.fullscreen) {
            width: @oneWide;
        }

        .ui[class*="two wide"].flyout:not(.fullscreen) {
            width: @twoWide;
        }

        .ui[class*="three wide"].flyout:not(.fullscreen) {
            width: @threeWide;
        }

        .ui[class*="four wide"].flyout:not(.fullscreen) {
            width: @fourWide;
        }

        .ui[class*="five wide"].flyout:not(.fullscreen) {
            width: @fiveWide;
        }

        .ui[class*="six wide"].flyout:not(.fullscreen) {
            width: @sixWide;
        }

        .ui[class*="seven wide"].flyout:not(.fullscreen) {
            width: @sevenWide;
        }

        .ui[class*="eight wide"].flyout:not(.fullscreen) {
            width: @eightWide;
        }

        .ui[class*="nine wide"].flyout:not(.fullscreen) {
            width: @nineWide;
        }

        .ui[class*="ten wide"].flyout:not(.fullscreen) {
            width: @tenWide;
        }

        .ui[class*="eleven wide"].flyout:not(.fullscreen) {
            width: @elevenWide;
        }

        .ui[class*="twelve wide"].flyout:not(.fullscreen) {
            width: @twelveWide;
        }

        .ui[class*="thirteen wide"].flyout:not(.fullscreen) {
            width: @thirteenWide;
        }

        .ui[class*="fourteen wide"].flyout:not(.fullscreen) {
            width: @fourteenWide;
        }

        .ui[class*="fifteen wide"].flyout:not(.fullscreen) {
            width: @fifteenWide;
        }

        .ui[class*="sixteen wide"].flyout:not(.fullscreen) {
            width: @sixteenWide;
        }
    }
}

/*******************************
          Animations
*******************************/

& when (@variationFlyoutOverlay) {
    /* --------------
        Overlay
    --------------- */

    /* Set-up */
    .ui.overlay.flyout {
        z-index: @topLayer;
    }

    /* Initial */
    & when (@variationFlyoutLeft) {
        .ui.left.overlay.flyout {
            transform: translate3d(-100%, 0, 0);
        }
    }
    & when (@variationFlyoutRight) {
        .ui.right.overlay.flyout {
            transform: translate3d(100%, 0, 0);
        }
    }
    & when (@variationFlyoutTop) {
        .ui.top.overlay.flyout {
            transform: translate3d(0, -100%, 0);
        }
    }
    & when (@variationFlyoutBottom) {
        .ui.bottom.overlay.flyout {
            transform: translate3d(0, 100%, 0);
        }
    }

    /* Animation */
    .animating.ui.overlay.flyout,
    .ui.visible.overlay.flyout {
        transition: transform @duration @easing;
    }

    /* End - flyout */
    .ui.visible.overlay.flyout {
        transform: translate3d(0, 0, 0);
    }

    /* End - Pusher */
    .ui.visible.overlay.flyout ~ .fixed,
    .ui.visible.overlay.flyout ~ .pusher {
        transform: none !important;
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
