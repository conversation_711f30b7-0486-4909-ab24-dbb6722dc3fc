/*******************************
           Accordion
*******************************/

@boxShadow: none;

/* Title */
@titleFont: @headerFont;
@titlePadding: 0.5em 0;
@titleFontSize: 1em;
@titleColor: @textColor;
@titleLineHeight: 1;

/* Icon */
@iconOpacity: 1;
@iconFontSize: 1em;
@iconFloat: none;
@iconWidth: 1.25em;
@iconHeight: 1em;
@iconDisplay: inline-block;
@iconMargin: 0 0.25rem 0 0;
@iconPadding: 0;
@iconTransition:
    transform @defaultDuration @defaultEasing,
    opacity @defaultDuration @defaultEasing;
@iconVerticalAlign: baseline;
@iconTransform: none;
@iconTransformRight: @menuIconTransform;

/* Child Accordion */
@childAccordionMargin: 1em 0 0;
@childAccordionPadding: 0;

/* Content */
@contentMargin: "";
@contentPadding: 0.5em 0 1em;

/* -------------------
       Coupling
-------------------- */

@menuTitlePadding: 0;
@menuIconFloat: right;
@menuIconMargin: @iconMargin;
@menuIconTransform: rotate(180deg);

/* -------------------
       States
-------------------- */

@activeIconTransform: rotate(90deg);

/* -------------------
      Variations
-------------------- */

/* Styled */
@styledWidth: 600px;
@styledBackground: @white;
@styledBorderRadius: @defaultBorderRadius;
@styledBoxShadow:
    @subtleShadow,
    0 0 0 1px @borderColor;

/* Content */
@styledContentMargin: 0;
@styledContentPadding: 0.5em 1em 1.5em;

/* Child Content */
@styledChildContentMargin: 0;
@styledChildContentPadding: @styledContentPadding;

/* Styled Title */
@styledTitleMargin: 0;
@styledTitlePadding: 0.75em 1em;
@styledTitleFontWeight: @bold;
@styledTitleColor: @unselectedTextColor;
@styledTitleTransition: background-color @defaultDuration @defaultEasing;
@styledTitleBorder: 1px solid @borderColor;
@styledTitleTransition:
    background @defaultDuration @defaultEasing,
    color @defaultDuration @defaultEasing;

/* Styled Title States */
@styledTitleHoverBackground: transparent;
@styledTitleHoverColor: @textColor;
@styledActiveTitleBackground: transparent;
@styledActiveTitleColor: @selectedTextColor;

/* Styled Child Title States */
@styledHoverChildTitleBackground: @styledTitleHoverBackground;
@styledHoverChildTitleColor: @styledTitleHoverColor;
@styledActiveChildTitleBackground: @styledActiveTitleBackground;
@styledActiveChildTitleColor: @styledActiveTitleColor;

/* Inverted */
@invertedTitleColor: @invertedTextColor;
@invertedStyledTitleColor: @invertedUnselectedTextColor;
@invertedStyledBackground: @black;
@invertedStyledTitleBorder: 1px solid @whiteBorderColor;
@invertedStyledBoxShadow:
    @subtleShadow,
    0 0 0 1px @whiteBorderColor;
@invertedStyledTitleHoverBackground: transparent;
@invertedStyledTitleHoverColor: @invertedTextColor;
@invertedStyledActiveTitleBackground: transparent;
@invertedStyledActiveTitleColor: @invertedSelectedTextColor;

@invertedStyledHoverChildTitleBackground: @invertedStyledTitleHoverBackground;
@invertedStyledHoverChildTitleColor: @invertedStyledTitleHoverColor;
@invertedStyledActiveChildTitleBackground: @invertedStyledActiveTitleBackground;
@invertedStyledActiveChildTitleColor: @invertedStyledActiveTitleColor;

/* Compact */
@titlePaddingCompact: 0.25em 0;
@contentPaddingCompact: 0.25em 0 0.5em;

/* Very Compact */
@titlePaddingVeryCompact: 0.125em 0;
@contentPaddingVeryCompact: 0.125em 0 0.25em;

/* Styled Compact */
@styledTitlePaddingCompact: 0.375em 0.5em;
@styledContentPaddingCompact: 0.25em 0.5em 0.75em;

/* Styled Very Compact */
@styledTitlePaddingVeryCompact: 0.1875em 0.25em;
@styledContentPaddingVeryCompact: 0.125em 0.25em 0.375em;

/* Basic Styled */
@basicStyledTitleColor: @mutedTextColor;
@basicStyledTitleHoverColor: @textColor;
@basicStyledActiveTitleColor: @selectedTextColor;

@invertedBasicStyledTitleColor: @invertedMutedTextColor;
@invertedBasicStyledTitleHoverColor: @invertedTextColor;
@invertedBasicStyledActiveTitleColor: @invertedSelectedTextColor;

/* Tree */
@treeContentPadding: 0;
@treeContentTopMargin: 0;
@treeContentLeftMargin: 1.7em;
